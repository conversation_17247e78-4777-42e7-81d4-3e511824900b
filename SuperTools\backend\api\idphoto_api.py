#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
证件照制作API
通过代理方式调用HivisionIDPhotos服务
"""

import os
import logging
import requests
import base64
from io import BytesIO
from PIL import Image
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from backend.utils.permissions import check_rate_limit, record_user_action

# 创建蓝图
idphoto_api = Blueprint('idphoto_api', __name__)

# 配置日志
logger = logging.getLogger("supertools.idphoto")

# HivisionIDPhotos服务配置
HIVISION_API_BASE = os.environ.get('HIVISION_API_BASE', 'http://127.0.0.1:8080')

# 证件照尺寸配置
ID_PHOTO_SIZES = {
    '1inch': {'width': 295, 'height': 413, 'name': '一寸'},
    '2inch': {'width': 413, 'height': 626, 'name': '二寸'},
    'small1inch': {'width': 260, 'height': 378, 'name': '小一寸'},
    'small2inch': {'width': 413, 'height': 531, 'name': '小二寸'},
    'big1inch': {'width': 390, 'height': 567, 'name': '大一寸'},
    'big2inch': {'width': 413, 'height': 626, 'name': '大二寸'},
    '5inch': {'width': 1050, 'height': 1499, 'name': '五寸'},
    'teacher_cert': {'width': 295, 'height': 413, 'name': '教师资格证'},
    'civil_servant': {'width': 295, 'height': 413, 'name': '国家公务员考试'},
    'cpa_exam': {'width': 295, 'height': 413, 'name': '初级会计考试'},
    'cet4_exam': {'width': 144, 'height': 192, 'name': '英语四六级考试'},
    'computer_exam': {'width': 390, 'height': 567, 'name': '计算机等级考试'},
    'graduate_exam': {'width': 531, 'height': 709, 'name': '研究生考试'},
    'social_card': {'width': 358, 'height': 441, 'name': '社保卡'},
    'driver_license': {'width': 260, 'height': 378, 'name': '电子驾驶证'},
    'us_visa': {'width': 600, 'height': 600, 'name': '美国签证'},
    'japan_visa': {'width': 295, 'height': 413, 'name': '日本签证'},
    'korea_visa': {'width': 413, 'height': 531, 'name': '韩国签证'},
}

# 背景颜色配置
BACKGROUND_COLORS = {
    'white': 'ffffff',
    'blue': '4285f4',
    'red': 'ea4335',
    'black': '000000',
    'darkblue': '1e3a8a',
    'gray': '6b7280',
    'gradient': '667eea',  # 渐变色使用主色调
}


@idphoto_api.route('/create', methods=['POST'])
@login_required
def create_idphoto():
    """
    创建证件照
    """
    try:
        # 检查用户权限
        can_download, remaining, error_msg = check_rate_limit(current_user, 'download')
        if not can_download:
            return jsonify({
                'success': False,
                'message': error_msg or '今日使用次数已达上限',
                'data': None
            }), 429

        # 获取上传的文件
        if 'photo' not in request.files:
            return jsonify({
                'success': False,
                'message': '请上传照片文件',
                'data': None
            }), 400

        photo_file = request.files['photo']
        if photo_file.filename == '':
            return jsonify({
                'success': False,
                'message': '请选择照片文件',
                'data': None
            }), 400

        # 重置文件指针到开始位置
        photo_file.seek(0)

        # 获取参数
        size_key = request.form.get('size', '1inch')
        background = request.form.get('background', 'white')
        custom_color = request.form.get('custom_color', '')

        # 验证尺寸
        if size_key not in ID_PHOTO_SIZES:
            return jsonify({
                'success': False,
                'message': '不支持的证件照尺寸',
                'data': None
            }), 400

        size_config = ID_PHOTO_SIZES[size_key]

        # 处理背景颜色
        if background in ['custom_rgb', 'custom_hex'] and custom_color:
            bg_color = custom_color.lstrip('#')
        else:
            bg_color = BACKGROUND_COLORS.get(background, '4285f4')

        # 第一步：调用HivisionIDPhotos生成透明背景证件照
        logger.info(f"开始为用户 {current_user.username} 制作证件照")

        # 准备请求数据 - 注意：必须使用数字类型，不是字符串
        files = {'input_image': (photo_file.filename, photo_file.stream, photo_file.content_type)}
        data = {
            'height': size_config['height'],
            'width': size_config['width'],
            'human_matting_model': 'modnet_photographic_portrait_matting',
            'face_detect_model': 'mtcnn',
            'hd': True,
            'dpi': 300,
            'face_align': True  # 修正参数名：face_alignment -> face_align
        }

        # 调用HivisionIDPhotos API
        logger.info(f"调用HivisionIDPhotos API: {HIVISION_API_BASE}/idphoto")
        logger.info(f"请求参数: {data}")

        response = requests.post(f"{HIVISION_API_BASE}/idphoto", files=files, data=data, timeout=60)

        logger.info(f"HivisionIDPhotos响应状态: {response.status_code}")

        if response.status_code != 200:
            logger.error(f"HivisionIDPhotos API调用失败: {response.status_code}, 响应: {response.text}")
            return jsonify({
                'success': False,
                'message': '证件照生成失败，请稍后重试',
                'data': None
            }), 500

        result = response.json()
        logger.info(f"HivisionIDPhotos响应结果: status={result.get('status')}")

        if not result.get('status'):
            logger.error(f"HivisionIDPhotos返回失败状态: {result}")
            return jsonify({
                'success': False,
                'message': '未检测到人脸或照片质量不佳，请重新上传',
                'data': None
            }), 400

        # 第二步：添加背景色
        transparent_image_base64 = result.get('image_base64_standard')
        if not transparent_image_base64:
            return jsonify({
                'success': False,
                'message': '证件照生成失败',
                'data': None
            }), 500

        # 调用添加背景色API - 使用base64方式传输透明图
        bg_data = {
            'input_image_base64': transparent_image_base64,
            'color': bg_color,
            'dpi': 300
        }

        bg_response = requests.post(f"{HIVISION_API_BASE}/add_background", data=bg_data, timeout=60)
        
        if bg_response.status_code != 200:
            logger.error(f"添加背景色失败: {bg_response.status_code}")
            return jsonify({
                'success': False,
                'message': '添加背景色失败',
                'data': None
            }), 500

        bg_result = bg_response.json()
        if not bg_result.get('status'):
            return jsonify({
                'success': False,
                'message': '添加背景色失败',
                'data': None
            }), 500

        # 获取最终的证件照
        final_image_base64 = bg_result.get('image_base64')
        logger.info(f"最终图片base64前50字符: {final_image_base64[:50] if final_image_base64 else 'None'}")

        # 记录使用统计
        record_user_action(current_user, 'download')

        logger.info(f"用户 {current_user.username} 证件照制作成功")
        logger.info(f"单张照片长度: {len(final_image_base64) if final_image_base64 else 0}")

        response_data = {
            'success': True,
            'message': '证件照制作成功',
            'data': {
                'single_photo': final_image_base64,
                'size_info': f"{size_config['name']} ({size_config['width']}x{size_config['height']})",
                'background_color': bg_color
            }
        }

        logger.info(f"准备返回响应: success={response_data['success']}")
        return jsonify(response_data)

    except requests.exceptions.Timeout:
        logger.error("HivisionIDPhotos服务超时")
        return jsonify({
            'success': False,
            'message': '服务响应超时，请稍后重试',
            'data': None
        }), 504

    except requests.exceptions.ConnectionError:
        logger.error("无法连接到HivisionIDPhotos服务")
        return jsonify({
            'success': False,
            'message': '证件照服务暂时不可用，请稍后重试',
            'data': None
        }), 503

    except Exception as e:
        logger.error(f"证件照制作失败: {e}")
        return jsonify({
            'success': False,
            'message': '证件照制作失败，请稍后重试',
            'data': None
        }), 500


@idphoto_api.route('/sizes', methods=['GET'])
def get_sizes():
    """
    获取支持的证件照尺寸
    """
    try:
        sizes = []
        for key, config in ID_PHOTO_SIZES.items():
            sizes.append({
                'key': key,
                'name': config['name'],
                'width': config['width'],
                'height': config['height'],
                'display': f"{config['name']} ({config['width']}x{config['height']})"
            })

        return jsonify({
            'success': True,
            'message': '获取尺寸列表成功',
            'data': sizes
        })

    except Exception as e:
        logger.error(f"获取尺寸列表失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取尺寸列表失败',
            'data': None
        }), 500


@idphoto_api.route('/status', methods=['GET'])
def check_status():
    """
    检查证件照服务状态
    """
    try:
        # 检查HivisionIDPhotos服务是否可用
        response = requests.get(f"{HIVISION_API_BASE}/docs", timeout=5)
        
        if response.status_code == 200:
            return jsonify({
                'success': True,
                'message': '证件照服务正常',
                'data': {
                    'hivision_status': 'online'
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': '证件照服务异常',
                'data': {
                    'hivision_status': 'offline'
                }
            }), 503

    except Exception as e:
        logger.error(f"检查服务状态失败: {e}")
        return jsonify({
            'success': False,
            'message': '证件照服务不可用',
            'data': {
                'hivision_status': 'offline'
            }
        }), 503
