/**
 * 直接的认证事件处理
 * 这个文件提供了直接绑定到登录和注册按钮的事件处理器
 * 用于解决可能的事件绑定问题
 */

document.addEventListener('DOMContentLoaded', function() {
    // 直接为登录和注册按钮添加点击事件
    setupAuthButtons();
});

function setupAuthButtons() {
    // 获取按钮元素
    const loginBtn = document.getElementById('login-btn');
    const registerBtn = document.getElementById('register-btn');

    // 为登录按钮添加点击事件
    if (loginBtn) {
        loginBtn.onclick = function(e) {
            e.preventDefault();
            showLoginModal();
        };
    }

    // 为注册按钮添加点击事件
    if (registerBtn) {
        registerBtn.onclick = function(e) {
            e.preventDefault();
            showRegisterModal();
        };
    }
    
    // 为所有关闭按钮添加点击事件
    document.querySelectorAll('.close-modal, .modal-overlay').forEach(el => {
        el.onclick = function(e) {
            if (e.target === el) {
                hideAllModals();
            }
        };
    });
    
    // 为切换按钮添加点击事件
    const switchToRegister = document.getElementById('switch-to-register');
    if (switchToRegister) {
        switchToRegister.onclick = function(e) {
            e.preventDefault();
            hideLoginModal();
            showRegisterModal();
        };
    }
    
    const switchToLogin = document.getElementById('switch-to-login');
    if (switchToLogin) {
        switchToLogin.onclick = function(e) {
            e.preventDefault();
            hideRegisterModal();
            showLoginModal();
        };
    }
}

// 显示登录模态框
function showLoginModal() {
    const modal = document.getElementById('login-modal');
    if (modal) {
        modal.classList.add('show');
        document.body.classList.add('modal-open');
    }
}

// 隐藏登录模态框
function hideLoginModal() {
    const modal = document.getElementById('login-modal');
    if (modal) {
        modal.classList.remove('show');
        document.body.classList.remove('modal-open');
    }
}

// 显示注册模态框
function showRegisterModal() {
    const modal = document.getElementById('register-modal');
    if (modal) {
        modal.classList.add('show');
        document.body.classList.add('modal-open');
    }
}

// 隐藏注册模态框
function hideRegisterModal() {
    const modal = document.getElementById('register-modal');
    if (modal) {
        modal.classList.remove('show');
        document.body.classList.remove('modal-open');
    }
}

// 隐藏所有模态框
function hideAllModals() {
    document.querySelectorAll('.modal').forEach(modal => {
        modal.classList.remove('show');
    });
    document.body.classList.remove('modal-open');
}
