<template>
  <view class="container">
    <view class="header">
      <text class="title">修改密码</text>
    </view>
    
    <view class="form-section">
      <view class="form-item">
        <text class="label">旧密码</text>
        <input 
          class="input" 
          type="password"
          v-model="formData.oldPassword" 
          placeholder="请输入当前密码"
          maxlength="20"
        />
      </view>
      
      <view class="form-item">
        <text class="label">新密码</text>
        <input 
          class="input" 
          type="password"
          v-model="formData.newPassword" 
          placeholder="请输入新密码（6-20位，包含字母和数字）"
          maxlength="20"
        />
      </view>
      
      <view class="form-item">
        <text class="label">确认密码</text>
        <input 
          class="input" 
          type="password"
          v-model="formData.confirmPassword" 
          placeholder="请再次输入新密码"
          maxlength="20"
        />
      </view>
    </view>
    

    
    <view class="actions">
      <button class="cancel-btn" @click="goBack">取消</button>
      <button class="save-btn" @click="changePassword" :disabled="!isFormValid">确认修改</button>
    </view>
  </view>
</template>

<script>
import { UserManager } from '../../utils/user.js'

export default {
  name: 'ChangePassword',
  
  data() {
    return {
      formData: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    }
  },
  
  computed: {
    // 表单验证
    isFormValid() {
      return this.formData.oldPassword.length > 0 &&
             this.formData.newPassword.length >= 6 &&
             this.formData.confirmPassword.length >= 6 &&
             this.formData.newPassword === this.formData.confirmPassword &&
             this.isPasswordValid(this.formData.newPassword)
    }
  },
  
  onLoad() {
    // 检查登录状态
    if (!UserManager.isLoggedIn()) {
      uni.showModal({
        title: '需要登录',
        content: '请先登录以修改密码',
        showCancel: false,
        success: () => {
          uni.navigateTo({
            url: '/pages/login/login'
          })
        }
      })
      return
    }
  },
  
  methods: {
    // 验证密码格式
    isPasswordValid(password) {
      // 密码验证：必须包含数字，可以包含字母和特殊符号
      const hasNumber = /\d/.test(password)
      const validChars = /^[A-Za-z\d!@#$%^&*()_+\-=\[\]{}|;:,.<>?]*$/.test(password)
      const validLength = password.length >= 6 && password.length <= 20

      return hasNumber && validChars && validLength
    },
    
    // 修改密码
    async changePassword() {
      try {
        // 表单验证
        if (!this.formData.oldPassword) {
          uni.showToast({
            title: '请输入旧密码',
            icon: 'none'
          })
          return
        }
        
        if (!this.formData.newPassword) {
          uni.showToast({
            title: '请输入新密码',
            icon: 'none'
          })
          return
        }
        
        if (this.formData.newPassword !== this.formData.confirmPassword) {
          uni.showToast({
            title: '两次密码输入不一致',
            icon: 'none'
          })
          return
        }
        
        if (!this.isPasswordValid(this.formData.newPassword)) {
          uni.showToast({
            title: '密码必须包含数字，长度6-20位',
            icon: 'none'
          })
          return
        }
        
        if (this.formData.oldPassword === this.formData.newPassword) {
          uni.showToast({
            title: '新密码不能与旧密码相同',
            icon: 'none'
          })
          return
        }
        
        uni.showLoading({
          title: '修改中...'
        })
        
        // 调用后端API修改密码
        const { changeUserPassword } = await import('../../api/user.js')
        const currentUserId = UserManager.getCurrentUserId()
        
        const passwordData = {
          oldPassword: this.formData.oldPassword,
          newPassword: this.formData.newPassword,
          confirmPassword: this.formData.confirmPassword
        }
        
        await changeUserPassword(currentUserId, passwordData)
        
        uni.hideLoading()
        uni.showModal({
          title: '修改成功',
          content: '密码修改成功，请重新登录',
          showCancel: false,
          success: () => {
            // 清除登录状态
            UserManager.logout()
            // 跳转到登录页面
            uni.reLaunch({
              url: '/pages/login/login'
            })
          }
        })
        
      } catch (error) {
        uni.hideLoading()
        console.error('修改密码失败:', error)
        uni.showToast({
          title: error.message || '修改失败',
          icon: 'none'
        })
      }
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  background: linear-gradient(135deg, #2196F3, #21CBF3);
  padding: 40rpx 30rpx;
  text-align: center;
  
  .title {
    color: white;
    font-size: 36rpx;
    font-weight: bold;
  }
}

.form-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  
  .form-item {
    display: flex;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .label {
      width: 150rpx;
      font-size: 28rpx;
      color: #333;
    }
    
    .input {
      flex: 1;
      height: 60rpx;
      padding: 0 20rpx;
      border: 1rpx solid #e0e0e0;
      border-radius: 8rpx;
      font-size: 28rpx;
    }
  }
}



.actions {
  padding: 40rpx 30rpx;
  display: flex;
  gap: 20rpx;
  
  .cancel-btn {
    flex: 1;
    height: 88rpx;
    background: #f5f5f5;
    color: #666;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
  }
  
  .save-btn {
    flex: 1;
    height: 88rpx;
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    
    &:disabled {
      background: #ccc;
      color: #999;
    }
  }
}
</style>
