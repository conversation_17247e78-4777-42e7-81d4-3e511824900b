-- 应用版本管理表
CREATE TABLE IF NOT EXISTS `app_versions` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '版本ID',
    `version_name` varchar(50) NOT NULL COMMENT '版本名称，如1.0.0',
    `version_code` int(11) NOT NULL COMMENT '版本号，用于比较大小',
    `platform` varchar(20) NOT NULL DEFAULT 'android' COMMENT '平台类型：android, ios, h5',
    `update_content` text COMMENT '更新内容描述',
    `download_url` varchar(500) COMMENT '下载链接',
    `file_size` varchar(20) COMMENT '文件大小，如10.5MB',
    `is_force_update` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否强制更新：0-否，1-是',
    `min_support_version` varchar(50) COMMENT '最低支持版本',
    `status` varchar(20) NOT NULL DEFAULT 'draft' COMMENT '状态：draft-草稿，published-已发布，deleted-已删除',
    `release_date` datetime COMMENT '发布时间',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_version_platform` (`version_name`, `platform`),
    KEY `idx_platform_status` (`platform`, `status`),
    KEY `idx_version_code` (`version_code`),
    KEY `idx_release_date` (`release_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用版本管理表';

-- 插入初始版本数据
INSERT INTO `app_versions` (
    `version_name`, 
    `version_code`, 
    `platform`, 
    `update_content`, 
    `download_url`, 
    `file_size`, 
    `is_force_update`, 
    `min_support_version`, 
    `status`, 
    `release_date`
) VALUES 
(
    '1.0.0', 
    100, 
    'android', 
    '🎉 情感回复助手首次发布！\n\n✨ 核心功能：\n• 智能情感分析，准确识别消息情感\n• 多种回复风格：温暖关怀、幽默风趣、高情商等\n• 一键复制回复内容\n• 历史记录管理\n\n🔧 技术特性：\n• 基于先进AI技术\n• 支持多平台使用\n• 简洁易用的界面设计\n\n📱 使用说明：\n1. 输入收到的消息内容\n2. 选择合适的回复风格\n3. 获取智能生成的回复建议\n4. 一键复制使用', 
    'https://example.com/download/emotional-reply-1.0.0.apk', 
    '15.2MB', 
    0, 
    '1.0.0', 
    'published', 
    NOW()
),
(
    '1.0.0', 
    100, 
    'ios', 
    '🎉 情感回复助手首次发布！\n\n✨ 核心功能：\n• 智能情感分析，准确识别消息情感\n• 多种回复风格：温暖关怀、幽默风趣、高情商等\n• 一键复制回复内容\n• 历史记录管理\n\n🔧 技术特性：\n• 基于先进AI技术\n• 支持多平台使用\n• 简洁易用的界面设计\n\n📱 使用说明：\n1. 输入收到的消息内容\n2. 选择合适的回复风格\n3. 获取智能生成的回复建议\n4. 一键复制使用', 
    'https://apps.apple.com/app/emotional-reply', 
    '18.5MB', 
    0, 
    '1.0.0', 
    'published', 
    NOW()
),
(
    '1.0.0', 
    100, 
    'h5', 
    '🎉 情感回复助手网页版首次发布！\n\n✨ 核心功能：\n• 智能情感分析，准确识别消息情感\n• 多种回复风格：温暖关怀、幽默风趣、高情商等\n• 一键复制回复内容\n• 历史记录管理\n\n🔧 技术特性：\n• 基于先进AI技术\n• 响应式设计，支持各种设备\n• 无需下载，即开即用\n\n📱 使用说明：\n1. 输入收到的消息内容\n2. 选择合适的回复风格\n3. 获取智能生成的回复建议\n4. 一键复制使用', 
    'https://emotional-reply.com', 
    '在线使用', 
    0, 
    '1.0.0', 
    'published', 
    NOW()
);

-- 创建版本更新日志表（可选）
CREATE TABLE IF NOT EXISTS `version_update_logs` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `user_id` bigint(20) COMMENT '用户ID',
    `device_id` varchar(100) COMMENT '设备ID',
    `current_version` varchar(50) COMMENT '当前版本',
    `target_version` varchar(50) COMMENT '目标版本',
    `platform` varchar(20) COMMENT '平台类型',
    `action` varchar(50) COMMENT '操作类型：check-检查更新，download-下载，install-安装，skip-跳过',
    `result` varchar(20) COMMENT '结果：success-成功，failed-失败，cancelled-取消',
    `error_message` text COMMENT '错误信息',
    `user_agent` varchar(500) COMMENT '用户代理',
    `ip_address` varchar(50) COMMENT 'IP地址',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_device_id` (`device_id`),
    KEY `idx_platform` (`platform`),
    KEY `idx_action` (`action`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本更新日志表';
