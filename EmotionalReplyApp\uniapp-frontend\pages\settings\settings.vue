<template>
  <view class="container themed" :class="themeClass">
    <!-- 用户信息 -->
    <view class="user-section">
      <view class="user-info">
        <image :src="avatarSrc" class="avatar" @error="onAvatarError" mode="aspectFit" />
        <view class="user-details">
          <text class="nickname">{{ userInfo.nickname }}</text>
          <text class="user-type">{{ getUserRoleText() }}</text>
        </view>
      </view>
      <view class="user-stats">
        <view class="stat-item">
          <text class="stat-number">{{ userInfo.todayUsage }}</text>
          <text class="stat-label">今日使用</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ userInfo.totalUsage }}</text>
          <text class="stat-label">总计使用</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ formatQuota(userInfo.remainingQuota) }}</text>
          <text class="stat-label">剩余配额</text>
        </view>
      </view>
    </view>
    
    <!-- 设置选项 -->
    <view class="settings-section">
      <text class="section-title">应用设置</text>
      
      <view class="setting-item" @click="toggleFloatingBubble">
        <text class="setting-label">悬浮气泡</text>
        <switch :checked="settings.showFloatingBubble" @change="onFloatingBubbleChange" />
      </view>

      <view class="setting-item" @click="showReplyStyleSettings">
        <text class="setting-label">回复风格设置</text>
        <text class="setting-value" v-if="replySettingsLoaded">{{ replyModeText }}</text>
        <text class="setting-value loading" v-else>加载中...</text>
      </view>
      
      <view class="setting-item" @click="selectTheme">
        <text class="setting-label">主题模式</text>
        <text class="setting-value">{{ getThemeName(settings.theme) }}</text>
      </view>
    </view>
    


    <!-- 账户管理 -->
    <view class="account-section">
      <text class="section-title">账户管理</text>
      
      <!-- 管理员显示生成激活码 -->
      <view class="setting-item" @click="generateActivationCode" v-if="userInfo.isAdmin">
        <text class="setting-label">生成激活码</text>
        <text class="setting-value premium">管理功能</text>
      </view>

      <!-- 管理员显示激活码管理 -->
      <view class="setting-item" @click="manageActivationCodes" v-if="userInfo.isAdmin">
        <text class="setting-label">激活码管理</text>
        <text class="setting-value premium">管理功能</text>
      </view>

      <!-- 普通用户显示升级VIP -->
      <view class="setting-item" @click="upgradeVip" v-if="!userInfo.isVip && !userInfo.isAdmin">
        <text class="setting-label">升级VIP</text>
        <text class="setting-value premium">立即升级</text>
      </view>

      <!-- VIP用户显示续费选项 -->
      <view class="setting-item" @click="renewVip" v-if="userInfo.isVip && !userInfo.isAdmin">
        <text class="setting-label">VIP续费</text>
        <text class="setting-value premium">续费延期</text>
      </view>
      
      <view class="setting-item" @click="viewProfile">
        <text class="setting-label">个人资料</text>
        <text class="setting-value">></text>
      </view>
      
      <view class="setting-item" @click="changePassword">
        <text class="setting-label">修改密码</text>
        <text class="setting-value">></text>
      </view>
    </view>
    
    <!-- 其他设置 -->
    <view class="other-section">
      <text class="section-title">其他</text>
      
      <view class="setting-item" @click="clearCache" @longpress="showAdvancedClearOptions">
        <text class="setting-label">清除缓存</text>
        <text class="setting-value">{{ cacheSize }}</text>
      </view>
      
      <view class="setting-item" @click="checkUpdate">
        <text class="setting-label">检查更新</text>
        <text class="setting-value">v1.0.0</text>
      </view>
      
      <view class="setting-item" @click="showAbout">
        <text class="setting-label">关于我们</text>
        <text class="setting-value">></text>
      </view>
      
      <view class="setting-item" @click="feedback">
        <text class="setting-label">意见反馈</text>
        <text class="setting-value">></text>
      </view>

      <!-- 开发调试选项 -->
      <view class="setting-item" @click="testFloatingBubble" v-if="isDevelopment">
        <text class="setting-label">🎈 悬浮气泡测试</text>
        <text class="setting-value">调试工具</text>
      </view>
    </view>
    
    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" @click="logout">
        <text>退出登录</text>
      </button>
    </view>
  </view>
</template>

<script>
import { getReplyStyles } from '../../api/emotion.js'
import { getUserStats } from '../../api/history.js'
import { UserManager } from '../../utils/user.js'
import { AuthGuard } from '../../utils/auth-guard.js'
import themeManager from '../../utils/theme.js'
import { CacheManager } from '../../utils/storage.js'
import { get, post } from '../../utils/request.js'

export default {
  name: 'SettingsPage',
  
  data() {
    return {
      userInfo: UserManager.getUserInfo() || {
        nickname: '未登录',
        avatar: 'static/images/default-avatar.png',
        isVip: false,
        isAdmin: false,
        todayUsage: 0,
        totalUsage: 0,
        remainingQuota: 0
      },
      settings: {
        showFloatingBubble: true,
        defaultStyle: 'romantic',
        theme: 'auto'
      },
      replySettings: {
        generationMode: 'smart', // smart, custom, single
        preferredCount: 2,
        primaryStyle: 'romantic',
        selectedStyles: ['romantic', 'humorous', 'high_eq']
      },
      replySettingsLoaded: false, // 回复设置是否已加载
      cacheSize: '计算中...',
      cacheStats: null, // 缓存统计信息
      cacheRefreshTimer: null, // 缓存刷新定时器
      replyStyles: {}, // 从API获取的回复风格
      themeClass: 'theme-auto' // 主题类名
    }
  },
  
  computed: {
    // 是否已登录
    isLoggedIn() {
      return UserManager.isLoggedIn()
    },

    // 头像路径处理 - 始终使用默认头像
    avatarSrc() {
      return '/static/images/default-avatar.png'
    },

    // 获取回复模式文本 - 计算属性确保响应式更新
    replyModeText() {
      const modeMap = {
        'smart': '智能选择',
        'custom': '自定义风格',
        'single': '单一风格'
      }
      return modeMap[this.replySettings.generationMode] || '智能选择'
    },

    // 是否为开发环境（显示调试选项）
    isDevelopment() {
      // #ifdef APP-PLUS
      return true // App环境下显示调试选项
      // #endif

      // #ifdef H5
      return window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
      // #endif

      // #ifdef MP-WEIXIN
      return false // 小程序环境不显示调试选项
      // #endif

      return false
    }
  },

  onLoad() {
    // 路由守卫检查
    if (!AuthGuard.pageGuard('pages/settings/settings')) {
      return
    }

    this.loadUserInfo()
    this.loadSettings()
    this.loadReplyStyles()
    this.loadReplySettings()
    this.syncThemeSettings()
    this.syncFloatingBubbleState()
    this.calculateCacheSize()
  },

  onShow() {
    // 每次页面显示时刷新用户信息，确保VIP状态是最新的
    this.loadUserInfo()
    // 刷新回复设置，确保显示最新的模式选择
    this.loadReplySettings()
    // 重新计算缓存大小
    this.calculateCacheSize()
  },

  onHide() {
    // 页面隐藏时清除定时器
    if (this.cacheRefreshTimer) {
      clearInterval(this.cacheRefreshTimer)
      this.cacheRefreshTimer = null
    }
  },

  onUnload() {
    // 页面卸载时清除定时器
    if (this.cacheRefreshTimer) {
      clearInterval(this.cacheRefreshTimer)
      this.cacheRefreshTimer = null
    }
  },
  
  methods: {
    // 加载用户信息
    async loadUserInfo() {
      try {
        const currentUserId = UserManager.getCurrentUserId()

        // 从服务器获取最新的用户信息
        const { getUserInfo } = await import('../../api/user.js')
        const serverUserInfo = await getUserInfo(currentUserId)

        // 更新本地存储的用户信息
        if (serverUserInfo) {
          UserManager.setUserInfo(serverUserInfo)
          this.userInfo = {
            ...serverUserInfo,
            avatar: '/static/images/default-avatar.png' // 始终使用默认头像
          }
        }

        // 获取用户配额信息
        const { checkQuota } = await import('../../api/user.js')
        const quotaInfo = await checkQuota(currentUserId)
        this.userInfo = {
          ...this.userInfo,
          todayUsage: quotaInfo.dailyUsage || 0,
          totalUsage: quotaInfo.totalUsage || 0,
          remainingQuota: quotaInfo.remainingQuota !== undefined ? quotaInfo.remainingQuota : 3
        }

      } catch (error) {
        // 如果服务器获取失败，使用本地数据
        this.userInfo = UserManager.getUserInfo() || UserManager.getDefaultUserInfo()

        // 仍然尝试获取配额信息
        try {
          const currentUserId = UserManager.getCurrentUserId()
          const { checkQuota } = await import('../../api/user.js')
          const quotaInfo = await checkQuota(currentUserId)
          this.userInfo = {
            ...this.userInfo,
            todayUsage: quotaInfo.dailyUsage || 0,
            totalUsage: quotaInfo.totalUsage || 0,
            remainingQuota: quotaInfo.remainingQuota !== undefined ? quotaInfo.remainingQuota : 3
          }
        } catch (quotaError) {
          // 配额信息获取失败，使用默认值
        }
      }
    },
    
    // 加载设置
    loadSettings() {
      const savedSettings = uni.getStorageSync('settings')
      if (savedSettings) {
        this.settings = { ...this.settings, ...savedSettings }
      }
    },

    // 加载回复风格
    async loadReplyStyles() {
      try {
        this.replyStyles = await getReplyStyles()
      } catch (error) {
        // 使用默认风格
        this.replyStyles = {
          warm_caring: '暖男',
          humorous: '玩梗',
          romantic: '撩妹',
          high_eq: '高情商',
          direct: '直接',
          mature: '成熟稳重',
          gentle: '温柔大叔',
          dominant: '霸道总裁',
          literary: '文艺风格',
          detailed: '话痨风格'
        }
      }
    },
    
    // 头像加载错误处理
    onAvatarError(e) {
      // 始终使用默认头像
      this.userInfo.avatar = '/static/images/default-avatar.png'
    },


    
    // 悬浮气泡开关
    onFloatingBubbleChange(e) {
      this.settings.showFloatingBubble = e.detail.value
      this.saveSettings()

      // 实际控制悬浮气泡显示/隐藏
      this.controlFloatingBubble(e.detail.value)
    },
    

    
    // 选择主题
    selectTheme() {
      const themes = [
        { value: 'auto', label: '跟随系统' },
        { value: 'light', label: '浅色模式' },
        { value: 'dark', label: '深色模式' }
      ]

      uni.showActionSheet({
        itemList: themes.map(t => t.label),
        success: (res) => {
          const selectedTheme = themes[res.tapIndex].value
          this.settings.theme = selectedTheme
          this.saveSettings()

          // 应用主题
          themeManager.setTheme(selectedTheme)

          // 更新当前页面的主题类
          this.themeClass = `theme-${selectedTheme}`

          // 显示切换成功提示
          uni.showToast({
            title: `已切换到${themes[res.tapIndex].label}`,
            icon: 'success'
          })
        }
      })
    },
    
    // 获取用户角色文本
    getUserRoleText() {
      if (!this.userInfo) {
        return '未登录'
      }

      // 调试信息（可选）
      // console.log('用户信息调试:', {
      //   isAdmin: this.userInfo.isAdmin,
      //   isVip: this.userInfo.isVip,
      //   userInfo: this.userInfo
      // })

      // 按优先级检查：管理员 > VIP用户 > 普通用户
      if (this.userInfo.isAdmin === 1 || this.userInfo.isAdmin === true) {
        return '管理员'
      } else if (this.userInfo.isVip === 1 || this.userInfo.isVip === true) {
        return 'VIP用户'
      } else {
        return '普通用户'
      }
    },

    // 生成激活码（管理员功能）
    generateActivationCode() {
      if (!this.userInfo.isAdmin) {
        uni.showToast({
          title: '权限不足',
          icon: 'error'
        })
        return
      }

      // 显示激活码类型选择
      uni.showActionSheet({
        itemList: [
          '1天VIP激活码',
          '7天VIP激活码',
          '30天VIP激活码',
          '90天VIP激活码',
          '180天VIP激活码',
          '365天VIP激活码'
        ],
        success: (res) => {
          const codeTypes = [
            { type: 'premium_1d', days: 1, name: '1天VIP' },
            { type: 'premium_7d', days: 7, name: '7天VIP' },
            { type: 'premium_1m', days: 30, name: '30天VIP' },
            { type: 'premium_3m', days: 90, name: '90天VIP' },
            { type: 'premium_6m', days: 180, name: '180天VIP' },
            { type: 'premium_1y', days: 365, name: '365天VIP' }
          ]

          const selectedType = codeTypes[res.tapIndex]
          this.doGenerateActivationCode(selectedType)
        }
      })
    },

    // 激活码管理（管理员功能）
    manageActivationCodes() {

      if (!this.userInfo.isAdmin) {
        uni.showToast({
          title: '权限不足',
          icon: 'error'
        })
        return
      }


      uni.navigateTo({
        url: '/pages/admin/activation-codes',
        fail: (err) => {
          console.error('跳转失败:', err)
          uni.showToast({
            title: '页面跳转失败',
            icon: 'error'
          })
        }
      })
    },

    // 执行生成激活码
    async doGenerateActivationCode(codeTypeInfo) {
      try {
        uni.showLoading({
          title: '生成中...'
        })

        // 调用后端API生成激活码
        const { generateActivationCode } = await import('../../api/activation.js')
        const response = await generateActivationCode(
          codeTypeInfo.type,
          codeTypeInfo.days,
          this.userInfo.id,
          `管理员${this.userInfo.nickname}生成的${codeTypeInfo.name}激活码`
        )

        uni.hideLoading()


        // 前端request工具已经处理了响应，直接使用response作为激活码对象
        if (response && response.code) {
          // 显示生成的激活码
          uni.showModal({
            title: '激活码生成成功',
            content: `激活码: ${response.code}\n类型: ${codeTypeInfo.name}\n有效期: ${codeTypeInfo.days}天\n激活码有效期: 30天\n\n请妥善保管此激活码`,
            showCancel: true,
            cancelText: '关闭',
            confirmText: '复制',
            success: (res) => {
              if (res.confirm) {
                // 复制到剪贴板
                uni.setClipboardData({
                  data: response.code,
                  success: () => {
                    uni.showToast({
                      title: '已复制到剪贴板',
                      icon: 'success'
                    })
                  }
                })
              }
            }
          })
        } else {
          throw new Error('生成激活码失败')
        }

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '生成失败',
          icon: 'error'
        })
        console.error('生成激活码失败:', error)
      }
    },

    // 生成随机激活码
    generateRandomCode() {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
      let result = ''

      // 生成格式：XXXX-XXXX-XXXX-XXXX
      for (let i = 0; i < 4; i++) {
        if (i > 0) result += '-'
        for (let j = 0; j < 4; j++) {
          result += chars.charAt(Math.floor(Math.random() * chars.length))
        }
      }

      return result
    },

    // 升级VIP
    upgradeVip() {
      uni.showActionSheet({
        itemList: [
          '激活码兑换',
          '购买VIP套餐'
        ],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 激活码兑换
            uni.navigateTo({
              url: '/pages/activation/activation'
            })
          } else if (res.tapIndex === 1) {
            // 购买VIP套餐
            uni.navigateTo({
              url: '/pages/vip/upgrade'
            })
          }
        }
      })
    },

    // VIP续费
    renewVip() {
      uni.showActionSheet({
        itemList: [
          '激活码续费',
          '购买续费套餐'
        ],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 激活码续费
            uni.navigateTo({
              url: '/pages/activation/activation'
            })
          } else if (res.tapIndex === 1) {
            // 购买续费套餐
            uni.navigateTo({
              url: '/pages/vip/upgrade'
            })
          }
        }
      })
    },
    
    // 查看个人资料
    viewProfile() {
      if (!this.isLoggedIn) {
        uni.showModal({
          title: '需要登录',
          content: '请先登录以查看个人资料',
          showCancel: false,
          success: () => {
            uni.navigateTo({
              url: '/pages/login/login'
            })
          }
        })
        return
      }
      uni.navigateTo({
        url: '/pages/user/profile'
      })
    },

    // 修改密码
    changePassword() {
      if (!this.isLoggedIn) {
        uni.showModal({
          title: '需要登录',
          content: '请先登录以修改密码',
          showCancel: false,
          success: () => {
            uni.navigateTo({
              url: '/pages/login/login'
            })
          }
        })
        return
      }
      uni.navigateTo({
        url: '/pages/user/change-password'
      })
    },
    
    // 修改密码
    changePassword() {
      uni.navigateTo({
        url: '/pages/user/change-password'
      })
    },
    
    // 计算缓存大小
    async calculateCacheSize() {
      try {
        this.cacheSize = '计算中...'
        const cacheStats = await CacheManager.getCacheStats()
        this.cacheStats = cacheStats
        this.cacheSize = cacheStats.totalSize
      } catch (error) {
        console.error('计算缓存大小失败:', error)
        this.cacheSize = '未知'
      }
    },

    // 清除缓存
    clearCache() {
      // 显示缓存详情和清除选项
      const cacheDetails = this.cacheStats ?
        `总大小: ${this.cacheStats.totalSize}\n项目数: ${this.cacheStats.itemCount}\n\n包含:\n${Object.entries(this.cacheStats.details).map(([key, value]) => `• ${this.getCacheTypeName(key)}: ${value}`).join('\n')}` :
        '确定要清除所有缓存数据吗？'

      uni.showModal({
        title: '清除缓存',
        content: cacheDetails + '\n\n清除后将释放存储空间，但可能需要重新加载某些数据。',
        confirmText: '清除',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.performCacheClear()
          }
        }
      })
    },

    // 执行缓存清除
    async performCacheClear() {
      try {
        uni.showLoading({
          title: '清除中...',
          mask: true
        })

        // 清除缓存，保留用户数据和设置
        const result = await CacheManager.clearCache({
          clearUserData: false,  // 保留用户数据
          clearSettings: false,  // 保留设置
          clearHistory: true,    // 清除历史记录缓存
          clearTempData: true,   // 清除临时数据
          clearImageCache: true, // 清除图片缓存
          clearApiCache: true    // 清除API缓存
        })

        uni.hideLoading()

        if (result.success) {
          // 重新计算缓存大小
          await this.calculateCacheSize()

          const freedSpace = CacheManager.formatSize(result.freedSpace)
          const message = `缓存清除成功！\n释放空间: ${freedSpace}\n清除项目: ${result.clearedItems.length}个`

          uni.showModal({
            title: '清除完成',
            content: message,
            showCancel: false,
            confirmText: '确定'
          })
        } else {
          throw new Error(result.errors.join(', '))
        }

      } catch (error) {
        uni.hideLoading()
        console.error('清除缓存失败:', error)

        uni.showModal({
          title: '清除失败',
          content: `清除缓存时发生错误: ${error.message}`,
          showCancel: false,
          confirmText: '确定'
        })
      }
    },

    // 获取缓存类型显示名称
    getCacheTypeName(key) {
      const nameMap = {
        localStorage: '本地存储',
        imageCache: '图片缓存',
        tempFiles: '临时文件',
        apiCache: 'API缓存'
      }
      return nameMap[key] || key
    },

    // 显示高级清除选项（长按触发）
    showAdvancedClearOptions() {
      const options = [
        '清除历史记录缓存',
        '清除图片缓存',
        '清除临时文件',
        '清除API缓存',
        '清除所有缓存（保留用户数据）',
        '完全清除（包含用户数据）'
      ]

      uni.showActionSheet({
        itemList: options,
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.clearSpecificCache({ clearHistory: true })
              break
            case 1:
              this.clearSpecificCache({ clearImageCache: true })
              break
            case 2:
              this.clearSpecificCache({ clearTempData: true })
              break
            case 3:
              this.clearSpecificCache({ clearApiCache: true })
              break
            case 4:
              this.clearSpecificCache({
                clearHistory: true,
                clearTempData: true,
                clearImageCache: true,
                clearApiCache: true
              })
              break
            case 5:
              this.showCompleteCleanWarning()
              break
          }
        }
      })
    },

    // 清除特定类型的缓存
    async clearSpecificCache(options) {
      try {
        uni.showLoading({
          title: '清除中...',
          mask: true
        })

        const result = await CacheManager.clearCache(options)
        uni.hideLoading()

        if (result.success) {
          await this.calculateCacheSize()
          const freedSpace = CacheManager.formatSize(result.freedSpace)

          uni.showToast({
            title: `已清除，释放${freedSpace}`,
            icon: 'success',
            duration: 2000
          })
        } else {
          throw new Error(result.errors.join(', '))
        }

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '清除失败',
          icon: 'error'
        })
      }
    },

    // 显示完全清除警告
    showCompleteCleanWarning() {
      uni.showModal({
        title: '⚠️ 危险操作',
        content: '此操作将清除所有数据，包括用户信息、设置和历史记录。清除后需要重新登录。\n\n确定要继续吗？',
        confirmText: '确定清除',
        confirmColor: '#ff4757',
        success: (res) => {
          if (res.confirm) {
            this.performCompleteClean()
          }
        }
      })
    },

    // 执行完全清除
    async performCompleteClean() {
      try {
        uni.showLoading({
          title: '完全清除中...',
          mask: true
        })

        // 清除所有缓存，包括用户数据
        const result = await CacheManager.clearCache({
          clearUserData: true,
          clearSettings: true,
          clearHistory: true,
          clearTempData: true,
          clearImageCache: true,
          clearApiCache: true
        })

        uni.hideLoading()

        if (result.success) {
          uni.showModal({
            title: '清除完成',
            content: '所有数据已清除，应用将重新启动',
            showCancel: false,
            success: () => {
              // 重启应用
              uni.reLaunch({
                url: '/pages/login/login'
              })
            }
          })
        } else {
          throw new Error(result.errors.join(', '))
        }

      } catch (error) {
        uni.hideLoading()
        uni.showModal({
          title: '清除失败',
          content: `清除过程中发生错误: ${error.message}`,
          showCancel: false
        })
      }
    },
    
    // 检查更新
    async checkUpdate() {
      uni.showLoading({
        title: '检查中...'
      })

      try {
        // 获取当前版本信息
        const systemInfo = uni.getSystemInfoSync()
        let platform = 'h5'
        if (systemInfo.platform === 'android') {
          platform = 'android'
        } else if (systemInfo.platform === 'ios') {
          platform = 'ios'
        }

        const currentVersionInfo = {
          versionName: '1.0.0', // 当前版本
          platform: platform,
          systemInfo: systemInfo
        }

        // 记录检查更新行为
        await this.logUpdateAction({
          action: 'check',
          currentVersion: currentVersionInfo.versionName,
          platform: currentVersionInfo.platform,
          deviceInfo: currentVersionInfo.systemInfo
        })

        // 调用检查更新API
        const updateInfo = await this.callCheckUpdateAPI(currentVersionInfo.versionName, currentVersionInfo.platform)

        uni.hideLoading()

        if (updateInfo && typeof updateInfo === 'object') {
          if (updateInfo.hasUpdate) {
            // 有新版本，显示更新对话框
            this.showUpdateDialog(updateInfo)
          } else {
            // 已是最新版本
            uni.showToast({
              title: updateInfo.message || '已是最新版本',
              icon: 'success'
            })
          }
        } else {
          throw new Error('检查更新失败：响应数据格式错误')
        }

      } catch (error) {
        uni.hideLoading()
        console.error('检查更新失败:', error)
        uni.showToast({
          title: '检查更新失败',
          icon: 'none'
        })
      }
    },

    // 调用检查更新API
    async callCheckUpdateAPI(currentVersion, platform) {
      try {
        // 使用统一的请求工具
        const response = await get('/version/check', {
          currentVersion: currentVersion,
          platform: platform
        })

        return response
      } catch (error) {
        console.error('API调用失败:', error)
        throw error
      }
    },

    // 记录用户更新行为
    async logUpdateAction(logData) {
      try {
        await post('/version/log', logData)
      } catch (error) {
        console.error('记录用户行为失败:', error)
        // 记录失败不影响主流程，只打印错误日志
      }
    },

    // 显示更新对话框
    showUpdateDialog(updateInfo) {
      const versionInfo = updateInfo.versionInfo || {}
      const isForceUpdate = versionInfo.isForceUpdate || false

      let content = `发现新版本 ${updateInfo.latestVersion}\n\n`

      if (versionInfo.updateContent) {
        content += versionInfo.updateContent
      }

      if (versionInfo.fileSize) {
        content += `\n\n文件大小：${versionInfo.fileSize}`
      }

      uni.showModal({
        title: isForceUpdate ? '强制更新' : '版本更新',
        content: content,
        showCancel: !isForceUpdate,
        confirmText: '立即更新',
        cancelText: '稍后更新',
        success: async (res) => {
          if (res.confirm) {
            // 用户选择立即更新
            await this.handleUpdate(updateInfo)
          } else if (res.cancel && !isForceUpdate) {
            // 用户选择稍后更新（仅非强制更新时）
            await this.logUpdateAction({
              action: 'skip',
              currentVersion: updateInfo.currentVersion,
              targetVersion: updateInfo.latestVersion,
              platform: versionInfo.platform
            })
          }
        }
      })
    },

    // 处理更新
    async handleUpdate(updateInfo) {
      const versionInfo = updateInfo.versionInfo || {}

      try {
        // 记录更新行为
        await this.logUpdateAction({
          action: 'download',
          currentVersion: updateInfo.currentVersion,
          targetVersion: updateInfo.latestVersion,
          platform: versionInfo.platform
        })

        if (versionInfo.downloadUrl) {
          // 有下载链接，打开下载页面
          // #ifdef APP-PLUS
          plus.runtime.openURL(versionInfo.downloadUrl)
          // #endif

          // #ifdef H5
          window.open(versionInfo.downloadUrl, '_blank')
          // #endif

          uni.showToast({
            title: '正在跳转下载页面',
            icon: 'success'
          })
        } else {
          // 没有下载链接，提示用户
          uni.showModal({
            title: '更新提示',
            content: '请前往应用商店搜索"情感回复助手"进行更新',
            showCancel: false,
            confirmText: '知道了'
          })
        }

      } catch (error) {
        console.error('处理更新失败:', error)
        uni.showToast({
          title: '更新失败',
          icon: 'none'
        })
      }
    },
    
    // 关于我们
    showAbout() {
      uni.navigateTo({
        url: '/pages/about/about'
      })
    },
    
    // 意见反馈
    feedback() {
      uni.navigateTo({
        url: '/pages/feedback/feedback'
      })
    },
    
    // 退出登录
    logout() {
      if (!this.isLoggedIn) {
        uni.navigateTo({
          url: '/pages/login/login'
        })
        return
      }

      uni.showModal({
        title: '退出登录',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 使用路由守卫的退出登录方法
            AuthGuard.logoutRedirect()

            // 清除用户数据
            UserManager.clearUserInfo()

            uni.showToast({
              title: '已退出登录',
              icon: 'success'
            })
          }
        }
      })
    },

    // 显示登录提示
    showLoginRequired() {
      uni.showModal({
        title: '需要登录',
        content: '请先登录以使用此功能',
        showCancel: false,
        success: () => {
          uni.navigateTo({
            url: '/pages/login/login'
          })
        }
      })
    },
    
    // 保存设置
    saveSettings() {
      uni.setStorageSync('settings', this.settings)
    },
    
    // 获取风格名称
    getStyleName(style) {
      return this.replyStyles[style] || style
    },

    // 显示回复风格设置
    showReplyStyleSettings() {
      // 直接跳转到风格选择页面
      uni.navigateTo({
        url: '/pages/reply-styles/reply-styles'
      })
    },





    // 保存回复设置
    async saveReplySettings() {
      try {
        const userId = UserManager.getCurrentUserId()
        if (!userId) {
          uni.showToast({ title: '请先登录', icon: 'none' })
          return
        }

        // 调用后端API保存设置
        const { updateReplyPreferences } = await import('../../api/user.js')
        await updateReplyPreferences(userId, {
          generationMode: this.replySettings.generationMode,
          preferredCount: this.replySettings.preferredCount,
          primaryStyle: this.replySettings.primaryStyle,
          replyStyles: JSON.stringify(this.replySettings.selectedStyles)
        })

        // 保存到本地
        uni.setStorageSync('replySettings', this.replySettings)

        uni.showToast({ title: '设置已保存', icon: 'success' })
      } catch (error) {
        console.error('保存回复设置失败:', error)
        uni.showToast({ title: '保存失败', icon: 'none' })
      }
    },

    // 加载回复设置
    async loadReplySettings() {
      try {
        this.replySettingsLoaded = false
        const userId = UserManager.getCurrentUserId()

        if (!userId) {
          this.replySettingsLoaded = true
          return
        }

        // 从后端获取设置
        const { getUserReplyPreferences } = await import('../../api/user.js')
        const preferences = await getUserReplyPreferences(userId)

        if (preferences && typeof preferences === 'object') {
         
          // 使用 Vue.set 确保响应式更新
          this.$set(this, 'replySettings', {
            generationMode: preferences.generationMode || 'smart',
            preferredCount: preferences.preferredCount || 2,
            primaryStyle: preferences.primaryStyle || 'romantic',
            selectedStyles: Array.isArray(preferences.replyStyles) ? preferences.replyStyles : JSON.parse(preferences.replyStyles || '["romantic", "humorous", "high_eq"]')
          })

          // 强制更新视图
          this.$forceUpdate()
        } else {
          // 返回数据为空或格式不正确，使用默认设置
        }
      } catch (error) {
        // 从本地存储获取
        const localSettings = uni.getStorageSync('replySettings')
        if (localSettings) {
          this.replySettings = { ...this.replySettings, ...localSettings }
        }
      } finally {
        this.replySettingsLoaded = true
      }
    },

    // 获取主题名称
    getThemeName(theme) {
      return themeManager.getThemeDisplayName(theme)
    },

    // 格式化配额显示
    formatQuota(quota) {
      if (quota === -1) {
        return '∞'
      }
      return quota || 0
    },

    // 同步主题设置
    syncThemeSettings() {
      try {
        // 从主题管理器获取当前主题
        const currentTheme = themeManager.getCurrentTheme()

        // 同步到设置中
        if (this.settings.theme !== currentTheme) {
          this.settings.theme = currentTheme
          this.saveSettings()
        }

        // 更新主题类名
        this.themeClass = `theme-${currentTheme}`


      } catch (error) {
        console.error('同步主题设置失败:', error)
      }
    },

    // 控制悬浮气泡显示/隐藏
    controlFloatingBubble(show) {
      // #ifdef APP-PLUS
      try {
        const plugin = uni.requireNativePlugin('floating-window-plugin')

        if (show) {
          // 先检查权限
          plugin.hasOverlayPermission((result) => {

            if (result.hasPermission) {
              // 有权限，显示悬浮气泡
              plugin.showFloatingBubble({
                x: 100,
                y: 200,
                size: 56
              }, (result) => {
                if (result.success) {
                  uni.showToast({
                    title: '悬浮气泡已开启',
                    icon: 'success'
                  })
                } else {
                  uni.showToast({
                    title: '悬浮气泡开启失败',
                    icon: 'error'
                  })
                  // 开启失败，重置开关状态
                  this.settings.showFloatingBubble = false
                  this.saveSettings()
                }
              })
            } else {
              // 没有权限，申请权限
              plugin.requestOverlayPermission((result) => {
                if (result.granted) {
                  // 权限申请成功，重新尝试显示
                  this.controlFloatingBubble(true)
                } else {
                  uni.showModal({
                    title: '需要悬浮窗权限',
                    content: '请在系统设置中开启悬浮窗权限，以使用悬浮气泡功能',
                    showCancel: false
                  })
                  // 权限被拒绝，重置开关状态
                  this.settings.showFloatingBubble = false
                  this.saveSettings()
                }
              })
            }
          })
        } else {
          // 隐藏悬浮气泡
          plugin.hideFloatingBubble((result) => {
            if (result.success) {
              uni.showToast({
                title: '悬浮气泡已关闭',
                icon: 'success'
              })
            }
          })
        }
      } catch (error) {
        console.error('悬浮气泡控制失败:', error)
        uni.showToast({
          title: '悬浮气泡功能不可用',
          icon: 'error'
        })
        // 功能不可用，重置开关状态
        this.settings.showFloatingBubble = false
        this.saveSettings()
      }
      // #endif

      // #ifndef APP-PLUS
      uni.showToast({
        title: '悬浮气泡仅在App中可用',
        icon: 'none'
      })
      this.settings.showFloatingBubble = false
      this.saveSettings()
      // #endif
    },

    // 同步悬浮气泡状态
    syncFloatingBubbleState() {
      // #ifdef APP-PLUS
      try {
        const plugin = uni.requireNativePlugin('floating-window-plugin')

        // 检查悬浮气泡是否正在显示
        plugin.isBubbleShowing((result) => {

          // 同步开关状态
          if (result.isShowing !== this.settings.showFloatingBubble) {
            this.settings.showFloatingBubble = result.isShowing
            this.saveSettings()
          }
        })
      } catch (error) {
        console.error('同步悬浮气泡状态失败:', error)
      }
      // #endif
    },

    // 测试悬浮气泡功能
    testFloatingBubble() {
      uni.navigateTo({
        url: '/pages/test/floating-bubble'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../styles/theme.scss';

page {
  --primary-color: #007AFF;
}

.container {
  background-color: var(--bg-color);
  color: var(--text-color);
  min-height: 100vh;
  transition: all 0.3s ease;
}

.user-section {
  background: var(--primary-gradient);
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  transition: background 0.3s ease;
  
  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    
    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      margin-right: 30rpx;
      border: 4rpx solid rgba(255, 255, 255, 0.3);
      background-color: #f5f5f5;
      display: block;
      object-fit: cover;
    }
    
    .user-details {
      flex: 1;
      
      .nickname {
        display: block;
        color: white;
        font-size: 36rpx;
        font-weight: bold;
        margin-bottom: 8rpx;
      }
      
      .user-type {
        color: rgba(255, 255, 255, 0.8);
        font-size: 26rpx;
      }
    }
  }
  
  .user-stats {
    display: flex;
    justify-content: space-around;
    
    .stat-item {
      text-align: center;
      
      .stat-number {
        display: block;
        color: white;
        font-size: 48rpx;
        font-weight: bold;
        margin-bottom: 8rpx;
      }
      
      .stat-label {
        color: rgba(255, 255, 255, 0.8);
        font-size: 24rpx;
      }
    }
  }
}

.settings-section, .account-section, .other-section {
  background: var(--card-bg);
  margin-bottom: 20rpx;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  
  .section-title {
    display: block;
    padding: 30rpx 30rpx 20rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: var(--text-color);
    transition: color 0.3s ease;
  }
  
  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .setting-label {
      font-size: 28rpx;
      color: var(--text-color);
      transition: color 0.3s ease;
    }

    .setting-value {
      font-size: 26rpx;
      color: var(--text-color-secondary);
      transition: color 0.3s ease;

      &.premium {
        color: #ff6b35;
        font-weight: bold;
      }

      &.loading {
        color: #999;
        font-style: italic;
      }
    }

  }
}

.logout-section {
  padding: 40rpx 30rpx;
  
  .logout-btn {
    width: 100%;
    height: 88rpx;
    background: #ff4757;
    border-radius: 44rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    
    text {
      color: white;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}
</style>
