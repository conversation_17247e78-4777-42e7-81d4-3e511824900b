package com.emotional.service.task;

import com.emotional.service.llm.impl.DeepSeekService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 余额监控定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "app.llm.deepseek.balance-check-enabled", havingValue = "true", matchIfMissing = true)
public class BalanceMonitorTask {
    
    private final DeepSeekService deepSeekService;
    
    /**
     * 每小时检查一次余额
     */
    @Scheduled(fixedRate = 43200000) // 1小时 = 3600000毫秒
    public void checkBalance() {
        try {
            if (!deepSeekService.isAvailable()) {
                log.debug("DeepSeek服务不可用，跳过余额检查");
                return;
            }
            
            log.debug("开始检查DeepSeek账户余额");
            DeepSeekService.BalanceInfo balanceInfo = deepSeekService.checkBalance();
            
            if (balanceInfo.getSuccess()) {
                log.info("DeepSeek余额检查完成: 可用余额={} {}", 
                        balanceInfo.getAvailableBalance(), 
                        balanceInfo.getCurrency());
                
                if (balanceInfo.getNeedWarning()) {
                    log.warn("DeepSeek余额不足，已发送管理员通知");
                }
            } else {
                log.warn("DeepSeek余额检查失败: {}", balanceInfo.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("余额监控任务执行失败", e);
        }
    }
    
    /**
     * 每天早上9点检查余额（更详细的检查）
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void dailyBalanceReport() {
        try {
            if (!deepSeekService.isAvailable()) {
                log.info("DeepSeek服务不可用，跳过每日余额报告");
                return;
            }
            
            log.info("开始每日DeepSeek余额报告");
            DeepSeekService.BalanceInfo balanceInfo = deepSeekService.checkBalance();
            
            if (balanceInfo.getSuccess()) {
                log.info("=== DeepSeek每日余额报告 ===");
                log.info("总余额: {} {}", balanceInfo.getTotalBalance(), balanceInfo.getCurrency());
                log.info("可用余额: {} {}", balanceInfo.getAvailableBalance(), balanceInfo.getCurrency());
                log.info("服务状态: 正常");
                log.info("========================");
            } else {
                log.error("每日余额报告失败: {}", balanceInfo.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("每日余额报告任务执行失败", e);
        }
    }
}
