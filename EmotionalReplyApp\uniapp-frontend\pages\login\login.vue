<template>
  <view class="container">
    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 应用标题区域 -->
      <view class="header">
        <view class="app-icon">
          <text class="icon-emoji">💬</text>
        </view>
        <text class="app-name">情感回复助手</text>
        <text class="app-desc">智能分析，贴心回复</text>
      </view>
    
      <!-- 登录表单卡片 -->
      <view class="form-section">
        <view class="input-group">
          <view class="input-wrapper" :class="{ 'dropdown-active': showEmailDropdown }">
            <text class="input-icon">📧</text>
            <input
              v-model="emailUsername"
              class="input-field"
              placeholder="请输入用户名"
              type="text"
              :maxlength="30"
              @input="onEmailUsernameInput"
              @focus="onEmailFocus"
              @blur="onEmailBlur"
            />
            <view class="email-suffix-container">
              <text class="email-suffix" @click="toggleEmailDropdown">
                {{ selectedEmailSuffix }}
              </text>
              <text class="dropdown-arrow" @click="toggleEmailDropdown">
                {{ showEmailDropdown ? '▲' : '▼' }}
              </text>
            </view>
          </view>

          <!-- 邮箱后缀下拉选择 -->
          <view class="email-dropdown" v-if="showEmailDropdown">
            <view
              class="dropdown-item"
              v-for="(suffix, index) in emailSuffixes"
              :key="index"
              @click="selectEmailSuffix(suffix)"
              :class="{ 'selected': suffix === selectedEmailSuffix }"
            >
              <text class="suggestion-text">{{ suffix }}</text>
              <text class="check-icon" v-if="suffix === selectedEmailSuffix">✓</text>
            </view>
          </view>
        </view>

        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">🔒</text>
            <input
              v-model="loginForm.password"
              class="input-field"
              placeholder="请输入密码"
              type="password"
              :maxlength="20"
            />
          </view>
        </view>

        <view class="form-options">
          <view class="remember-section" @click="toggleRemember">
            <view class="checkbox" :class="{ checked: rememberPassword }">
              <text class="check-icon" v-if="rememberPassword">✓</text>
            </view>
            <text class="remember-text">记住密码</text>
          </view>
          <text class="forgot-password" @click="forgotPassword">忘记密码？</text>
        </view>
      
        <button
          class="login-btn"
          :class="{ disabled: !canLogin, loading: loading }"
          @click="handleLogin"
        >
          <text class="btn-text">{{ loading ? '登录中...' : '登录' }}</text>
          <view class="loading-spinner" v-if="loading"></view>
        </button>

        <!-- 协议文本 -->
        <view class="agreement-section">
          <text class="agreement">
            登录即表示同意
            <text class="link-text" @click="showAgreement">《用户协议》</text>
            和
            <text class="link-text" @click="showPrivacy">《隐私政策》</text>
          </text>
        </view>

        <view class="register-section">
          <text class="register-text">还没有账号？</text>
          <text class="register-link" @click="goRegister">立即注册</text>
        </view>
      </view>

      <!-- 快速登录区域 -->
      <view class="quick-login">
        <view class="divider">
          <view class="divider-line"></view>
          <text class="divider-text">快速登录</text>
          <view class="divider-line"></view>
        </view>

        <view class="quick-buttons">
          <button class="quick-btn wechat" @click="wechatLogin">
            <text class="btn-icon">💬</text>
            <text class="btn-text">微信登录</text>
          </button>
          <button class="quick-btn guest" @click="guestLogin">
            <text class="btn-icon">🎭</text>
            <text class="btn-text">游客体验</text>
          </button>
        </view>
      </view>
    </view>

  </view>
</template>

<script>
import { login } from '../../api/user.js'
import { UserManager } from '../../utils/user.js'
import { AuthGuard } from '../../utils/auth-guard.js'

export default {
  name: 'LoginPage',
  
  data() {
    return {
      loginForm: {
        username: '',
        password: ''
      },
      emailUsername: '',
      selectedEmailSuffix: '@qq.com',
      rememberPassword: false,
      loading: false,
      showEmailDropdown: false,
      emailSuffixes: [
        '@qq.com',
        '@163.com',
        '@126.com',
        '@gmail.com',
        '@sina.com',
        '@sohu.com',
        '@139.com',
        '@189.cn',
        '@yeah.net',
        '@foxmail.com',
        '@hotmail.com',
        '@outlook.com',
        '@aliyun.com',
        '@icloud.com'
      ]
    }
  },
  
  computed: {
    canLogin() {
      return this.loginForm.username.trim() &&
             this.loginForm.password.trim() &&
             !this.loading
    }
  },

  watch: {
    // 监听邮箱用户名和后缀变化，自动更新完整邮箱
    emailUsername: {
      handler(newVal) {
        this.updateFullEmail()
      },
      immediate: true
    },
    selectedEmailSuffix: {
      handler(newVal) {
        this.updateFullEmail()
      },
      immediate: true
    }
  },
  
  onLoad() {
    // 如果已经登录，直接跳转到首页
    if (UserManager.isLoggedIn()) {
      AuthGuard.loginSuccessRedirect()
      return
    }

    this.loadSavedCredentials()
  },
  
  methods: {
    // 加载保存的凭据
    loadSavedCredentials() {
      const saved = uni.getStorageSync('savedCredentials')
      if (saved) {
        this.loginForm = saved
        this.rememberPassword = true

        // 解析邮箱地址
        if (saved.username && saved.username.includes('@')) {
          const [username, domain] = saved.username.split('@')
          this.emailUsername = username
          this.selectedEmailSuffix = '@' + domain
        }
      }
    },
    


    // 更新完整邮箱地址
    updateFullEmail() {
      if (this.emailUsername.trim()) {
        this.loginForm.username = this.emailUsername + this.selectedEmailSuffix
      } else {
        this.loginForm.username = ''
      }
    },

    // 邮箱用户名输入处理
    onEmailUsernameInput(e) {
      this.emailUsername = e.detail.value
    },

    // 邮箱输入框获得焦点
    onEmailFocus() {
      // 焦点时不自动显示下拉框，只有点击后缀时才显示
    },

    // 邮箱输入框失去焦点
    onEmailBlur() {
      // 延迟隐藏，让点击事件能够触发
      setTimeout(() => {
        this.showEmailDropdown = false
      }, 200)
    },

    // 切换邮箱下拉框
    toggleEmailDropdown() {
      this.showEmailDropdown = !this.showEmailDropdown
    },

    // 选择邮箱后缀
    selectEmailSuffix(suffix) {
      this.selectedEmailSuffix = suffix
      this.showEmailDropdown = false
    },

    // 切换记住密码
    toggleRemember() {
      this.rememberPassword = !this.rememberPassword
    },

    // 记住密码选择（兼容旧版本）
    onRememberChange(e) {
      this.rememberPassword = e.detail.value.length > 0
    },
    
    // 处理登录
    async handleLogin() {
      if (!this.canLogin) return

      this.loading = true

      try {
        // 调用真实登录API
        const response = await login(this.loginForm.username, this.loginForm.password)

        // 保存用户信息和Token
        UserManager.setUserInfo(response.userInfo)
        UserManager.setToken(response.token)

        // 保存密码（如果选择记住）
        if (this.rememberPassword) {
          uni.setStorageSync('savedCredentials', this.loginForm)
        } else {
          uni.removeStorageSync('savedCredentials')
        }

        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })

        // 跳转到首页
        setTimeout(() => {
          AuthGuard.loginSuccessRedirect()
        }, 1500)

      } catch (error) {
        // 获取具体的错误信息
        let errorMessage = '登录失败，请检查邮箱和密码'

        if (error && error.message) {
          errorMessage = error.message
        } else if (error && error.data && error.data.message) {
          errorMessage = error.data.message
        } else if (error && typeof error === 'string') {
          errorMessage = error
        }

        // 如果是网络相关错误，允许使用演示模式
        if (errorMessage.includes('网络') || errorMessage.includes('连接') || errorMessage.includes('超时')) {
          uni.showModal({
            title: '连接失败',
            content: errorMessage + '\n\n是否使用演示模式？',
            success: (res) => {
              if (res.confirm) {
                this.enterDemoMode()
              }
            }
          })
        } else {
          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 3000
          })
        }
      } finally {
        this.loading = false
      }
    },

    // 进入演示模式
    enterDemoMode() {
      const demoUser = {
        id: 1,
        username: this.loginForm.username || 'demo',
        nickname: 'yumu',
        email: '<EMAIL>',
        avatar: '/static/images/default-avatar.png',
        isVip: false,
        dailyQuota: 10,
        todayUsed: 0,
        totalUsed: 0
      }

      UserManager.setUserInfo(demoUser)
      UserManager.setToken('demo_token_' + Date.now())

      uni.showToast({
        title: '已进入演示模式',
        icon: 'success'
      })

      setTimeout(() => {
        AuthGuard.loginSuccessRedirect()
      }, 1500)
    },
    
    // 忘记密码
    forgotPassword() {
      uni.navigateTo({
        url: '/pages/user/forgot-password'
      })
    },
    
    // 去注册
    goRegister() {
      uni.navigateTo({
        url: '/pages/user/register'
      })
    },
    
    // 微信登录
    wechatLogin() {
      uni.showToast({
        title: '微信登录功能开发中',
        icon: 'none'
      })
    },
    
    // 游客登录
    guestLogin() {
      const guestInfo = {
        id: 0,
        username: 'guest',
        nickname: '游客',
        avatar: '/static/images/guest-avatar.png',
        isVip: false
      }
      
      uni.setStorageSync('userInfo', guestInfo)
      uni.setStorageSync('isGuest', true)
      
      uni.showToast({
        title: '游客登录成功',
        icon: 'success'
      })
      
      setTimeout(() => {
        AuthGuard.loginSuccessRedirect()
      }, 1500)
    },
    
    // 显示用户协议
    showAgreement() {
      uni.navigateTo({
        url: '/pages/legal/agreement'
      })
    },
    
    // 显示隐私政策
    showPrivacy() {
      uni.navigateTo({
        url: '/pages/legal/privacy'
      })
    },
    
    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

// 主要内容区域
.main-content {
  flex: 1;
  padding: calc(80rpx + var(--status-bar-height, 0)) 30rpx 40rpx;
  display: flex;
  flex-direction: column;
}

// 应用标题区域
.header {
  text-align: center;
  margin-bottom: 80rpx;

  .app-icon {
    width: 120rpx;
    height: 120rpx;
    margin: 0 auto 30rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10rpx);

    .icon-emoji {
      font-size: 60rpx;
    }
  }

  .app-name {
    display: block;
    color: white;
    font-size: 48rpx;
    font-weight: bold;
    margin-bottom: 16rpx;
  }

  .app-desc {
    color: rgba(255, 255, 255, 0.8);
    font-size: 28rpx;
  }
}

// 登录表单卡片
.form-section {
  background: white;
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

  .input-group {
    margin-bottom: 30rpx;
    position: relative;

    .input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      background: #f8f9fa;
      border-radius: 16rpx;
      border: 2rpx solid #e9ecef;
      transition: all 0.3s ease;

      &:focus-within, &.dropdown-active {
        border-color: #2196F3;
        background: white;
        box-shadow: 0 0 0 6rpx rgba(33, 150, 243, 0.1);
      }

      .input-icon {
        padding: 0 20rpx;
        font-size: 32rpx;
        color: #6c757d;
      }

      .input-field {
        flex: 1;
        height: 88rpx;
        padding: 0 20rpx;
        border: none;
        background: transparent;
        font-size: 28rpx;
        color: #333;

        &::placeholder {
          color: #adb5bd;
        }
      }



      .email-suffix-container {
        display: flex;
        align-items: center;
        border-left: 1rpx solid #e9ecef;
        padding: 0 10rpx;
        min-width: 160rpx;

        .email-suffix {
          font-size: 28rpx;
          color: #6c757d;
          cursor: pointer;
          padding: 8rpx 12rpx;
          border-radius: 8rpx;
          transition: all 0.2s ease;

          &:active {
            background: rgba(108, 117, 125, 0.1);
          }
        }

        .dropdown-arrow {
          font-size: 20rpx;
          color: #6c757d;
          cursor: pointer;
          transition: transform 0.3s ease;
          margin-left: 8rpx;

          &:active {
            transform: scale(0.9);
          }
        }
      }
    }

    .email-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 2rpx solid #e9ecef;
      border-top: none;
      border-radius: 0 0 16rpx 16rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
      max-height: 400rpx;
      overflow-y: auto;
      z-index: 1000;

      .dropdown-item {
        padding: 20rpx 30rpx;
        border-bottom: 1rpx solid #f1f3f4;
        cursor: pointer;
        transition: background-color 0.2s ease;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &:hover {
          background-color: #f8f9fa;
        }

        &:active {
          background-color: #e9ecef;
        }

        &.selected {
          background-color: rgba(33, 150, 243, 0.1);
        }

        &:last-child {
          border-bottom: none;
        }

        .suggestion-text {
          font-size: 28rpx;
          color: #333;
        }

        .check-icon {
          font-size: 24rpx;
          color: #2196F3;
          font-weight: bold;
        }
      }
    }
  }
  
  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;

    .remember-section {
      display: flex;
      align-items: center;
      cursor: pointer;

      .checkbox {
        width: 36rpx;
        height: 36rpx;
        border: 2rpx solid #dee2e6;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12rpx;
        transition: all 0.3s ease;

        &.checked {
          background: #2196F3;
          border-color: #2196F3;

          .check-icon {
            color: white;
            font-size: 20rpx;
            font-weight: bold;
          }
        }
      }

      .remember-text {
        font-size: 26rpx;
        color: #666;
      }
    }

    .forgot-password {
      font-size: 26rpx;
      color: #2196F3;
      cursor: pointer;
      padding: 10rpx;
      min-height: 44rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        opacity: 0.7;
        transform: scale(0.95);
      }
    }
  }
  
  .login-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #2196F3, #21CBF3);
    border-radius: 16rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30rpx;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    .btn-text {
      color: white;
      font-size: 32rpx;
      font-weight: bold;
    }

    .loading-spinner {
      width: 32rpx;
      height: 32rpx;
      border: 3rpx solid rgba(255, 255, 255, 0.3);
      border-top: 3rpx solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-left: 16rpx;
    }

    &.disabled {
      background: #dee2e6;

      .btn-text {
        color: #6c757d;
      }
    }

    &.loading {
      pointer-events: none;
    }

    &:active {
      transform: translateY(2rpx);
    }
  }

  .agreement-section {
    margin-bottom: 20rpx;

    .agreement {
      text-align: center;
      font-size: 22rpx;
      color: #999;
      line-height: 1.6;
      display: block;

      .link-text {
        color: #2196F3;
        cursor: pointer;
      }
    }
  }

  .register-section {
    text-align: center;

    .register-text {
      font-size: 26rpx;
      color: #666;
    }

    .register-link {
      color: #2196F3;
      margin-left: 8rpx;
      font-size: 26rpx;
      cursor: pointer;
    }
  }
}

// 快速登录区域
.quick-login {
  margin-bottom: 40rpx;

  .divider {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;

    .divider-line {
      flex: 1;
      height: 1rpx;
      background: rgba(255, 255, 255, 0.3);
    }

    .divider-text {
      color: rgba(255, 255, 255, 0.8);
      font-size: 24rpx;
      padding: 0 20rpx;
    }
  }

  .quick-buttons {
    display: flex;
    gap: 20rpx;

    .quick-btn {
      flex: 1;
      height: 80rpx;
      border-radius: 16rpx;
      border: 2rpx solid rgba(255, 255, 255, 0.3);
      background: rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10rpx);
      transition: all 0.3s ease;

      .btn-icon {
        font-size: 28rpx;
        margin-right: 8rpx;
      }

      .btn-text {
        color: white;
        font-size: 26rpx;
      }

      &.wechat {
        background: rgba(7, 193, 96, 0.2);
        border-color: rgba(7, 193, 96, 0.5);
      }

      &.guest {
        background: rgba(255, 255, 255, 0.15);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }
}



// 动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
