<template>
  <view class="container">
    <view class="header">
      <text class="title">缓存管理测试</text>
    </view>

    <!-- 缓存信息显示 -->
    <view class="cache-info">
      <view class="info-item">
        <text class="label">总缓存大小:</text>
        <text class="value">{{ cacheStats ? cacheStats.totalSize : '计算中...' }}</text>
      </view>
      
      <view class="info-item">
        <text class="label">缓存项目数:</text>
        <text class="value">{{ cacheStats ? cacheStats.itemCount : '-' }}</text>
      </view>

      <view class="info-item">
        <text class="label">最后更新:</text>
        <text class="value">{{ lastUpdateTime }}</text>
      </view>
    </view>

    <!-- 缓存详情 -->
    <view class="cache-details" v-if="cacheStats && cacheStats.details">
      <text class="section-title">缓存详情</text>
      <view class="detail-item" v-for="(value, key) in cacheStats.details" :key="key">
        <text class="detail-label">{{ getCacheTypeName(key) }}:</text>
        <text class="detail-value">{{ value }}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="actions">
      <button class="action-btn primary" @click="refreshCacheInfo">刷新缓存信息</button>
      <button class="action-btn" @click="addTestData">添加测试数据</button>
      <button class="action-btn" @click="clearTestData">清除测试数据</button>
      <button class="action-btn danger" @click="clearAllCache">清除所有缓存</button>
    </view>

    <!-- 测试数据管理 -->
    <view class="test-data">
      <text class="section-title">测试数据</text>
      <view class="test-item">
        <text class="test-label">测试数据数量:</text>
        <text class="test-value">{{ testDataCount }}</text>
      </view>
      <input class="test-input" v-model="testDataSize" placeholder="输入测试数据大小(KB)" type="number" />
    </view>
  </view>
</template>

<script>
import { CacheManager } from '../../utils/storage.js'

export default {
  name: 'CacheTest',
  
  data() {
    return {
      cacheStats: null,
      lastUpdateTime: '',
      testDataCount: 0,
      testDataSize: 100 // 默认100KB
    }
  },

  onLoad() {
    this.refreshCacheInfo()
    this.countTestData()
  },

  methods: {
    // 刷新缓存信息
    async refreshCacheInfo() {
      try {
        this.cacheStats = await CacheManager.getCacheStats()
        this.lastUpdateTime = new Date().toLocaleTimeString()
      } catch (error) {
        console.error('获取缓存信息失败:', error)
        uni.showToast({
          title: '获取缓存信息失败',
          icon: 'error'
        })
      }
    },

    // 获取缓存类型显示名称
    getCacheTypeName(key) {
      const nameMap = {
        localStorage: '本地存储',
        imageCache: '图片缓存',
        tempFiles: '临时文件',
        apiCache: 'API缓存'
      }
      return nameMap[key] || key
    },

    // 添加测试数据
    addTestData() {
      try {
        const dataSize = parseInt(this.testDataSize) || 100
        const testData = 'x'.repeat(dataSize * 1024) // 生成指定大小的测试数据
        const key = `test_data_${Date.now()}`
        
        uni.setStorageSync(key, testData)
        this.testDataCount++
        
        uni.showToast({
          title: `已添加${dataSize}KB测试数据`,
          icon: 'success'
        })
        
        // 刷新缓存信息
        setTimeout(() => {
          this.refreshCacheInfo()
        }, 500)
        
      } catch (error) {
        console.error('添加测试数据失败:', error)
        uni.showToast({
          title: '添加测试数据失败',
          icon: 'error'
        })
      }
    },

    // 清除测试数据
    async clearTestData() {
      try {
        const storageInfo = uni.getStorageInfoSync()
        let clearedCount = 0
        
        if (storageInfo.keys) {
          storageInfo.keys.forEach(key => {
            if (key.startsWith('test_data_')) {
              uni.removeStorageSync(key)
              clearedCount++
            }
          })
        }
        
        this.testDataCount = 0
        
        uni.showToast({
          title: `已清除${clearedCount}个测试数据`,
          icon: 'success'
        })
        
        // 刷新缓存信息
        setTimeout(() => {
          this.refreshCacheInfo()
        }, 500)
        
      } catch (error) {
        console.error('清除测试数据失败:', error)
        uni.showToast({
          title: '清除测试数据失败',
          icon: 'error'
        })
      }
    },

    // 清除所有缓存
    clearAllCache() {
      uni.showModal({
        title: '清除所有缓存',
        content: '确定要清除所有缓存数据吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({
                title: '清除中...'
              })
              
              const result = await CacheManager.clearCache({
                clearUserData: false,
                clearSettings: false,
                clearHistory: true,
                clearTempData: true,
                clearImageCache: true,
                clearApiCache: true
              })
              
              uni.hideLoading()
              
              if (result.success) {
                const freedSpace = CacheManager.formatSize(result.freedSpace)
                uni.showToast({
                  title: `清除成功，释放${freedSpace}`,
                  icon: 'success'
                })
                
                // 刷新信息
                this.refreshCacheInfo()
                this.countTestData()
              } else {
                throw new Error(result.errors.join(', '))
              }
              
            } catch (error) {
              uni.hideLoading()
              console.error('清除缓存失败:', error)
              uni.showToast({
                title: '清除缓存失败',
                icon: 'error'
              })
            }
          }
        }
      })
    },

    // 统计测试数据数量
    countTestData() {
      try {
        const storageInfo = uni.getStorageInfoSync()
        let count = 0
        
        if (storageInfo.keys) {
          storageInfo.keys.forEach(key => {
            if (key.startsWith('test_data_')) {
              count++
            }
          })
        }
        
        this.testDataCount = count
      } catch (error) {
        console.error('统计测试数据失败:', error)
        this.testDataCount = 0
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2196F3;
}

.cache-info, .cache-details, .test-data {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.info-item, .detail-item, .test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child, .detail-item:last-child, .test-item:last-child {
  border-bottom: none;
}

.label, .detail-label, .test-label {
  color: #666;
  font-size: 28rpx;
}

.value, .detail-value, .test-value {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-btn {
  padding: 25rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  border: none;
  background-color: #f0f0f0;
  color: #333;
}

.action-btn.primary {
  background-color: #2196F3;
  color: white;
}

.action-btn.danger {
  background-color: #ff4757;
  color: white;
}

.test-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}
</style>
