package com.emotional.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.emotional.service.entity.UserStats;

import java.time.LocalDate;

/**
 * 用户统计服务接口
 */
public interface UserStatsService extends IService<UserStats> {

    /**
     * 获取用户统计信息
     * @param userId 用户ID
     * @return 用户统计信息
     */
    UserStats getUserStats(Long userId);

    /**
     * 获取用户指定日期的统计信息
     * @param userId 用户ID
     * @param date 日期
     * @return 统计信息
     */
    UserStats getUserStatsByDate(Long userId, LocalDate date);

    /**
     * 增加用户使用次数
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean incrementUsage(Long userId);

    /**
     * 初始化用户今日统计
     * @param userId 用户ID
     * @return 统计信息
     */
    UserStats initTodayStats(Long userId);

    /**
     * 获取用户总使用次数
     * @param userId 用户ID
     * @return 总使用次数
     */
    Integer getTotalUsage(Long userId);

    /**
     * 获取用户今日使用次数
     * @param userId 用户ID
     * @return 今日使用次数
     */
    Integer getTodayUsage(Long userId);

    /**
     * 检查用户是否还有配额
     * @param userId 用户ID
     * @return 是否有配额
     */
    boolean hasQuota(Long userId);
}
