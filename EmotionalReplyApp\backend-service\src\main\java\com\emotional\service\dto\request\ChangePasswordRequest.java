package com.emotional.service.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 修改密码请求DTO
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@Schema(description = "修改密码请求")
public class ChangePasswordRequest {
    
    /**
     * 旧密码
     */
    @Schema(description = "旧密码", example = "oldPassword123")
    @NotBlank(message = "旧密码不能为空")
    private String oldPassword;
    
    /**
     * 新密码
     */
    @Schema(description = "新密码", example = "newPassword123")
    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 20, message = "新密码长度必须在6-20个字符之间")
    @Pattern(regexp = "^(?=.*\\d)[A-Za-z\\d!@#$%^&*()_+\\-=\\[\\]{}|;:,.<>?]{6,20}$",
             message = "新密码必须包含数字，长度6-20位")
    private String newPassword;
    
    /**
     * 确认新密码
     */
    @Schema(description = "确认新密码", example = "newPassword123")
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;
    
    /**
     * 检查两次密码是否一致
     */
    public boolean isPasswordMatch() {
        return newPassword != null && newPassword.equals(confirmPassword);
    }
}
