"""
投票相关路由
"""
from flask import Blueprint, request, jsonify, session
from backend.models.platform_vote import PlatformVote, UserVote
from backend.models.user import User
from backend.main import db
import logging

logger = logging.getLogger(__name__)

vote_bp = Blueprint('vote', __name__, url_prefix='/api/vote')


def get_client_ip():
    """获取客户端IP地址"""
    # 检查是否通过代理
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr


@vote_bp.route('/platform/<platform>', methods=['POST'])
def vote_platform(platform):
    """
    为平台投票或取消投票
    """
    try:
        # 获取用户信息
        user_id = session.get('user_id')
        ip_address = get_client_ip()
        user_agent = request.headers.get('User-Agent', '')
        
        # 验证平台名称
        valid_platforms = ['zhihu', 'weibo']
        if platform not in valid_platforms:
            return jsonify({
                'success': False,
                'message': '无效的平台名称'
            }), 400
        
        # 检查是否已投票
        has_voted = UserVote.has_voted(platform, user_id, ip_address)
        
        if has_voted:
            # 取消投票
            if UserVote.remove_vote(platform, user_id, ip_address):
                new_count = PlatformVote.decrement_vote(platform)
                logger.info(f"用户取消投票 - 平台: {platform}, IP: {ip_address}, 用户ID: {user_id}")
                return jsonify({
                    'success': True,
                    'message': '取消投票成功',
                    'data': {
                        'platform': platform,
                        'vote_count': new_count,
                        'has_voted': False
                    }
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '取消投票失败'
                }), 500
        else:
            # 添加投票
            if UserVote.add_vote(platform, user_id, ip_address, user_agent):
                new_count = PlatformVote.increment_vote(platform)
                logger.info(f"用户投票 - 平台: {platform}, IP: {ip_address}, 用户ID: {user_id}")
                return jsonify({
                    'success': True,
                    'message': '投票成功',
                    'data': {
                        'platform': platform,
                        'vote_count': new_count,
                        'has_voted': True
                    }
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '投票失败，您可能已经投过票了'
                }), 400
                
    except Exception as e:
        logger.error(f"投票操作失败: {e}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': '服务器错误，请稍后再试'
        }), 500


@vote_bp.route('/platform/<platform>', methods=['GET'])
def get_platform_vote(platform):
    """
    获取平台投票信息
    """
    try:
        # 验证平台名称
        valid_platforms = ['zhihu', 'weibo']
        if platform not in valid_platforms:
            return jsonify({
                'success': False,
                'message': '无效的平台名称'
            }), 400
        
        # 获取票数
        vote_count = PlatformVote.get_vote_count(platform)
        
        # 检查当前用户是否已投票
        user_id = session.get('user_id')
        ip_address = get_client_ip()
        has_voted = UserVote.has_voted(platform, user_id, ip_address)
        
        return jsonify({
            'success': True,
            'data': {
                'platform': platform,
                'vote_count': vote_count,
                'has_voted': has_voted
            }
        })
        
    except Exception as e:
        logger.error(f"获取投票信息失败: {e}")
        return jsonify({
            'success': False,
            'message': '服务器错误，请稍后再试'
        }), 500


@vote_bp.route('/platforms', methods=['GET'])
def get_all_platform_votes():
    """
    获取所有平台的投票信息
    """
    try:
        # 获取所有平台票数
        all_votes = PlatformVote.get_all_votes()
        
        # 获取当前用户的投票状态
        user_id = session.get('user_id')
        ip_address = get_client_ip()
        user_votes = UserVote.get_user_votes(user_id, ip_address)
        
        # 确保所有平台都有数据
        platforms = ['zhihu', 'weibo']
        result = {}
        
        for platform in platforms:
            vote_count = all_votes.get(platform, 0)
            if vote_count == 0:
                # 如果数据库中没有记录，创建初始记录
                PlatformVote.get_or_create(platform)
                vote_count = PlatformVote.get_vote_count(platform)
            
            result[platform] = {
                'platform': platform,
                'vote_count': vote_count,
                'has_voted': platform in user_votes
            }
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error(f"获取所有投票信息失败: {e}")
        return jsonify({
            'success': False,
            'message': '服务器错误，请稍后再试'
        }), 500


@vote_bp.route('/user/votes', methods=['GET'])
def get_user_votes():
    """
    获取当前用户的投票记录
    """
    try:
        user_id = session.get('user_id')
        ip_address = get_client_ip()
        
        user_votes = UserVote.get_user_votes(user_id, ip_address)
        
        return jsonify({
            'success': True,
            'data': {
                'voted_platforms': user_votes
            }
        })
        
    except Exception as e:
        logger.error(f"获取用户投票记录失败: {e}")
        return jsonify({
            'success': False,
            'message': '服务器错误，请稍后再试'
        }), 500


@vote_bp.route('/stats', methods=['GET'])
def get_vote_stats():
    """
    获取投票统计信息（管理员功能）
    """
    try:
        # 这里可以添加管理员权限检查
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({
                'success': False,
                'message': '需要登录'
            }), 401
        
        # 获取统计信息
        total_votes = db.session.query(db.func.sum(PlatformVote.vote_count)).scalar() or 0
        total_voters = UserVote.query.count()
        platform_votes = PlatformVote.get_all_votes()
        
        return jsonify({
            'success': True,
            'data': {
                'total_votes': total_votes,
                'total_voters': total_voters,
                'platform_votes': platform_votes
            }
        })
        
    except Exception as e:
        logger.error(f"获取投票统计失败: {e}")
        return jsonify({
            'success': False,
            'message': '服务器错误，请稍后再试'
        }), 500
