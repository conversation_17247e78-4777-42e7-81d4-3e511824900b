package com.emotional.plugins;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

import com.alibaba.fastjson.JSONObject;

import io.dcloud.feature.uniapp.annotation.UniJSMethod;
import io.dcloud.feature.uniapp.bridge.UniJSCallback;
import io.dcloud.feature.uniapp.common.UniModule;

/**
 * 悬浮窗原生插件
 * 提供悬浮窗权限管理和悬浮气泡功能
 */
public class FloatingWindowPlugin extends UniModule {
    
    private static final String TAG = "FloatingWindowPlugin";
    private static final int REQUEST_OVERLAY_PERMISSION = 1001;
    
    private Context mContext;
    private FloatingBubbleService bubbleService;
    
    /**
     * 初始化插件
     */
    @UniJSMethod(uiThread = false)
    public void init(UniJSCallback callback) {
        try {
            mContext = mUniSDKInstance.getContext();
            Log.d(TAG, "FloatingWindowPlugin initialized");
            
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "Plugin initialized successfully");
            
            if (callback != null) {
                callback.invoke(result);
            }
        } catch (Exception e) {
            Log.e(TAG, "Plugin initialization failed", e);
            
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "Plugin initialization failed: " + e.getMessage());
            
            if (callback != null) {
                callback.invoke(result);
            }
        }
    }
    
    /**
     * 检查是否有悬浮窗权限
     */
    @UniJSMethod(uiThread = false)
    public void hasOverlayPermission(UniJSCallback callback) {
        boolean hasPermission = false;
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            hasPermission = Settings.canDrawOverlays(mContext);
        } else {
            hasPermission = true; // Android 6.0以下默认有权限
        }
        
        JSONObject result = new JSONObject();
        result.put("hasPermission", hasPermission);
        
        Log.d(TAG, "Overlay permission check: " + hasPermission);
        
        if (callback != null) {
            callback.invoke(result);
        }
    }
    
    /**
     * 请求悬浮窗权限
     */
    @UniJSMethod(uiThread = true)
    public void requestOverlayPermission(UniJSCallback callback) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(mContext)) {
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION);
                intent.setData(Uri.parse("package:" + mContext.getPackageName()));
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                
                try {
                    mContext.startActivity(intent);
                    
                    JSONObject result = new JSONObject();
                    result.put("success", true);
                    result.put("message", "Permission request sent");
                    
                    if (callback != null) {
                        callback.invoke(result);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Failed to request overlay permission", e);
                    
                    JSONObject result = new JSONObject();
                    result.put("success", false);
                    result.put("message", "Failed to request permission: " + e.getMessage());
                    
                    if (callback != null) {
                        callback.invoke(result);
                    }
                }
            } else {
                JSONObject result = new JSONObject();
                result.put("success", true);
                result.put("message", "Permission already granted");
                
                if (callback != null) {
                    callback.invoke(result);
                }
            }
        } else {
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "Permission not required for this Android version");
            
            if (callback != null) {
                callback.invoke(result);
            }
        }
    }
    
    /**
     * 显示悬浮气泡
     */
    @UniJSMethod(uiThread = false)
    public void showFloatingBubble(JSONObject options, UniJSCallback callback) {
        try {
            // 检查权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(mContext)) {
                JSONObject result = new JSONObject();
                result.put("success", false);
                result.put("message", "Overlay permission not granted");
                
                if (callback != null) {
                    callback.invoke(result);
                }
                return;
            }
            
            // 启动悬浮气泡服务
            Intent serviceIntent = new Intent(mContext, FloatingBubbleService.class);
            if (options != null) {
                serviceIntent.putExtra("options", options.toJSONString());
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                mContext.startForegroundService(serviceIntent);
            } else {
                mContext.startService(serviceIntent);
            }
            
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "Floating bubble started");
            
            Log.d(TAG, "Floating bubble service started");
            
            if (callback != null) {
                callback.invoke(result);
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to show floating bubble", e);
            
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "Failed to show floating bubble: " + e.getMessage());
            
            if (callback != null) {
                callback.invoke(result);
            }
        }
    }
    
    /**
     * 隐藏悬浮气泡
     */
    @UniJSMethod(uiThread = false)
    public void hideFloatingBubble(UniJSCallback callback) {
        try {
            Intent serviceIntent = new Intent(mContext, FloatingBubbleService.class);
            mContext.stopService(serviceIntent);
            
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "Floating bubble stopped");
            
            Log.d(TAG, "Floating bubble service stopped");
            
            if (callback != null) {
                callback.invoke(result);
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to hide floating bubble", e);
            
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "Failed to hide floating bubble: " + e.getMessage());
            
            if (callback != null) {
                callback.invoke(result);
            }
        }
    }
    
    /**
     * 检查悬浮气泡是否正在显示
     */
    @UniJSMethod(uiThread = false)
    public void isBubbleShowing(UniJSCallback callback) {
        boolean isShowing = FloatingBubbleService.isServiceRunning();
        
        JSONObject result = new JSONObject();
        result.put("isShowing", isShowing);
        
        if (callback != null) {
            callback.invoke(result);
        }
    }
    
    /**
     * 更新悬浮气泡配置
     */
    @UniJSMethod(uiThread = false)
    public void updateBubbleConfig(JSONObject config, UniJSCallback callback) {
        try {
            if (FloatingBubbleService.isServiceRunning()) {
                // 发送配置更新广播
                Intent updateIntent = new Intent(FloatingBubbleService.ACTION_UPDATE_CONFIG);
                updateIntent.putExtra("config", config.toJSONString());
                mContext.sendBroadcast(updateIntent);
                
                JSONObject result = new JSONObject();
                result.put("success", true);
                result.put("message", "Bubble config updated");
                
                if (callback != null) {
                    callback.invoke(result);
                }
            } else {
                JSONObject result = new JSONObject();
                result.put("success", false);
                result.put("message", "Bubble service is not running");
                
                if (callback != null) {
                    callback.invoke(result);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to update bubble config", e);
            
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "Failed to update config: " + e.getMessage());
            
            if (callback != null) {
                callback.invoke(result);
            }
        }
    }
}
