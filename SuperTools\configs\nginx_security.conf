
# SuperTools Nginx 安全配置
# 防止IP地址和敏感信息泄露

server {
    listen 80;
    server_name your_domain.com;  # 替换为您的域名
    
    # 隐藏服务器信息
    server_tokens off;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # 隐藏真实IP
    real_ip_header X-Forwarded-For;
    real_ip_recursive on;
    
    # 错误页面自定义（防止暴露服务器信息）
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root /var/www/html;
        internal;
    }
    
    location = /50x.html {
        root /var/www/html;
        internal;
    }
    
    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(sql|env|log|ini|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 代理到应用
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 隐藏代理错误信息
        proxy_intercept_errors on;
    }
}
