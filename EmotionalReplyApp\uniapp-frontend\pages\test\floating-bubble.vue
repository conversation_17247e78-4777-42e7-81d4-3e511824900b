<template>
  <view class="container">
    <view class="header">
      <text class="title">🎈 悬浮气泡调试</text>
      <text class="subtitle">测试悬浮气泡功能是否正常</text>
    </view>

    <!-- 调试信息 -->
    <view class="debug-section">
      <text class="section-title">调试信息</text>
      <view class="debug-item">
        <text class="debug-label">插件支持:</text>
        <text class="debug-value" :class="debugInfo.isSupported ? 'success' : 'error'">
          {{ debugInfo.isSupported ? '✅ 支持' : '❌ 不支持' }}
        </text>
      </view>
      <view class="debug-item">
        <text class="debug-label">插件初始化:</text>
        <text class="debug-value" :class="debugInfo.isInitialized ? 'success' : 'error'">
          {{ debugInfo.isInitialized ? '✅ 已初始化' : '❌ 未初始化' }}
        </text>
      </view>
      <view class="debug-item">
        <text class="debug-label">运行平台:</text>
        <text class="debug-value">{{ debugInfo.platform }}</text>
      </view>
      <view class="debug-item">
        <text class="debug-label">悬浮窗权限:</text>
        <text class="debug-value" :class="permissionStatus.hasPermission ? 'success' : 'error'">
          {{ permissionStatus.hasPermission ? '✅ 已授权' : '❌ 未授权' }}
        </text>
      </view>
      <view class="debug-item">
        <text class="debug-label">气泡状态:</text>
        <text class="debug-value" :class="bubbleStatus.isShowing ? 'success' : 'warning'">
          {{ bubbleStatus.isShowing ? '🎈 显示中' : '🫥 已隐藏' }}
        </text>
      </view>
    </view>

    <!-- 控制按钮 -->
    <view class="control-section">
      <text class="section-title">功能测试</text>
      
      <button class="test-btn" @click="checkPermission">
        🔍 检查权限
      </button>
      
      <button class="test-btn" @click="requestPermission">
        📋 申请权限
      </button>
      
      <button class="test-btn" @click="showBubble">
        🎈 显示气泡
      </button>
      
      <button class="test-btn" @click="hideBubble">
        🫥 隐藏气泡
      </button>
      
      <button class="test-btn" @click="toggleBubble">
        🔄 切换状态
      </button>
      
      <button class="test-btn" @click="checkStatus">
        📊 检查状态
      </button>
      
      <button class="test-btn" @click="refreshDebugInfo">
        🔄 刷新信息
      </button>
    </view>

    <!-- 日志输出 -->
    <view class="log-section">
      <text class="section-title">操作日志</text>
      <scroll-view class="log-container" scroll-y>
        <view v-for="(log, index) in logs" :key="index" class="log-item">
          <text class="log-time">{{ log.time }}</text>
          <text class="log-content">{{ log.content }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import floatingBubbleManager from '../../utils/floating-bubble.js'

export default {
  name: 'FloatingBubbleTest',
  
  data() {
    return {
      debugInfo: {
        isSupported: false,
        isInitialized: false,
        hasPlugin: false,
        platform: 'unknown'
      },
      permissionStatus: {
        hasPermission: false
      },
      bubbleStatus: {
        isShowing: false
      },
      logs: []
    }
  },
  
  onLoad() {
    this.addLog('页面加载完成')
    this.refreshDebugInfo()
  },
  
  methods: {
    // 添加日志
    addLog(content) {
      const now = new Date()
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
      
      this.logs.unshift({
        time,
        content
      })
      
      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }
    },
    
    // 刷新调试信息
    refreshDebugInfo() {
      this.debugInfo = floatingBubbleManager.getDebugInfo()
      this.addLog(`调试信息已刷新: ${JSON.stringify(this.debugInfo)}`)
      
      // 同时检查权限和状态
      this.checkPermission()
      this.checkStatus()
    },
    
    // 检查权限
    async checkPermission() {
      this.addLog('正在检查悬浮窗权限...')
      
      try {
        const result = await floatingBubbleManager.checkPermission()
        this.permissionStatus = result
        this.addLog(`权限检查结果: ${JSON.stringify(result)}`)
      } catch (error) {
        this.addLog(`权限检查失败: ${error.message}`)
      }
    },
    
    // 申请权限
    async requestPermission() {
      this.addLog('正在申请悬浮窗权限...')
      
      try {
        const result = await floatingBubbleManager.requestPermission()
        this.addLog(`权限申请结果: ${JSON.stringify(result)}`)
        
        if (result.granted) {
          uni.showToast({
            title: '权限申请成功',
            icon: 'success'
          })
          // 重新检查权限状态
          this.checkPermission()
        } else {
          uni.showToast({
            title: '权限申请失败',
            icon: 'error'
          })
        }
      } catch (error) {
        this.addLog(`权限申请失败: ${error.message}`)
      }
    },
    
    // 显示气泡
    async showBubble() {
      this.addLog('正在显示悬浮气泡...')
      
      try {
        const result = await floatingBubbleManager.showBubble({
          x: 100,
          y: 300,
          size: 56
        })
        
        this.addLog(`显示气泡结果: ${JSON.stringify(result)}`)
        
        if (result.success) {
          uni.showToast({
            title: '悬浮气泡已显示',
            icon: 'success'
          })
          this.checkStatus()
        } else {
          uni.showToast({
            title: result.message || '显示失败',
            icon: 'error'
          })
        }
      } catch (error) {
        this.addLog(`显示气泡失败: ${error.message}`)
      }
    },
    
    // 隐藏气泡
    async hideBubble() {
      this.addLog('正在隐藏悬浮气泡...')
      
      try {
        const result = await floatingBubbleManager.hideBubble()
        this.addLog(`隐藏气泡结果: ${JSON.stringify(result)}`)
        
        if (result.success) {
          uni.showToast({
            title: '悬浮气泡已隐藏',
            icon: 'success'
          })
          this.checkStatus()
        } else {
          uni.showToast({
            title: result.message || '隐藏失败',
            icon: 'error'
          })
        }
      } catch (error) {
        this.addLog(`隐藏气泡失败: ${error.message}`)
      }
    },
    
    // 切换气泡状态
    async toggleBubble() {
      this.addLog('正在切换悬浮气泡状态...')
      
      try {
        const result = await floatingBubbleManager.toggleBubble()
        this.addLog(`切换状态结果: ${JSON.stringify(result)}`)
        
        if (result.success) {
          uni.showToast({
            title: '状态切换成功',
            icon: 'success'
          })
          this.checkStatus()
        }
      } catch (error) {
        this.addLog(`切换状态失败: ${error.message}`)
      }
    },
    
    // 检查气泡状态
    async checkStatus() {
      this.addLog('正在检查悬浮气泡状态...')
      
      try {
        const result = await floatingBubbleManager.isBubbleShowing()
        this.bubbleStatus = result
        this.addLog(`气泡状态: ${JSON.stringify(result)}`)
      } catch (error) {
        this.addLog(`状态检查失败: ${error.message}`)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  
  .title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: #666;
  }
}

.debug-section, .control-section, .log-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.debug-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.debug-label {
  font-size: 28rpx;
  color: #666;
}

.debug-value {
  font-size: 28rpx;
  font-weight: bold;
  
  &.success {
    color: #4CAF50;
  }
  
  &.error {
    color: #F44336;
  }
  
  &.warning {
    color: #FF9800;
  }
}

.test-btn {
  width: 100%;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #2196F3;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.log-container {
  height: 400rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 10rpx;
}

.log-item {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  
  .log-time {
    color: #999;
    margin-right: 10rpx;
    min-width: 80rpx;
  }
  
  .log-content {
    color: #333;
    flex: 1;
    word-break: break-all;
  }
}
</style>
