package com.emotional.service.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应码枚举
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Getter
@AllArgsConstructor
public enum ResultCode {
    
    // 通用响应码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    PARAM_ERROR(400, "参数错误"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    
    // 用户相关
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_DISABLED(1002, "用户已被禁用"),
    USER_QUOTA_EXCEEDED(1003, "用户配额已用完"),
    
    // 认证相关
    UNAUTHORIZED(2001, "未授权访问"),
    TOKEN_INVALID(2002, "Token无效"),
    TOKEN_EXPIRED(2003, "Token已过期"),
    LOGIN_FAILED(2004, "登录失败"),
    
    // 业务相关
    MESSAGE_TOO_LONG(3001, "消息内容过长"),
    MESSAGE_EMPTY(3002, "消息内容不能为空"),
    EMOTION_ANALYZE_FAILED(3003, "情感分析失败"),
    REPLY_GENERATE_FAILED(3004, "回复生成失败"),
    HISTORY_NOT_FOUND(3005, "历史记录不存在"),
    
    // 系统相关
    SYSTEM_BUSY(4001, "系统繁忙，请稍后重试"),
    DATABASE_ERROR(4002, "数据库操作失败"),
    REDIS_ERROR(4003, "缓存操作失败"),
    EXTERNAL_API_ERROR(4004, "外部API调用失败");
    
    private final Integer code;
    private final String message;
}
