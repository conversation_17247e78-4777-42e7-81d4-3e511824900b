package com.emotional.service.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OpenAPI配置类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Configuration
public class OpenApiConfig {
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("情感回复助手API")
                        .description("基于AI的智能情感分析和回复生成服务")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("YUMU")
                                .email("<EMAIL>")
                                .url("https://emotional-reply.com"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")));
    }
}
