package com.emotional.service.controller;

import com.emotional.service.llm.impl.DeepSeekService;
import com.emotional.service.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * DeepSeek AI服务管理控制器
 */
@Tag(name = "DeepSeek AI服务", description = "DeepSeek大语言模型服务状态和管理")
@RestController
@RequestMapping("/api/llm")
@RequiredArgsConstructor
@Slf4j
public class LLMController {

    private final DeepSeekService deepSeekService;
    
    /**
     * 获取DeepSeek服务状态
     */
    @Operation(summary = "获取DeepSeek服务状态", description = "查看DeepSeek AI服务的可用性状态")
    @GetMapping("/status")
    public Result<?> getServiceStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("providerName", deepSeekService.getProviderName());
            status.put("available", deepSeekService.isAvailable());
            status.put("enabled", true);

            return Result.success(status);
        } catch (Exception e) {
            log.error("获取DeepSeek服务状态失败", e);
            return Result.error("获取服务状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查DeepSeek服务是否可用
     */
    @Operation(summary = "检查DeepSeek服务可用性", description = "检查DeepSeek AI服务是否可用")
    @GetMapping("/available")
    public Result<Boolean> checkAvailability() {
        try {
            boolean available = deepSeekService.isAvailable();
            return Result.success(available);
        } catch (Exception e) {
            log.error("检查DeepSeek服务可用性失败", e);
            return Result.error("检查服务可用性失败: " + e.getMessage());
        }
    }

    /**
     * 检查DeepSeek账户余额
     */
    @Operation(summary = "检查DeepSeek账户余额", description = "查看DeepSeek API账户余额信息")
    @GetMapping("/balance")
    public Result<?> checkBalance() {
        try {
            DeepSeekService.BalanceInfo balanceInfo = deepSeekService.checkBalance();
            return Result.success(balanceInfo);
        } catch (Exception e) {
            log.error("检查DeepSeek余额失败", e);
            return Result.error("检查余额失败: " + e.getMessage());
        }
    }
}
