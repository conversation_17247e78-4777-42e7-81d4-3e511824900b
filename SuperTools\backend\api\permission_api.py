#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
权限管理API模块
提供用户权限查询和管理相关的API
"""

import logging
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user

from backend.main import db
from backend.models.user import User
from backend.utils.permissions import require_role, PermissionManager

# 创建日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
permission_api = Blueprint('permission_api', __name__, url_prefix='/permission')

@permission_api.route('/check', methods=['GET'])
@login_required
def check_permissions():
    """
    检查当前用户权限

    响应:
        {
            "success": true,
            "message": "权限查询成功",
            "data": {
                "role": "normal_user",
                "role_display": "普通用户",
                "permissions": {
                    "video_platforms": ["douyin", "kuaishou", "bilibili"],
                    "article_platforms": [],
                    "admin_functions": [],
                    "download_limit": 50,
                    "api_rate_limit": 20
                },
                "is_banned": false,
                "is_pro_valid": true,
                "vip_expire_date": null
            }
        }
    """
    try:
        # 检查并自动降级过期的Pro用户
        if current_user.role == 'pro_user' and not current_user.is_pro_user_valid():
            logger.info(f"检测到Pro用户 {current_user.username} 已过期，自动降级为普通用户")
            PermissionManager.downgrade_user_to_normal(current_user)

        permissions = current_user.get_permissions()

        return jsonify({
            "success": True,
            "message": "权限查询成功",
            "data": {
                "role": current_user.role,
                "role_display": current_user.get_role_display(),
                "permissions": permissions,
                "is_banned": current_user.is_banned,
                "is_pro_valid": current_user.is_pro_user_valid(),
                "vip_expire_date": current_user.vip_expire_date.isoformat() if current_user.vip_expire_date else None
            }
        })

    except Exception as e:
        logger.error(f"权限查询失败: {e}")
        return jsonify({
            "success": False,
            "message": "权限查询失败",
            "data": None
        }), 500

@permission_api.route('/upgrade', methods=['POST'])
@login_required
def upgrade_user():
    """
    升级用户权限（用户自助升级或管理员操作）

    请求体:
        {
            "user_id": 1,  // 可选，不提供则升级当前用户
            "target_role": "pro_user",
            "days": 30
        }

    响应:
        {
            "success": true,
            "message": "用户权限升级成功",
            "data": {
                "user_id": 1,
                "username": "test_user",
                "old_role": "normal_user",
                "new_role": "pro_user",
                "vip_expire_date": "2024-01-01T00:00:00"
            }
        }
    """
    try:
        data = request.get_json()

        user_id = data.get('user_id')
        target_role = data.get('target_role')
        days = data.get('days', 30)

        if not target_role:
            return jsonify({
                "success": False,
                "message": "请提供目标角色",
                "data": None
            }), 400

        # 确定要升级的用户
        if user_id and user_id != 'current':
            # 管理员升级其他用户
            if current_user.role != 'super_admin':
                return jsonify({
                    "success": False,
                    "message": "您没有权限升级其他用户",
                    "data": None
                }), 403

            user = User.query.get(user_id)
            if not user:
                return jsonify({
                    "success": False,
                    "message": "用户不存在",
                    "data": None
                }), 404
        else:
            # 用户自助升级
            user = current_user

        old_role = user.role

        # 验证升级/降级操作的合理性
        if user != current_user and current_user.role != 'super_admin':
            return jsonify({
                "success": False,
                "message": "您没有权限操作其他用户",
                "data": None
            }), 403

        # 普通用户只能升级到Pro，不能降级或升级到管理员
        if user == current_user and current_user.role != 'super_admin':
            if target_role == 'super_admin':
                return jsonify({
                    "success": False,
                    "message": "无法升级到管理员角色",
                    "data": None
                }), 403

            if target_role == 'normal_user' and old_role == 'pro_user':
                # 允许Pro用户降级到普通用户
                pass
            elif target_role == 'pro_user' and old_role == 'normal_user':
                # 允许普通用户升级到Pro用户
                pass
            elif target_role == 'pro_user' and old_role == 'pro_user':
                # 允许Pro用户续费
                pass
            else:
                return jsonify({
                    "success": False,
                    "message": "无效的升级/降级操作",
                    "data": None
                }), 400

        # 执行升级/降级
        if target_role == 'pro_user':
            PermissionManager.upgrade_user_to_pro(user, days)
        elif target_role == 'normal_user':
            PermissionManager.downgrade_user_to_normal(user)
        elif target_role == 'super_admin' and current_user.role == 'super_admin':
            user.role = 'super_admin'
            user.permissions = None
            db.session.commit()
        else:
            return jsonify({
                "success": False,
                "message": "无效的目标角色",
                "data": None
            }), 400

        return jsonify({
            "success": True,
            "message": "用户权限升级成功",
            "data": {
                "user_id": user.id,
                "username": user.username,
                "old_role": old_role,
                "new_role": user.role,
                "vip_expire_date": user.vip_expire_date.isoformat() if user.vip_expire_date else None
            }
        })

    except Exception as e:
        logger.error(f"用户权限升级失败: {e}")
        return jsonify({
            "success": False,
            "message": "用户权限升级失败",
            "data": None
        }), 500

@permission_api.route('/cleanup-expired', methods=['POST'])
@require_role('super_admin')
def cleanup_expired_users():
    """
    清理过期的Pro用户（仅超级管理员可用）

    响应:
        {
            "success": true,
            "message": "过期用户清理完成",
            "data": {
                "downgraded_count": 3
            }
        }
    """
    try:
        downgraded_count = PermissionManager.check_and_downgrade_expired_users()

        return jsonify({
            "success": True,
            "message": f"过期用户清理完成，共降级 {downgraded_count} 个用户",
            "data": {
                "downgraded_count": downgraded_count
            }
        })

    except Exception as e:
        logger.error(f"清理过期用户失败: {e}")
        return jsonify({
            "success": False,
            "message": "清理过期用户失败",
            "data": None
        }), 500

@permission_api.route('/ban', methods=['POST'])
@require_role('super_admin')
def ban_user():
    """
    封禁用户（仅超级管理员）

    请求体:
        {
            "user_id": 1,
            "reason": "违规操作"
        }
    """
    try:
        data = request.get_json()

        user_id = data.get('user_id')
        reason = data.get('reason', '违规操作')

        if not user_id:
            return jsonify({
                "success": False,
                "message": "请提供用户ID",
                "data": None
            }), 400

        # 查找用户
        user = User.query.get(user_id)
        if not user:
            return jsonify({
                "success": False,
                "message": "用户不存在",
                "data": None
            }), 404

        # 不能封禁超级管理员
        if user.role == 'super_admin':
            return jsonify({
                "success": False,
                "message": "不能封禁超级管理员",
                "data": None
            }), 403

        # 执行封禁
        PermissionManager.ban_user(user, reason)

        return jsonify({
            "success": True,
            "message": "用户封禁成功",
            "data": {
                "user_id": user.id,
                "username": user.username,
                "reason": reason
            }
        })

    except Exception as e:
        logger.error(f"用户封禁失败: {e}")
        return jsonify({
            "success": False,
            "message": "用户封禁失败",
            "data": None
        }), 500

@permission_api.route('/unban', methods=['POST'])
@require_role('super_admin')
def unban_user():
    """
    解封用户（仅超级管理员）

    请求体:
        {
            "user_id": 1
        }
    """
    try:
        data = request.get_json()

        user_id = data.get('user_id')

        if not user_id:
            return jsonify({
                "success": False,
                "message": "请提供用户ID",
                "data": None
            }), 400

        # 查找用户
        user = User.query.get(user_id)
        if not user:
            return jsonify({
                "success": False,
                "message": "用户不存在",
                "data": None
            }), 404

        # 执行解封
        PermissionManager.unban_user(user)

        return jsonify({
            "success": True,
            "message": "用户解封成功",
            "data": {
                "user_id": user.id,
                "username": user.username
            }
        })

    except Exception as e:
        logger.error(f"用户解封失败: {e}")
        return jsonify({
            "success": False,
            "message": "用户解封失败",
            "data": None
        }), 500

@permission_api.route('/platforms', methods=['GET'])
@login_required
def get_available_platforms():
    """
    获取当前用户可用的平台列表

    响应:
        {
            "success": true,
            "message": "平台列表获取成功",
            "data": {
                "video_platforms": [
                    {"name": "douyin", "display": "抖音", "available": true},
                    {"name": "kuaishou", "display": "快手", "available": true},
                    {"name": "bilibili", "display": "哔哩哔哩", "available": true}
                ],
                "article_platforms": [
                    {"name": "csdn", "display": "CSDN", "available": false},
                    {"name": "zhihu", "display": "知乎", "available": false}
                ]
            }
        }
    """
    try:
        permissions = current_user.get_permissions()
        video_platforms = permissions.get('video_platforms', [])
        article_platforms = permissions.get('article_platforms', [])

        # 定义所有平台
        all_video_platforms = [
            {"name": "douyin", "display": "抖音"},
            {"name": "kuaishou", "display": "快手"},
            {"name": "bilibili", "display": "哔哩哔哩"}
        ]

        all_article_platforms = [
            {"name": "csdn", "display": "CSDN"},
            {"name": "zhihu", "display": "知乎"}
        ]

        # 添加可用性标识
        for platform in all_video_platforms:
            platform['available'] = platform['name'] in video_platforms

        for platform in all_article_platforms:
            platform['available'] = platform['name'] in article_platforms

        return jsonify({
            "success": True,
            "message": "平台列表获取成功",
            "data": {
                "video_platforms": all_video_platforms,
                "article_platforms": all_article_platforms
            }
        })

    except Exception as e:
        logger.error(f"平台列表获取失败: {e}")
        return jsonify({
            "success": False,
            "message": "平台列表获取失败",
            "data": None
        }), 500
