# 悬浮窗插件配置说明

## 问题描述
云打包时出现错误：`插件[floating-window-plugin(floating-window-plugin)]不存在: 插件在native-plugins目录下不存在`

## 解决方案

### 方案一：使用DCloud官方插件（推荐）

1. **在HBuilderX中安装插件**
   - 打开HBuilderX
   - 点击菜单：工具 → 插件安装
   - 搜索"悬浮窗"或"FloatingBubble"
   - 安装官方提供的悬浮窗插件

2. **更新manifest.json配置**
   ```json
   "nativePlugins": {
     "DC-FloatingBubble": {
       "version": "1.0.0",
       "description": "悬浮气泡插件",
       "provider": "DCloud"
     }
   }
   ```

3. **使用插件市场插件**
   - 访问：https://ext.dcloud.net.cn/
   - 搜索"悬浮窗"相关插件
   - 选择评分高、更新及时的插件
   - 按照插件说明进行配置

### 方案二：自定义插件开发

如果需要自定义悬浮窗功能，需要：

1. **创建原生插件目录结构**
   ```
   nativeplugins/
   └── floating-window-plugin/
       ├── android/
       │   ├── libs/
       │   ├── src/
       │   └── build.gradle
       ├── ios/
       └── package.json
   ```

2. **开发Android原生代码**
   - 实现悬浮窗权限检查
   - 实现悬浮窗显示/隐藏
   - 实现与uni-app的通信接口

3. **配置插件描述文件**
   ```json
   {
     "name": "floating-window-plugin",
     "id": "floating-window-plugin",
     "version": "1.0.0",
     "description": "悬浮窗插件",
     "platforms": ["android"]
   }
   ```

### 方案三：临时禁用悬浮窗功能

如果暂时不需要悬浮窗功能，可以：

1. **注释掉manifest.json中的插件配置**
2. **在代码中添加功能检测**
   ```javascript
   // 检查是否支持悬浮窗
   const isFloatingSupported = () => {
     // #ifdef APP-PLUS
     try {
       const plugin = uni.requireNativePlugin('DC-FloatingBubble')
       return !!plugin
     } catch (error) {
       return false
     }
     // #endif
     return false
   }
   ```

## 权限配置

确保在manifest.json中包含必要的权限：

```json
"permissions": [
  "<uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\" />",
  "<uses-permission android:name=\"android.permission.FOREGROUND_SERVICE\" />",
  "<uses-permission android:name=\"android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS\" />"
]
```

## 测试建议

1. **本地测试**
   - 使用自定义基座测试
   - 确保插件在真机上正常工作

2. **云打包测试**
   - 先使用测试证书进行云打包
   - 确认插件配置正确后再使用正式证书

3. **权限测试**
   - 测试悬浮窗权限申请流程
   - 测试在不同Android版本上的兼容性

## 注意事项

1. **Android版本兼容性**
   - Android 6.0+ 需要动态申请悬浮窗权限
   - 不同厂商的权限管理可能有差异

2. **用户体验**
   - 提供清晰的权限说明
   - 在权限被拒绝时提供替代方案

3. **性能考虑**
   - 悬浮窗可能影响系统性能
   - 提供开关让用户自主选择
