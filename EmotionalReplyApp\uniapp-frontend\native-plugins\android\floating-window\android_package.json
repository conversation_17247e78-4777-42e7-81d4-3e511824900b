{"name": "floating-window-plugin", "id": "floating-window-plugin", "version": "1.0.0", "description": "悬浮窗原生插件，提供系统级悬浮气泡功能", "main": "index.js", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"android": {"plugins": [{"type": "module", "name": "FloatingWindowPlugin", "class": "com.emotional.plugins.FloatingWindowPlugin"}], "integrateType": "aar", "dependencies": ["androidx.appcompat:appcompat:1.4.2", "com.google.android.material:material:1.6.1"], "permissions": ["android.permission.SYSTEM_ALERT_WINDOW", "android.permission.FOREGROUND_SERVICE", "android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"], "minSdkVersion": "23", "useAndroidX": true}, "ios": {"plugins": [{"type": "module", "name": "FloatingWindowPlugin", "class": "FloatingWindowPlugin"}], "frameworks": ["UIKit", "Foundation"], "deploymentTarget": "10.0"}}, "keywords": ["floating", "window", "overlay", "bubble", "uniapp", "plugin"], "author": {"name": "EmotionalReply Team", "email": "<EMAIL>"}, "license": "MIT", "engines": {"HBuilderX": "^3.0.0"}}