package com.emotional.service.dto.llm;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.util.List;

/**
 * 回复生成请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReplyGenerationRequest {
    
    /**
     * 原始消息
     */
    @NotBlank(message = "原始消息不能为空")
    @Size(max = 2000, message = "原始消息不能超过2000个字符")
    private String originalMessage;
    
    /**
     * 情感分析结果
     */
    private EmotionAnalysisResponse emotionAnalysis;
    
    /**
     * 回复风格列表
     */
    private List<String> replyStyles;
    
    /**
     * 用户ID（用于个性化）
     */
    private Long userId;
    
    /**
     * 生成回复数量
     */
    @Min(value = 1, message = "至少生成1个回复")
    @Max(value = 10, message = "最多生成10个回复")
    private Integer count = 3;
    
    /**
     * 最大长度
     */
    @Min(value = 10, message = "回复最小长度为10")
    @Max(value = 500, message = "回复最大长度为500")
    private Integer maxLength = 200;
    
    /**
     * 最小长度
     */
    @Min(value = 5, message = "回复最小长度为5")
    private Integer minLength = 10;
    
    /**
     * 温度参数（控制创造性，0.0-1.0）
     */
    private Double temperature = 0.7;
    
    /**
     * 语言
     */
    private String language = "zh-CN";
    
    /**
     * 上下文信息
     */
    private String context;
    
    /**
     * 用户偏好设置
     */
    private String userPreferences;

    /**
     * 对方性别（用于生成更合适的回复建议）
     * 可选值：male（男性）、female（女性）、空字符串（未知）
     */
    private String senderGender;
}
