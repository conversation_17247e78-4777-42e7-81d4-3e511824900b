#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
VIP账号管理模型
用于管理各平台的VIP账号池
"""

import datetime
import json
from enum import Enum
from backend.main import db


class AccountStatus(Enum):
    """账号状态枚举"""
    ACTIVE = "active"           # 活跃可用
    INACTIVE = "inactive"       # 暂时不可用
    BANNED = "banned"          # 被封禁
    EXPIRED = "expired"        # 已过期
    MAINTENANCE = "maintenance" # 维护中


class Platform(Enum):
    """支持的平台枚举"""
    CSDN = "csdn"
    ZHIHU = "zhihu"
    JIANSHU = "jianshu"
    CNBLOGS = "cnblogs"


class VipAccount(db.Model):
    """VIP账号模型类"""
    __tablename__ = 'vip_accounts'

    id = db.Column(db.Integer, primary_key=True)
    platform = db.Column(db.String(50), nullable=False, index=True)  # 平台名称
    username = db.Column(db.String(100), nullable=False)  # 账号用户名
    password = db.Column(db.String(255), nullable=False)  # 加密后的密码
    email = db.Column(db.String(255))  # 账号邮箱
    phone = db.Column(db.String(20))  # 账号手机号
    
    # 账号状态和信息
    status = db.Column(db.String(20), default=AccountStatus.ACTIVE.value, nullable=False)
    vip_level = db.Column(db.String(50))  # VIP等级，如 "CSDN会员", "CSDN专家"
    expire_date = db.Column(db.DateTime)  # VIP到期时间
    
    # 使用统计
    total_uses = db.Column(db.Integer, default=0, nullable=False)  # 总使用次数
    daily_uses = db.Column(db.Integer, default=0, nullable=False)  # 今日使用次数
    last_used_at = db.Column(db.DateTime)  # 最后使用时间
    last_reset_date = db.Column(db.Date, default=datetime.date.today)  # 最后重置日期
    
    # 限制设置
    daily_limit = db.Column(db.Integer, default=100)  # 每日使用限制
    concurrent_limit = db.Column(db.Integer, default=3)  # 并发使用限制
    current_concurrent = db.Column(db.Integer, default=0)  # 当前并发数
    
    # 账号配置
    cookies = db.Column(db.Text)  # 存储登录cookies (JSON格式)
    headers = db.Column(db.Text)  # 存储请求头配置 (JSON格式)
    proxy_config = db.Column(db.Text)  # 代理配置 (JSON格式)
    
    # 管理信息
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)  # 创建者
    notes = db.Column(db.Text)  # 备注信息
    
    # 健康检查
    last_health_check = db.Column(db.DateTime)  # 最后健康检查时间
    health_status = db.Column(db.String(20), default='unknown')  # 健康状态: healthy, unhealthy, unknown
    error_count = db.Column(db.Integer, default=0)  # 错误计数
    last_error = db.Column(db.Text)  # 最后错误信息
    
    # 关联关系
    creator = db.relationship('User', backref=db.backref('created_vip_accounts', lazy='dynamic'))
    
    # 复合索引
    __table_args__ = (
        db.UniqueConstraint('platform', 'username', name='uq_platform_username'),
        db.Index('idx_platform_status', 'platform', 'status'),
        db.Index('idx_status_daily_uses', 'status', 'daily_uses'),
    )

    def __init__(self, platform, username, password, created_by, **kwargs):
        """初始化VIP账号"""
        self.platform = platform
        self.username = username
        self.password = self._encrypt_password(password)
        self.created_by = created_by
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def _encrypt_password(self, password):
        """加密密码"""
        from cryptography.fernet import Fernet
        import os
        
        # 获取加密密钥（应该从环境变量或配置文件获取）
        key = os.environ.get('VIP_ACCOUNT_KEY', Fernet.generate_key())
        if isinstance(key, str):
            key = key.encode()
        
        f = Fernet(key)
        return f.encrypt(password.encode()).decode()

    def _decrypt_password(self):
        """解密密码"""
        from cryptography.fernet import Fernet
        import os
        
        key = os.environ.get('VIP_ACCOUNT_KEY', Fernet.generate_key())
        if isinstance(key, str):
            key = key.encode()
            
        f = Fernet(key)
        return f.decrypt(self.password.encode()).decode()

    def get_password(self):
        """获取解密后的密码"""
        try:
            return self._decrypt_password()
        except Exception:
            return None

    def set_password(self, password):
        """设置新密码"""
        self.password = self._encrypt_password(password)

    def get_cookies(self):
        """获取cookies字典"""
        if self.cookies:
            try:
                return json.loads(self.cookies)
            except:
                return {}
        return {}

    def set_cookies(self, cookies_dict):
        """设置cookies"""
        self.cookies = json.dumps(cookies_dict)

    def get_headers(self):
        """获取请求头字典"""
        if self.headers:
            try:
                return json.loads(self.headers)
            except:
                return {}
        return {}

    def set_headers(self, headers_dict):
        """设置请求头"""
        self.headers = json.dumps(headers_dict)

    def get_proxy_config(self):
        """获取代理配置"""
        if self.proxy_config:
            try:
                return json.loads(self.proxy_config)
            except:
                return {}
        return {}

    def set_proxy_config(self, proxy_dict):
        """设置代理配置"""
        self.proxy_config = json.dumps(proxy_dict)

    def is_available(self):
        """检查账号是否可用"""
        # 检查状态
        if self.status != AccountStatus.ACTIVE.value:
            return False
        
        # 检查VIP是否过期
        if self.expire_date and datetime.datetime.now() > self.expire_date:
            return False
        
        # 检查每日使用限制
        self._reset_daily_counter_if_needed()
        if self.daily_limit > 0 and self.daily_uses >= self.daily_limit:
            return False
        
        # 检查并发限制
        if self.concurrent_limit > 0 and self.current_concurrent >= self.concurrent_limit:
            return False
        
        return True

    def _reset_daily_counter_if_needed(self):
        """如果需要，重置每日计数器"""
        today = datetime.date.today()
        if self.last_reset_date != today:
            self.daily_uses = 0
            self.last_reset_date = today
            db.session.commit()

    def acquire(self):
        """获取账号使用权"""
        if not self.is_available():
            return False
        
        self.current_concurrent += 1
        self.daily_uses += 1
        self.total_uses += 1
        self.last_used_at = datetime.datetime.now()
        db.session.commit()
        return True

    def release(self):
        """释放账号使用权"""
        if self.current_concurrent > 0:
            self.current_concurrent -= 1
            db.session.commit()

    def mark_error(self, error_message):
        """标记错误"""
        self.error_count += 1
        self.last_error = error_message
        self.health_status = 'unhealthy'
        
        # 如果错误次数过多，暂时禁用账号
        if self.error_count >= 5:
            self.status = AccountStatus.INACTIVE.value
        
        db.session.commit()

    def mark_healthy(self):
        """标记为健康状态"""
        self.health_status = 'healthy'
        self.last_health_check = datetime.datetime.now()
        self.error_count = 0
        self.last_error = None
        
        # 如果之前因错误被禁用，重新激活
        if self.status == AccountStatus.INACTIVE.value and self.error_count == 0:
            self.status = AccountStatus.ACTIVE.value
        
        db.session.commit()

    def to_dict(self, include_sensitive=False):
        """转换为字典"""
        data = {
            'id': self.id,
            'platform': self.platform,
            'username': self.username,
            'email': self.email,
            'status': self.status,
            'vip_level': self.vip_level,
            'expire_date': self.expire_date.isoformat() if self.expire_date else None,
            'total_uses': self.total_uses,
            'daily_uses': self.daily_uses,
            'daily_limit': self.daily_limit,
            'concurrent_limit': self.concurrent_limit,
            'current_concurrent': self.current_concurrent,
            'health_status': self.health_status,
            'error_count': self.error_count,
            'last_used_at': self.last_used_at.isoformat() if self.last_used_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'notes': self.notes
        }
        
        if include_sensitive:
            data['password'] = self.get_password()
            data['phone'] = self.phone
            data['cookies'] = self.get_cookies()
            data['headers'] = self.get_headers()
            data['proxy_config'] = self.get_proxy_config()
        
        return data

    def __repr__(self):
        """字符串表示"""
        return f'<VipAccount {self.platform}:{self.username} ({self.status})>'
