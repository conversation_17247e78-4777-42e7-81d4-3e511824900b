#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户使用统计模型
记录用户的下载次数和API调用次数
"""

import datetime
from backend.main import db


class UserUsage(db.Model):
    """用户使用统计表"""
    __tablename__ = 'user_usage'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    date = db.Column(db.Date, default=datetime.date.today, nullable=False, index=True)
    
    # 下载统计
    download_count = db.Column(db.Integer, default=0, nullable=False)  # 当日下载次数
    
    # API调用统计
    api_call_count = db.Column(db.Integer, default=0, nullable=False)  # 当日API调用次数
    last_api_call = db.Column(db.DateTime)  # 最后一次API调用时间
    
    # 分钟级API调用统计（用于速率限制）
    api_calls_this_minute = db.Column(db.Integer, default=0, nullable=False)
    current_minute = db.Column(db.DateTime)  # 当前分钟的开始时间
    
    # 创建时间
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, nullable=False)

    # 外键关系
    user = db.relationship('User', backref=db.backref('usage_records', lazy='dynamic'))

    # 唯一约束：每个用户每天只有一条记录
    __table_args__ = (
        db.UniqueConstraint('user_id', 'date', name='unique_user_date'),
    )

    @classmethod
    def get_or_create_today_usage(cls, user_id):
        """获取或创建今日使用记录"""
        today = datetime.date.today()
        usage = cls.query.filter_by(user_id=user_id, date=today).first()
        
        if not usage:
            usage = cls(user_id=user_id, date=today)
            db.session.add(usage)
            db.session.commit()
        
        return usage

    def increment_download(self):
        """增加下载次数"""
        self.download_count += 1
        self.updated_at = datetime.datetime.now()
        db.session.commit()

    def increment_api_call(self):
        """增加API调用次数"""
        now = datetime.datetime.now()
        current_minute_start = now.replace(second=0, microsecond=0)
        
        # 检查是否是新的分钟
        if self.current_minute != current_minute_start:
            self.current_minute = current_minute_start
            self.api_calls_this_minute = 0
        
        self.api_call_count += 1
        self.api_calls_this_minute += 1
        self.last_api_call = now
        self.updated_at = now
        db.session.commit()

    def get_remaining_downloads(self, user):
        """获取剩余下载次数"""
        permissions = user.get_permissions()
        limit = permissions.get('download_limit', 0)
        
        if limit == -1:  # 无限制
            return -1
        
        return max(0, limit - self.download_count)

    def get_remaining_api_calls_this_minute(self, user):
        """获取本分钟剩余API调用次数"""
        permissions = user.get_permissions()
        limit = permissions.get('api_rate_limit', 0)
        
        if limit == -1:  # 无限制
            return -1
        
        now = datetime.datetime.now()
        current_minute_start = now.replace(second=0, microsecond=0)
        
        # 如果是新的分钟，重置计数
        if self.current_minute != current_minute_start:
            return limit
        
        return max(0, limit - self.api_calls_this_minute)

    def can_download(self, user):
        """检查是否可以下载"""
        remaining = self.get_remaining_downloads(user)
        return remaining == -1 or remaining > 0

    def can_api_call(self, user):
        """检查是否可以进行API调用"""
        remaining = self.get_remaining_api_calls_this_minute(user)
        return remaining == -1 or remaining > 0

    @classmethod
    def get_user_stats(cls, user_id, days=30):
        """获取用户统计信息"""
        end_date = datetime.date.today()
        start_date = end_date - datetime.timedelta(days=days-1)
        
        records = cls.query.filter(
            cls.user_id == user_id,
            cls.date >= start_date,
            cls.date <= end_date
        ).all()
        
        total_downloads = sum(record.download_count for record in records)
        total_api_calls = sum(record.api_call_count for record in records)
        
        return {
            'total_downloads': total_downloads,
            'total_api_calls': total_api_calls,
            'days': days,
            'daily_records': [
                {
                    'date': record.date.isoformat(),
                    'downloads': record.download_count,
                    'api_calls': record.api_call_count
                }
                for record in records
            ]
        }

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'date': self.date.isoformat(),
            'download_count': self.download_count,
            'api_call_count': self.api_call_count,
            'api_calls_this_minute': self.api_calls_this_minute,
            'last_api_call': self.last_api_call.isoformat() if self.last_api_call else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

    def __repr__(self):
        return f'<UserUsage {self.user_id} {self.date} downloads:{self.download_count} api:{self.api_call_count}>'
