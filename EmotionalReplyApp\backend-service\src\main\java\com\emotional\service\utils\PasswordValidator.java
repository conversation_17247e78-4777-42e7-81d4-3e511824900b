package com.emotional.service.utils;

import java.util.regex.Pattern;

/**
 * 密码验证工具类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
public class PasswordValidator {
    
    // 密码长度限制
    private static final int MIN_LENGTH = 6;
    private static final int MAX_LENGTH = 20;
    
    // 正则表达式模式
    private static final Pattern NUMBER_PATTERN = Pattern.compile("\\d");
    private static final Pattern VALID_CHARS_PATTERN = Pattern.compile("^[A-Za-z\\d!@#$%^&*()_+\\-=\\[\\]{}|;:,.<>?]*$");
    
    /**
     * 验证密码是否符合要求
     * 
     * @param password 密码
     * @return 验证结果
     */
    public static ValidationResult validate(String password) {
        if (password == null || password.trim().isEmpty()) {
            return ValidationResult.error("密码不能为空");
        }
        
        // 检查长度
        if (password.length() < MIN_LENGTH) {
            return ValidationResult.error("密码长度不能少于" + MIN_LENGTH + "位");
        }
        
        if (password.length() > MAX_LENGTH) {
            return ValidationResult.error("密码长度不能超过" + MAX_LENGTH + "位");
        }
        
        // 检查是否包含数字（必须）
        if (!NUMBER_PATTERN.matcher(password).find()) {
            return ValidationResult.error("密码必须包含数字");
        }
        
        // 检查字符是否合法
        if (!VALID_CHARS_PATTERN.matcher(password).matches()) {
            return ValidationResult.error("密码包含不支持的字符，只能包含字母、数字和常用特殊符号");
        }
        
        // 检查前后空格
        if (!password.equals(password.trim())) {
            return ValidationResult.error("密码不能包含前后空格");
        }
        
        return ValidationResult.success("密码符合要求");
    }
    
    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String message;
        
        private ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }
        
        public static ValidationResult success(String message) {
            return new ValidationResult(true, message);
        }
        
        public static ValidationResult error(String message) {
            return new ValidationResult(false, message);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getMessage() {
            return message;
        }
    }
}
