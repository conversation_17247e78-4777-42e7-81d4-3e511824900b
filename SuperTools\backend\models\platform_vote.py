"""
平台投票模型
"""
from backend.main import db
from datetime import datetime


class PlatformVote(db.Model):
    """平台投票模型"""
    __tablename__ = 'platform_votes'
    
    id = db.Column(db.Integer, primary_key=True)
    platform = db.Column(db.String(50), nullable=False, index=True)  # 平台名称 (zhihu, weibo等)
    vote_count = db.Column(db.Integer, default=0, nullable=False)  # 总票数
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 添加唯一约束
    __table_args__ = (
        db.UniqueConstraint('platform', name='uq_platform_vote'),
    )
    
    @classmethod
    def get_or_create(cls, platform):
        """获取或创建平台投票记录"""
        vote = cls.query.filter_by(platform=platform).first()
        if not vote:
            # 设置初始票数
            initial_votes = {
                'zhihu': 18,
                'weibo': 12
            }
            vote = cls(
                platform=platform,
                vote_count=initial_votes.get(platform, 0)
            )
            db.session.add(vote)
            db.session.commit()
        return vote
    
    @classmethod
    def get_vote_count(cls, platform):
        """获取平台票数"""
        vote = cls.query.filter_by(platform=platform).first()
        return vote.vote_count if vote else 0
    
    @classmethod
    def increment_vote(cls, platform):
        """增加票数"""
        vote = cls.get_or_create(platform)
        vote.vote_count += 1
        vote.updated_at = datetime.utcnow()
        db.session.commit()
        return vote.vote_count
    
    @classmethod
    def decrement_vote(cls, platform):
        """减少票数"""
        vote = cls.get_or_create(platform)
        if vote.vote_count > 0:
            vote.vote_count -= 1
            vote.updated_at = datetime.utcnow()
            db.session.commit()
        return vote.vote_count
    
    @classmethod
    def get_all_votes(cls):
        """获取所有平台的票数"""
        votes = cls.query.all()
        return {vote.platform: vote.vote_count for vote in votes}
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'platform': self.platform,
            'vote_count': self.vote_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class UserVote(db.Model):
    """用户投票记录模型"""
    __tablename__ = 'user_votes'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # 允许匿名投票
    platform = db.Column(db.String(50), nullable=False, index=True)
    ip_address = db.Column(db.String(45), nullable=False, index=True)  # 支持IPv6
    user_agent = db.Column(db.Text)  # 浏览器信息
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 添加复合唯一约束，防止重复投票
    __table_args__ = (
        db.UniqueConstraint('user_id', 'platform', name='uq_user_platform_vote'),
        db.UniqueConstraint('ip_address', 'platform', name='uq_ip_platform_vote'),
    )
    
    @classmethod
    def has_voted(cls, platform, user_id=None, ip_address=None):
        """检查是否已投票"""
        query = cls.query.filter_by(platform=platform)
        
        if user_id:
            # 已登录用户，检查用户ID
            query = query.filter_by(user_id=user_id)
        elif ip_address:
            # 未登录用户，检查IP地址
            query = query.filter_by(ip_address=ip_address, user_id=None)
        else:
            return False
            
        return query.first() is not None
    
    @classmethod
    def add_vote(cls, platform, user_id=None, ip_address=None, user_agent=None):
        """添加投票记录"""
        if cls.has_voted(platform, user_id, ip_address):
            return False
            
        vote = cls(
            user_id=user_id,
            platform=platform,
            ip_address=ip_address,
            user_agent=user_agent
        )
        db.session.add(vote)
        db.session.commit()
        return True
    
    @classmethod
    def remove_vote(cls, platform, user_id=None, ip_address=None):
        """移除投票记录"""
        query = cls.query.filter_by(platform=platform)
        
        if user_id:
            query = query.filter_by(user_id=user_id)
        elif ip_address:
            query = query.filter_by(ip_address=ip_address, user_id=None)
        else:
            return False
            
        vote = query.first()
        if vote:
            db.session.delete(vote)
            db.session.commit()
            return True
        return False
    
    @classmethod
    def get_user_votes(cls, user_id=None, ip_address=None):
        """获取用户的所有投票"""
        query = cls.query
        
        if user_id:
            query = query.filter_by(user_id=user_id)
        elif ip_address:
            query = query.filter_by(ip_address=ip_address, user_id=None)
        else:
            return []
            
        votes = query.all()
        return [vote.platform for vote in votes]
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'platform': self.platform,
            'ip_address': self.ip_address,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
