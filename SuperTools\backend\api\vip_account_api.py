#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
VIP账号管理API
提供VIP账号的增删改查和状态管理功能
"""

import logging
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user

from ..models.vip_account import VipAccount, AccountStatus
from ..utils.vip_account_pool import vip_account_pool
from ..utils.permissions import require_permission
from ..main import db

# 创建日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
vip_account_api = Blueprint('vip_account_api', __name__, url_prefix='/vip-accounts')


@vip_account_api.route('/', methods=['GET'])
@login_required
@require_permission('admin', 'user_management')
def list_accounts():
    """
    获取VIP账号列表
    
    查询参数:
        platform: 平台名称 (可选)
        status: 账号状态 (可选)
        page: 页码 (默认1)
        per_page: 每页数量 (默认20)
    
    响应:
        {
            "success": true,
            "message": "获取成功",
            "data": {
                "accounts": [...],
                "total": 100,
                "page": 1,
                "per_page": 20,
                "pages": 5
            }
        }
    """
    try:
        # 获取查询参数
        platform = request.args.get('platform')
        status = request.args.get('status')
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)  # 限制最大每页数量
        
        # 构建查询
        query = VipAccount.query
        
        if platform:
            query = query.filter(VipAccount.platform == platform)
        
        if status:
            query = query.filter(VipAccount.status == status)
        
        # 分页查询
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        # 转换为字典
        accounts = [account.to_dict() for account in pagination.items]
        
        return jsonify({
            "success": True,
            "message": "获取成功",
            "data": {
                "accounts": accounts,
                "total": pagination.total,
                "page": page,
                "per_page": per_page,
                "pages": pagination.pages
            }
        })
        
    except Exception as e:
        logger.error(f"获取VIP账号列表失败: {e}")
        return jsonify({
            "success": False,
            "message": f"获取失败: {str(e)}",
            "data": None
        }), 500


@vip_account_api.route('/', methods=['POST'])
@login_required
@require_permission('admin', 'user_management')
def create_account():
    """
    创建VIP账号
    
    请求体:
        {
            "platform": "csdn",
            "username": "test_user",
            "password": "password123",
            "email": "<EMAIL>",
            "phone": "***********",
            "vip_level": "CSDN会员",
            "expire_date": "2024-12-31T23:59:59",
            "daily_limit": 100,
            "concurrent_limit": 3,
            "notes": "备注信息"
        }
    
    响应:
        {
            "success": true,
            "message": "创建成功",
            "data": {
                "id": 1,
                "platform": "csdn",
                "username": "test_user",
                ...
            }
        }
    """
    try:
        if not request.is_json:
            return jsonify({
                "success": False,
                "message": "请求格式错误，需要JSON格式",
                "data": None
            }), 400
        
        data = request.json
        
        # 验证必要字段
        required_fields = ['platform', 'username', 'password']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    "success": False,
                    "message": f"缺少必要字段: {field}",
                    "data": None
                }), 400
        
        # 检查账号是否已存在
        existing_account = VipAccount.query.filter_by(
            platform=data['platform'],
            username=data['username']
        ).first()
        
        if existing_account:
            return jsonify({
                "success": False,
                "message": "该平台上的用户名已存在",
                "data": None
            }), 400
        
        # 处理日期字段
        expire_date = None
        if data.get('expire_date'):
            from datetime import datetime
            try:
                expire_date = datetime.fromisoformat(data['expire_date'].replace('Z', '+00:00'))
            except ValueError:
                return jsonify({
                    "success": False,
                    "message": "日期格式错误",
                    "data": None
                }), 400
        
        # 创建账号
        account = vip_account_pool.add_account(
            platform=data['platform'],
            username=data['username'],
            password=data['password'],
            created_by=current_user.id,
            email=data.get('email'),
            phone=data.get('phone'),
            vip_level=data.get('vip_level'),
            expire_date=expire_date,
            daily_limit=data.get('daily_limit', 100),
            concurrent_limit=data.get('concurrent_limit', 3),
            notes=data.get('notes')
        )
        
        return jsonify({
            "success": True,
            "message": "创建成功",
            "data": account.to_dict()
        })
        
    except Exception as e:
        logger.error(f"创建VIP账号失败: {e}")
        return jsonify({
            "success": False,
            "message": f"创建失败: {str(e)}",
            "data": None
        }), 500


@vip_account_api.route('/<int:account_id>', methods=['GET'])
@login_required
@require_permission('admin', 'user_management')
def get_account(account_id):
    """获取单个VIP账号详情"""
    try:
        account = VipAccount.query.get(account_id)
        if not account:
            return jsonify({
                "success": False,
                "message": "账号不存在",
                "data": None
            }), 404
        
        return jsonify({
            "success": True,
            "message": "获取成功",
            "data": account.to_dict(include_sensitive=True)
        })
        
    except Exception as e:
        logger.error(f"获取VIP账号详情失败: {e}")
        return jsonify({
            "success": False,
            "message": f"获取失败: {str(e)}",
            "data": None
        }), 500


@vip_account_api.route('/<int:account_id>', methods=['PUT'])
@login_required
@require_permission('admin', 'user_management')
def update_account(account_id):
    """更新VIP账号"""
    try:
        account = VipAccount.query.get(account_id)
        if not account:
            return jsonify({
                "success": False,
                "message": "账号不存在",
                "data": None
            }), 404
        
        if not request.is_json:
            return jsonify({
                "success": False,
                "message": "请求格式错误，需要JSON格式",
                "data": None
            }), 400
        
        data = request.json
        
        # 更新字段
        updatable_fields = [
            'email', 'phone', 'vip_level', 'daily_limit', 
            'concurrent_limit', 'notes', 'status'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(account, field, data[field])
        
        # 处理密码更新
        if data.get('password'):
            account.set_password(data['password'])
        
        # 处理日期字段
        if data.get('expire_date'):
            from datetime import datetime
            try:
                account.expire_date = datetime.fromisoformat(data['expire_date'].replace('Z', '+00:00'))
            except ValueError:
                return jsonify({
                    "success": False,
                    "message": "日期格式错误",
                    "data": None
                }), 400
        
        db.session.commit()
        
        # 刷新账号池
        vip_account_pool._refresh_pool(account.platform)
        
        return jsonify({
            "success": True,
            "message": "更新成功",
            "data": account.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新VIP账号失败: {e}")
        return jsonify({
            "success": False,
            "message": f"更新失败: {str(e)}",
            "data": None
        }), 500


@vip_account_api.route('/<int:account_id>', methods=['DELETE'])
@login_required
@require_permission('admin', 'user_management')
def delete_account(account_id):
    """删除VIP账号"""
    try:
        vip_account_pool.remove_account(account_id)
        
        return jsonify({
            "success": True,
            "message": "删除成功",
            "data": None
        })
        
    except Exception as e:
        logger.error(f"删除VIP账号失败: {e}")
        return jsonify({
            "success": False,
            "message": f"删除失败: {str(e)}",
            "data": None
        }), 500


@vip_account_api.route('/pool-status', methods=['GET'])
@login_required
@require_permission('admin', 'user_management')
def get_pool_status():
    """获取账号池状态"""
    try:
        platform = request.args.get('platform', 'csdn')
        status = vip_account_pool.get_pool_status(platform)
        
        return jsonify({
            "success": True,
            "message": "获取成功",
            "data": status
        })
        
    except Exception as e:
        logger.error(f"获取账号池状态失败: {e}")
        return jsonify({
            "success": False,
            "message": f"获取失败: {str(e)}",
            "data": None
        }), 500


@vip_account_api.route('/health-check', methods=['POST'])
@login_required
@require_permission('admin', 'user_management')
def health_check():
    """执行健康检查"""
    try:
        platform = request.json.get('platform') if request.is_json else None
        vip_account_pool.health_check(platform)
        
        return jsonify({
            "success": True,
            "message": "健康检查已启动",
            "data": None
        })
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return jsonify({
            "success": False,
            "message": f"健康检查失败: {str(e)}",
            "data": None
        }), 500
