package com.emotional.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 应用版本实体类
 */
@Data
@TableName("app_versions")
public class AppVersion {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 版本名称 (如: 1.0.0)
     */
    private String versionName;
    
    /**
     * 版本号 (用于比较大小，如: 100)
     */
    private Integer versionCode;
    
    /**
     * 平台 (android, ios, h5)
     */
    private String platform;
    
    /**
     * 更新内容描述
     */
    private String updateContent;
    
    /**
     * 下载链接
     */
    private String downloadUrl;
    
    /**
     * 文件大小 (如: 25.6MB)
     */
    private String fileSize;
    
    /**
     * 是否强制更新 (0: 否, 1: 是)
     */
    private Boolean isForceUpdate;
    
    /**
     * 版本状态 (draft: 草稿, published: 已发布, unpublished: 已下架)
     */
    private String status;
    
    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime releaseDate;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 创建者
     */
    private String createdBy;
    
    /**
     * 更新者
     */
    private String updatedBy;
    
    /**
     * 备注
     */
    private String remark;
}
