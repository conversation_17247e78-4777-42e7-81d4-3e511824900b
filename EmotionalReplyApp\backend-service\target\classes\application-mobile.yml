# 真机调试环境配置
# 使用方式：java -jar app.jar --spring.profiles.active=mobile

server:
  port: 8080
  # 监听所有网络接口，允许外部设备访问
  address: 0.0.0.0
  servlet:
    context-path: /api

# 数据库配置（如果需要外部访问数据库）
spring:
  datasource:
    # 如果数据库在其他机器上，修改这里的IP
    url: ************************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}

# 日志配置 - 真机调试时可能需要更详细的日志
logging:
  level:
    com.emotional.service: DEBUG
    org.springframework.web: INFO
    # 网络相关日志
    org.springframework.web.servlet.DispatcherServlet: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 应用自定义配置
app:
  # 开发模式配置
  debug: true
