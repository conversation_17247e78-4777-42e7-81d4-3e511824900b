#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
邮件服务类
用于发送邮件
"""

import logging
import os
import smtplib
import ssl
from email.header import Header
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import List, Optional, Tuple, Union

logger = logging.getLogger(__name__)

class QQMailer:
    """QQ邮件发送器"""

    def __init__(self, sender: str, auth_code: str):
        """
        初始化QQ邮件发送器

        Args:
            sender: 发件人QQ邮箱地址
            auth_code: QQ邮箱授权码
        """
        self.sender = sender
        self.auth_code = auth_code
        self.smtp_server = "smtp.qq.com"
        self.smtp_port = 465

        logger.info(f"初始化QQ邮件服务: {self.sender}")

    def send_mail(self, receiver_email: str, subject: str, body: str,
                 attachment_path: Optional[str] = None) -> bool:
        """
        发送邮件

        Args:
            receiver_email: 收件人邮箱地址
            subject: 邮件主题
            body: 邮件正文
            attachment_path: 附件路径，可选

        Returns:
            成功返回True，失败返回False
        """
        try:
            # 创建邮件对象
            msg = MIMEMultipart()
            msg['From'] = self.sender
            msg['To'] = receiver_email
            msg['Subject'] = Header(subject, 'utf-8')

            # 添加正文
            msg.attach(MIMEText(body, 'plain', 'utf-8'))

            # 添加附件
            if attachment_path and os.path.exists(attachment_path):
                attachment_name = os.path.basename(attachment_path)
                attachment_size = os.path.getsize(attachment_path)
                logger.info(f"正在添加附件: {attachment_name} (大小: {attachment_size} 字节)")

                try:
                    with open(attachment_path, 'rb') as f:
                        attachment = MIMEApplication(f.read())
                        attachment.add_header('Content-Disposition', 'attachment',
                                             filename=('utf-8', '', attachment_name))
                        msg.attach(attachment)
                    logger.info(f"附件 {attachment_name} 添加成功")
                except Exception as attach_error:
                    logger.error(f"添加附件时发生错误: {attach_error}")
                    return False

            # 创建SMTP连接
            try:
                # 使用SSL连接
                context = ssl.create_default_context()
                logger.info(f"正在连接到SMTP服务器: {self.smtp_server}:{self.smtp_port}")

                with smtplib.SMTP_SSL(self.smtp_server, self.smtp_port, context=context) as server:
                    logger.info(f"正在登录邮箱: {self.sender}")
                    server.login(self.sender, self.auth_code)
                    logger.info(f"正在发送邮件到: {receiver_email}")

                    # 发送邮件
                    send_result = server.send_message(msg)

                    # 检查发送结果
                    if send_result:
                        # 如果有拒绝的收件人，记录警告但不算失败
                        logger.warning(f"部分收件人被拒绝: {send_result}")

                    logger.info(f"邮件已成功发送至 {receiver_email}")

                # 如果能执行到这里，说明邮件发送成功
                return True

            except smtplib.SMTPAuthenticationError as auth_error:
                logger.error(f"SMTP认证失败: {auth_error}")
                return False

            except smtplib.SMTPRecipientsRefused as recipients_error:
                logger.error(f"收件人被拒绝: {recipients_error}")
                return False

            except smtplib.SMTPException as smtp_error:
                # 检查是否是连接关闭时的错误
                error_str = str(smtp_error)
                if "(-1, b'\\x00\\x00\\x00')" in error_str or "Connection unexpectedly closed" in error_str:
                    # 这种错误通常发生在邮件已发送成功后的连接关闭阶段
                    logger.warning(f"SMTP连接关闭时出现错误，但邮件可能已发送成功: {smtp_error}")
                    return True  # 认为发送成功
                else:
                    logger.error(f"SMTP错误: {smtp_error}")
                    return False

            except Exception as general_error:
                logger.error(f"发送邮件时发生未知错误: {general_error}")
                return False

        except Exception as e:
            logger.error(f"发送邮件失败: {e}")
            return False

    def send_html_email(self, to_email: str, subject: str, html_content: str) -> bool:
        """
        发送HTML邮件（无附件）

        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            html_content: HTML邮件内容

        Returns:
            bool: 发送成功返回True，失败返回False
        """
        try:
            # 创建邮件对象
            msg = MIMEMultipart()
            msg['From'] = self.sender
            msg['To'] = to_email
            msg['Subject'] = Header(subject, 'utf-8')

            # 添加HTML正文
            msg.attach(MIMEText(html_content, 'html', 'utf-8'))

            # 创建SMTP连接并发送
            try:
                context = ssl.create_default_context()
                logger.info(f"正在连接到SMTP服务器: {self.smtp_server}:{self.smtp_port}")

                with smtplib.SMTP_SSL(self.smtp_server, self.smtp_port, context=context) as server:
                    logger.info(f"正在登录邮箱: {self.sender}")
                    server.login(self.sender, self.auth_code)
                    logger.info(f"正在发送HTML邮件到: {to_email}")

                    # 发送邮件
                    send_result = server.send_message(msg)

                    # 检查发送结果
                    if send_result:
                        logger.warning(f"部分收件人被拒绝: {send_result}")

                logger.info(f"HTML邮件已成功发送至 {to_email}")
                return True

            except smtplib.SMTPAuthenticationError as auth_error:
                logger.error(f"SMTP认证失败: {auth_error}")
                return False

            except smtplib.SMTPRecipientsRefused as recipients_error:
                logger.error(f"收件人被拒绝: {recipients_error}")
                return False

            except smtplib.SMTPException as smtp_error:
                # 检查是否是连接关闭时的错误
                error_str = str(smtp_error)
                if "(-1, b'\\x00\\x00\\x00')" in error_str or "Connection unexpectedly closed" in error_str:
                    logger.warning(f"SMTP连接关闭时出现错误，但邮件可能已发送成功: {smtp_error}")
                    return True  # 认为发送成功
                else:
                    logger.error(f"SMTP错误: {smtp_error}")
                    return False

            except Exception as general_error:
                logger.error(f"发送HTML邮件时发生未知错误: {general_error}")
                return False

        except Exception as e:
            logger.error(f"发送HTML邮件失败: {e}")
            return False

    def send_email_with_attachment(self, to_email: str, subject: str,
                                 html_content: str, attachment_path: str,
                                 attachment_name: Optional[str] = None) -> bool:
        """
        发送带附件的HTML邮件

        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            html_content: HTML邮件内容
            attachment_path: 附件文件路径
            attachment_name: 自定义附件名称，如果为None则使用文件名

        Returns:
            bool: 发送成功返回True，失败返回False
        """
        try:
            # 创建邮件对象
            msg = MIMEMultipart()
            msg['From'] = self.sender
            msg['To'] = to_email
            msg['Subject'] = Header(subject, 'utf-8')

            # 添加HTML正文
            msg.attach(MIMEText(html_content, 'html', 'utf-8'))

            # 添加附件
            if attachment_path and os.path.exists(attachment_path):
                file_name = attachment_name or os.path.basename(attachment_path)
                file_size = os.path.getsize(attachment_path)
                logger.info(f"正在添加附件: {file_name} (大小: {file_size} 字节)")

                try:
                    with open(attachment_path, 'rb') as f:
                        attachment = MIMEApplication(f.read())
                        attachment.add_header(
                            'Content-Disposition',
                            'attachment',
                            filename=('utf-8', '', file_name)
                        )
                        msg.attach(attachment)
                    logger.info(f"附件 {file_name} 添加成功")
                except Exception as attach_error:
                    logger.error(f"添加附件时发生错误: {attach_error}")
                    return False
            else:
                logger.error(f"附件文件不存在: {attachment_path}")
                return False

            # 创建SMTP连接并发送
            try:
                context = ssl.create_default_context()
                logger.info(f"正在连接到SMTP服务器: {self.smtp_server}:{self.smtp_port}")

                with smtplib.SMTP_SSL(self.smtp_server, self.smtp_port, context=context) as server:
                    logger.info(f"正在登录邮箱: {self.sender}")
                    server.login(self.sender, self.auth_code)
                    logger.info(f"正在发送邮件到: {to_email}")

                    # 发送邮件
                    send_result = server.send_message(msg)

                    # 检查发送结果
                    if send_result:
                        logger.warning(f"部分收件人被拒绝: {send_result}")

                logger.info(f"HTML邮件已成功发送至 {to_email}")
                return True

            except smtplib.SMTPAuthenticationError as auth_error:
                logger.error(f"SMTP认证失败: {auth_error}")
                return False

            except smtplib.SMTPRecipientsRefused as recipients_error:
                logger.error(f"收件人被拒绝: {recipients_error}")
                return False

            except smtplib.SMTPException as smtp_error:
                # 检查是否是连接关闭时的错误
                error_str = str(smtp_error)
                if "(-1, b'\\x00\\x00\\x00')" in error_str or "Connection unexpectedly closed" in error_str:
                    logger.warning(f"SMTP连接关闭时出现错误，但邮件可能已发送成功: {smtp_error}")
                    return True  # 认为发送成功
                else:
                    logger.error(f"SMTP错误: {smtp_error}")
                    return False

            except Exception as general_error:
                logger.error(f"发送HTML邮件时发生未知错误: {general_error}")
                return False

        except Exception as e:
            logger.error(f"发送HTML邮件失败: {e}")
            return False

# 为了兼容性，创建EmailSender别名
EmailSender = QQMailer