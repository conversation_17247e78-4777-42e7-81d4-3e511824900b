package com.emotional.service.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 生成激活码请求
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
public class GenerateCodeRequest {
    
    /**
     * 激活码类型
     */
    @NotBlank(message = "激活码类型不能为空")
    private String codeType;
    
    /**
     * 有效天数
     */
    @NotNull(message = "有效天数不能为空")
    private Integer durationDays;
    
    /**
     * 管理员ID
     */
    @NotNull(message = "管理员ID不能为空")
    private Long adminId;
    
    /**
     * 备注信息
     */
    private String remark;
}
