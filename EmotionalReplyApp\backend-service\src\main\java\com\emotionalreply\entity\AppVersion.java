package com.emotionalreply.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * 应用版本实体
 */
@ApiModel("应用版本")
@TableName("app_versions")
public class AppVersion {

    @ApiModelProperty("版本ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("版本名称")
    @TableField("version_name")
    private String versionName;

    @ApiModelProperty("版本号")
    @TableField("version_code")
    private Integer versionCode;

    @ApiModelProperty("平台类型")
    @TableField("platform")
    private String platform;

    @ApiModelProperty("更新内容")
    @TableField("update_content")
    private String updateContent;

    @ApiModelProperty("下载链接")
    @TableField("download_url")
    private String downloadUrl;

    @ApiModelProperty("文件大小")
    @TableField("file_size")
    private String fileSize;

    @ApiModelProperty("是否强制更新")
    @TableField("is_force_update")
    private Boolean isForceUpdate;

    @ApiModelProperty("最低支持版本")
    @TableField("min_support_version")
    private String minSupportVersion;

    @ApiModelProperty("发布状态")
    @TableField("status")
    private String status;

    @ApiModelProperty("发布时间")
    @TableField("release_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime releaseDate;

    @ApiModelProperty("创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @ApiModelProperty("更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    // 构造函数
    public AppVersion() {}

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public Integer getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(Integer versionCode) {
        this.versionCode = versionCode;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getUpdateContent() {
        return updateContent;
    }

    public void setUpdateContent(String updateContent) {
        this.updateContent = updateContent;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public String getFileSize() {
        return fileSize;
    }

    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }

    public Boolean getIsForceUpdate() {
        return isForceUpdate;
    }

    public void setIsForceUpdate(Boolean isForceUpdate) {
        this.isForceUpdate = isForceUpdate;
    }

    public String getMinSupportVersion() {
        return minSupportVersion;
    }

    public void setMinSupportVersion(String minSupportVersion) {
        this.minSupportVersion = minSupportVersion;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getReleaseDate() {
        return releaseDate;
    }

    public void setReleaseDate(LocalDateTime releaseDate) {
        this.releaseDate = releaseDate;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "AppVersion{" +
                "id=" + id +
                ", versionName='" + versionName + '\'' +
                ", versionCode=" + versionCode +
                ", platform='" + platform + '\'' +
                ", updateContent='" + updateContent + '\'' +
                ", downloadUrl='" + downloadUrl + '\'' +
                ", fileSize='" + fileSize + '\'' +
                ", isForceUpdate=" + isForceUpdate +
                ", minSupportVersion='" + minSupportVersion + '\'' +
                ", status='" + status + '\'' +
                ", releaseDate=" + releaseDate +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
