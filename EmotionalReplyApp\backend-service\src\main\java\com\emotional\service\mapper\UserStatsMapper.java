package com.emotional.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.emotional.service.entity.UserStats;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDate;

/**
 * 用户统计Mapper接口
 */
@Mapper
public interface UserStatsMapper extends BaseMapper<UserStats> {

    /**
     * 获取用户指定日期的统计信息
     */
    @Select("SELECT * FROM user_stats WHERE user_id = #{userId} AND stat_date = #{statDate}")
    UserStats getUserStatsByDate(@Param("userId") Long userId, @Param("statDate") LocalDate statDate);

    /**
     * 获取用户总使用次数
     */
    @Select("SELECT COALESCE(SUM(daily_usage), 0) FROM user_stats WHERE user_id = #{userId}")
    Integer getTotalUsageByUserId(@Param("userId") Long userId);

    /**
     * 增加用户当日使用次数
     */
    @Update("UPDATE user_stats SET daily_usage = daily_usage + 1, updated_time = NOW() " +
            "WHERE user_id = #{userId} AND stat_date = #{statDate}")
    int incrementDailyUsage(@Param("userId") Long userId, @Param("statDate") LocalDate statDate);

    /**
     * 获取用户当日使用次数
     */
    @Select("SELECT COALESCE(daily_usage, 0) FROM user_stats WHERE user_id = #{userId} AND stat_date = #{statDate}")
    Integer getDailyUsage(@Param("userId") Long userId, @Param("statDate") LocalDate statDate);
}
