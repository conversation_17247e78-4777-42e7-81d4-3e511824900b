#!/bin/bash

# 后端部署脚本
# 使用方法: ./backend-deploy.sh [环境] [版本]
# 示例: ./backend-deploy.sh prod 1.0.0

ENVIRONMENT=${1:-dev}
VERSION=${2:-latest}
APP_NAME="emotional-reply-service"
JAR_NAME="${APP_NAME}-${VERSION}.jar"
DEPLOY_DIR="/opt/emotional-reply"
LOG_DIR="/var/log/emotional-reply"

echo "🚀 开始部署后端服务..."
echo "环境: $ENVIRONMENT"
echo "版本: $VERSION"

# 1. 创建部署目录
sudo mkdir -p $DEPLOY_DIR
sudo mkdir -p $LOG_DIR

# 2. 停止现有服务
echo "⏹️ 停止现有服务..."
sudo systemctl stop $APP_NAME || true

# 3. 备份当前版本
if [ -f "$DEPLOY_DIR/$JAR_NAME" ]; then
    echo "📦 备份当前版本..."
    sudo cp "$DEPLOY_DIR/$JAR_NAME" "$DEPLOY_DIR/${JAR_NAME}.backup.$(date +%Y%m%d_%H%M%S)"
fi

# 4. 复制新版本
echo "📁 复制新版本..."
sudo cp "target/$JAR_NAME" "$DEPLOY_DIR/"

# 5. 设置权限
sudo chown -R emotional-reply:emotional-reply $DEPLOY_DIR
sudo chown -R emotional-reply:emotional-reply $LOG_DIR

# 6. 更新配置文件
echo "⚙️ 更新配置文件..."
sudo cp "deploy/application-${ENVIRONMENT}.yml" "$DEPLOY_DIR/"

# 7. 启动服务
echo "▶️ 启动服务..."
sudo systemctl start $APP_NAME
sudo systemctl enable $APP_NAME

# 8. 检查服务状态
sleep 10
if sudo systemctl is-active --quiet $APP_NAME; then
    echo "✅ 服务启动成功!"
    echo "📊 服务状态:"
    sudo systemctl status $APP_NAME --no-pager -l
    
    # 健康检查
    echo "🔍 健康检查..."
    for i in {1..30}; do
        if curl -f http://localhost:8080/api/actuator/health > /dev/null 2>&1; then
            echo "✅ 健康检查通过!"
            break
        fi
        echo "⏳ 等待服务启动... ($i/30)"
        sleep 2
    done
else
    echo "❌ 服务启动失败!"
    echo "📋 错误日志:"
    sudo journalctl -u $APP_NAME --no-pager -l -n 50
    exit 1
fi

echo "🎉 后端部署完成!"
