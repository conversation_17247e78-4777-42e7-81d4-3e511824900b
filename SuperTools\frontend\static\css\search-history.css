/**
 * SuperSpider 搜索历史记录样式
 */

/* 搜索记录模态框 */
#search-history-modal .modal-container {
    max-width: 90%;
    width: 1200px;
}

#search-history-modal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#search-history-modal .modal-header-actions {
    display: flex;
    gap: 10px;
}

/* 搜索统计 */
.search-stats {
    display: flex;
    justify-content: space-around;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-value {
    display: block;
    font-size: 1.8rem;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 4px;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
}

/* 工具栏 */
.search-history-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.selected-count {
    font-size: 14px;
    color: #666;
}

.batch-actions {
    display: flex;
    gap: 8px;
}

.batch-btn {
    background: none;
    border: 1px solid #dee2e6;
    color: #666;
    cursor: pointer;
    font-size: 14px;
    padding: 6px 10px;
    border-radius: 4px;
    transition: all 0.2s;
}

.batch-btn:hover:not(.disabled) {
    background-color: #3498db;
    color: white;
    border-color: #3498db;
}

.batch-btn.disabled {
    color: #ccc;
    cursor: not-allowed;
    opacity: 0.5;
}

.toolbar-right {
    display: flex;
    align-items: center;
}

.sort-select {
    padding: 6px 10px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    background: white;
}

.view-mode-switcher {
    display: flex;
    gap: 5px;
}

.view-mode-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 16px;
    padding: 5px 8px;
    border-radius: 4px;
    transition: all 0.2s;
}

.view-mode-btn:hover {
    background-color: #e3f2fd;
}

.view-mode-btn.active {
    color: #3498db;
    background-color: #e3f2fd;
}

.sort-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.sort-container label {
    font-size: 14px;
    color: #666;
}

.sort-container select {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* 筛选表单 */
.filter-container {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.filter-form {
    display: flex;
    align-items: center;
}

.filter-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    gap: 20px;
}

.filter-left {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    flex: 1;
}

.filter-right {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.filter-row select {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    min-width: 120px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.filter-row .btn {
    padding: 8px 20px;
    font-size: 14px;
    border-radius: 6px;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    min-width: 80px;
}

.filter-row .btn-primary {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    border-color: #9b59b6;
    box-shadow: 0 2px 4px rgba(155, 89, 182, 0.3);
}

.filter-row .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(155, 89, 182, 0.4);
    background: linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%);
}

.filter-row .btn-secondary {
    background: #f8f9fa;
    color: #6c757d;
    border-color: #dee2e6;
}

.filter-row .btn-secondary:hover {
    background: #e9ecef;
    color: #495057;
    border-color: #adb5bd;
}

/* 搜索记录列表 */
.search-records-list-container {
    margin-top: 20px;
}

/* 表格视图 */
.search-records-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.search-records-table th,
.search-records-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.search-records-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.search-records-table tr:hover {
    background-color: #f5f5f5;
}

.search-records-table .checkbox-column {
    width: 40px;
    text-align: center;
}

.search-records-table .search-record-title {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.search-records-table tr.status-completed {
    border-left: 3px solid #2ecc71;
}

.search-records-table tr.status-pending {
    border-left: 3px solid #f39c12;
}

.search-records-table tr.status-failed {
    border-left: 3px solid #e74c3c;
}

.search-records-table tr.is-favorite {
    background-color: #fff8e1;
}

.search-records-table tr.is-favorite:hover {
    background-color: #fff5d6;
}

/* 网格视图 */
.search-records-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.search-record-card {
    border: 1px solid #eee;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    position: relative;
}

.search-record-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.search-record-card.status-completed {
    border-top: 3px solid #2ecc71;
}

.search-record-card.status-pending {
    border-top: 3px solid #f39c12;
}

.search-record-card.status-failed {
    border-top: 3px solid #e74c3c;
}

.search-record-card.is-favorite {
    background-color: #fff8e1;
}

.search-record-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f8f9fa;
}

.search-record-checkbox-container {
    z-index: 2;
}

.search-record-card-platform {
    font-size: 12px;
    color: #666;
    padding: 3px 8px;
    background-color: #eee;
    border-radius: 4px;
}

.search-record-card-favorite {
    color: #f39c12;
}

.search-record-card-thumbnail {
    height: 150px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.search-record-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.search-record-thumbnail-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #aaa;
    font-size: 48px;
}

.search-record-card-content {
    padding: 15px;
}

.search-record-card-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    max-height: 44px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.search-record-card-author {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.search-record-card-meta {
    display: flex;
    gap: 10px;
    margin-bottom: 8px;
}

.search-record-card-type,
.search-record-card-status {
    font-size: 12px;
}

.search-record-card-date {
    font-size: 12px;
    color: #999;
    margin-bottom: 8px;
}

.search-record-card-notes-indicator {
    font-size: 12px;
    color: #3498db;
    margin-top: 5px;
}

.search-record-card-actions {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-top: 1px solid #eee;
}

/* 搜索记录操作按钮 */
.search-record-actions {
    display: flex;
    gap: 8px;
}

.search-record-btn,
.action-btn {
    background: none;
    border: none;
    color: #3498db;
    cursor: pointer;
    font-size: 16px;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s;
}

.search-record-btn:hover,
.action-btn:hover {
    background-color: #e3f2fd;
}

.search-record-btn.disabled {
    color: #ccc;
    cursor: not-allowed;
}

.action-btn.favorite-btn.active {
    color: #f39c12;
}

.action-btn.notes-btn.has-notes {
    color: #27ae60;
}

.action-btn.delete-btn {
    color: #e74c3c;
}

.action-btn.delete-btn:hover {
    background-color: #ffebee;
}

/* 状态标签 */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.status-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.status-failed {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-badge.status-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
}

.pagination-info {
    font-size: 14px;
    color: #666;
}

.pagination-links {
    display: flex;
    gap: 5px;
}

.page-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    padding: 0 8px;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #ddd;
    color: #333;
    text-decoration: none;
    transition: all 0.2s;
}

.page-link:hover {
    background-color: #f5f5f5;
}

.page-link.active {
    background-color: #3498db;
    border-color: #3498db;
    color: #fff;
}

.page-link.disabled {
    color: #ccc;
    cursor: not-allowed;
}

/* 加载中和空状态 */
.loading-container,
.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;
    color: #999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-container i {
    font-size: 48px;
    margin-bottom: 15px;
    color: #ddd;
}

/* 错误消息 */
.error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30px;
    color: #e74c3c;
    text-align: center;
}

.error-message i {
    font-size: 48px;
    margin-bottom: 15px;
}

/* 笔记模态框 */
#notes-modal .modal-container {
    max-width: 600px;
}

.notes-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

#notes-content {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: vertical;
    font-family: inherit;
    font-size: 14px;
}

/* Toast 消息 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s;
    min-width: 250px;
    max-width: 350px;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.success {
    border-left: 4px solid #2ecc71;
}

.toast.error {
    border-left: 4px solid #e74c3c;
}

.toast.warning {
    border-left: 4px solid #f39c12;
}

.toast.info {
    border-left: 4px solid #3498db;
}

.toast-icon {
    margin-right: 10px;
    font-size: 18px;
}

.toast.success .toast-icon {
    color: #2ecc71;
}

.toast.error .toast-icon {
    color: #e74c3c;
}

.toast.warning .toast-icon {
    color: #f39c12;
}

.toast.info .toast-icon {
    color: #3498db;
}

.toast-message {
    font-size: 14px;
    color: #333;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .filter-form {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
    }

    .search-records-toolbar {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .search-records-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}
