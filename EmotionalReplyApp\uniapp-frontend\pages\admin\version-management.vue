<template>
  <view class="version-management">
    <!-- 顶部操作栏 -->
    <view class="top-actions">
      <button class="btn-primary" @click="showPublishDialog">发布新版本</button>
      <button class="btn-secondary" @click="refreshList">刷新列表</button>
    </view>

    <!-- 筛选器 -->
    <view class="filters">
      <view class="filter-item">
        <text class="filter-label">平台:</text>
        <picker :value="filterPlatform" :range="platformOptions" range-key="label" @change="onPlatformChange">
          <view class="picker-value">{{ platformOptions[filterPlatform].label }}</view>
        </picker>
      </view>
      <view class="filter-item">
        <text class="filter-label">状态:</text>
        <picker :value="filterStatus" :range="statusOptions" range-key="label" @change="onStatusChange">
          <view class="picker-value">{{ statusOptions[filterStatus].label }}</view>
        </picker>
      </view>
    </view>

    <!-- 版本列表 -->
    <view class="version-list" v-if="!loading">
      <view class="version-item" v-for="version in versions" :key="version.id">
        <view class="version-header">
          <view class="version-info">
            <text class="version-name">{{ version.versionName }}</text>
            <text class="version-platform">{{ version.platform }}</text>
          </view>
          <view class="version-status" :class="getStatusClass(version.status)">
            {{ getStatusText(version.status) }}
          </view>
        </view>
        
        <view class="version-details">
          <text class="version-code">版本号: {{ version.versionCode }}</text>
          <text class="version-date">发布时间: {{ formatDate(version.releaseDate) }}</text>
          <text class="version-force" v-if="version.isForceUpdate">强制更新</text>
        </view>
        
        <view class="version-content">
          <text class="content-title">更新内容:</text>
          <text class="content-text">{{ version.updateContent }}</text>
        </view>
        
        <view class="version-actions">
          <button class="btn-small btn-info" @click="viewVersion(version)">查看详情</button>
          <button class="btn-small btn-warning" @click="unpublishVersion(version)" v-if="version.status === 'published'">下架</button>
          <button class="btn-small btn-success" @click="republishVersion(version)" v-if="version.status === 'unpublished'">重新发布</button>
          <button class="btn-small btn-danger" @click="deleteVersion(version)">删除</button>
        </view>
      </view>
      
      <view class="empty-state" v-if="versions.length === 0">
        <text class="empty-text">暂无版本数据</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" v-if="loading">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 发布新版本对话框 -->
    <uni-popup ref="publishPopup" type="center" :mask-click="false">
      <view class="publish-dialog">
        <view class="dialog-header">
          <text class="dialog-title">发布新版本</text>
          <text class="dialog-close" @click="closePublishDialog">×</text>
        </view>
        
        <view class="dialog-content">
          <view class="form-item">
            <text class="form-label">版本名称:</text>
            <input class="form-input" v-model="newVersion.versionName" placeholder="如: 1.1.0" />
          </view>
          
          <view class="form-item">
            <text class="form-label">版本号:</text>
            <input class="form-input" v-model.number="newVersion.versionCode" type="number" placeholder="如: 110" />
          </view>
          
          <view class="form-item">
            <text class="form-label">平台:</text>
            <picker :value="newVersion.platformIndex" :range="publishPlatforms" range-key="label" @change="onNewPlatformChange">
              <view class="picker-value">{{ publishPlatforms[newVersion.platformIndex].label }}</view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">下载地址:</text>
            <input class="form-input" v-model="newVersion.downloadUrl" placeholder="下载链接或在线使用" />
          </view>
          
          <view class="form-item">
            <text class="form-label">文件大小:</text>
            <input class="form-input" v-model="newVersion.fileSize" placeholder="如: 5.2MB 或 在线使用" />
          </view>
          
          <view class="form-item">
            <text class="form-label">最低支持版本:</text>
            <input class="form-input" v-model="newVersion.minSupportVersion" placeholder="如: 1.0.0" />
          </view>
          
          <view class="form-item checkbox-item">
            <checkbox :checked="newVersion.isForceUpdate" @change="onForceUpdateChange" />
            <text class="checkbox-label">强制更新</text>
          </view>
          
          <view class="form-item">
            <text class="form-label">更新内容:</text>
            <textarea class="form-textarea" v-model="newVersion.updateContent" placeholder="请输入更新内容..." />
          </view>
        </view>
        
        <view class="dialog-actions">
          <button class="btn-secondary" @click="closePublishDialog">取消</button>
          <button class="btn-primary" @click="publishNewVersion" :disabled="publishing">
            {{ publishing ? '发布中...' : '发布' }}
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  name: 'VersionManagement',
  data() {
    return {
      loading: false,
      publishing: false,
      versions: [],
      
      // 筛选器
      filterPlatform: 0,
      filterStatus: 0,
      platformOptions: [
        { label: '全部平台', value: '' },
        { label: 'Android', value: 'android' },
        { label: 'iOS', value: 'ios' },
        { label: 'H5', value: 'h5' }
      ],
      statusOptions: [
        { label: '全部状态', value: '' },
        { label: '已发布', value: 'published' },
        { label: '已下架', value: 'unpublished' },
        { label: '草稿', value: 'draft' }
      ],
      
      // 发布新版本
      publishPlatforms: [
        { label: 'Android', value: 'android' },
        { label: 'iOS', value: 'ios' },
        { label: 'H5', value: 'h5' }
      ],
      newVersion: {
        versionName: '',
        versionCode: '',
        platformIndex: 0,
        downloadUrl: '',
        fileSize: '',
        minSupportVersion: '',
        isForceUpdate: false,
        updateContent: ''
      }
    }
  },
  
  onLoad() {
    this.loadVersions()
  },
  
  methods: {
    // 加载版本列表
    async loadVersions() {
      this.loading = true
      try {
        const { getVersionList } = await import('../../api/version.js')
        const platform = this.platformOptions[this.filterPlatform].value
        const status = this.statusOptions[this.filterStatus].value

        const params = { page: 1, size: 50 }
        if (platform) params.platform = platform
        if (status) params.status = status

        const response = await getVersionList(params)
        this.versions = response.list || []
      } catch (error) {
        console.error('加载版本列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 刷新列表
    refreshList() {
      this.loadVersions()
    },
    
    // 平台筛选改变
    onPlatformChange(e) {
      this.filterPlatform = e.detail.value
      this.loadVersions()
    },
    
    // 状态筛选改变
    onStatusChange(e) {
      this.filterStatus = e.detail.value
      this.loadVersions()
    },
    
    // 显示发布对话框
    showPublishDialog() {
      this.resetNewVersion()
      this.$refs.publishPopup.open()
    },
    
    // 关闭发布对话框
    closePublishDialog() {
      this.$refs.publishPopup.close()
    },
    
    // 重置新版本表单
    resetNewVersion() {
      this.newVersion = {
        versionName: '',
        versionCode: '',
        platformIndex: 0,
        downloadUrl: '',
        fileSize: '',
        minSupportVersion: '',
        isForceUpdate: false,
        updateContent: ''
      }
    },
    
    // 新版本平台改变
    onNewPlatformChange(e) {
      this.newVersion.platformIndex = e.detail.value
    },
    
    // 强制更新改变
    onForceUpdateChange(e) {
      this.newVersion.isForceUpdate = e.detail.value
    },
    
    // 发布新版本
    async publishNewVersion() {
      if (!this.validateNewVersion()) {
        return
      }

      this.publishing = true
      try {
        const { publishVersion } = await import('../../api/version.js')

        const versionData = {
          versionName: this.newVersion.versionName,
          versionCode: this.newVersion.versionCode,
          platform: this.publishPlatforms[this.newVersion.platformIndex].value,
          downloadUrl: this.newVersion.downloadUrl,
          fileSize: this.newVersion.fileSize,
          minSupportVersion: this.newVersion.minSupportVersion,
          isForceUpdate: this.newVersion.isForceUpdate,
          updateContent: this.newVersion.updateContent
        }

        await publishVersion(versionData)

        uni.showToast({
          title: '发布成功',
          icon: 'success'
        })

        this.closePublishDialog()
        this.loadVersions()
      } catch (error) {
        console.error('发布版本失败:', error)
        uni.showToast({
          title: '发布失败',
          icon: 'error'
        })
      } finally {
        this.publishing = false
      }
    },
    
    // 验证新版本表单
    validateNewVersion() {
      if (!this.newVersion.versionName) {
        uni.showToast({ title: '请输入版本名称', icon: 'none' })
        return false
      }
      if (!this.newVersion.versionCode) {
        uni.showToast({ title: '请输入版本号', icon: 'none' })
        return false
      }
      if (!this.newVersion.updateContent) {
        uni.showToast({ title: '请输入更新内容', icon: 'none' })
        return false
      }
      return true
    },
    
    // 查看版本详情
    viewVersion(version) {
      uni.showModal({
        title: `版本 ${version.versionName} 详情`,
        content: `平台: ${version.platform}\n版本号: ${version.versionCode}\n状态: ${this.getStatusText(version.status)}\n发布时间: ${this.formatDate(version.releaseDate)}\n\n更新内容:\n${version.updateContent}`,
        showCancel: false
      })
    },
    
    // 下架版本
    async unpublishVersion(version) {
      const res = await uni.showModal({
        title: '确认下架',
        content: `确定要下架版本 ${version.versionName} 吗？`,
        showCancel: true
      })

      if (res.confirm) {
        try {
          const { unpublishVersion } = await import('../../api/version.js')
          await unpublishVersion(version.id)

          uni.showToast({
            title: '下架成功',
            icon: 'success'
          })

          this.loadVersions()
        } catch (error) {
          console.error('下架版本失败:', error)
          uni.showToast({
            title: '下架失败',
            icon: 'error'
          })
        }
      }
    },
    
    // 重新发布版本
    async republishVersion(version) {
      try {
        const { publishVersion } = await import('../../api/version.js')
        await publishVersion(version)

        uni.showToast({
          title: '重新发布成功',
          icon: 'success'
        })

        this.loadVersions()
      } catch (error) {
        console.error('重新发布失败:', error)
        uni.showToast({
          title: '重新发布失败',
          icon: 'error'
        })
      }
    },
    
    // 删除版本
    async deleteVersion(version) {
      const res = await uni.showModal({
        title: '确认删除',
        content: `确定要删除版本 ${version.versionName} 吗？此操作不可恢复！`,
        showCancel: true
      })

      if (res.confirm) {
        try {
          const { deleteVersion } = await import('../../api/version.js')
          await deleteVersion(version.id)

          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })

          this.loadVersions()
        } catch (error) {
          console.error('删除版本失败:', error)
          uni.showToast({
            title: '删除失败',
            icon: 'error'
          })
        }
      }
    },
    
    // 获取状态样式类
    getStatusClass(status) {
      switch (status) {
        case 'published': return 'status-published'
        case 'unpublished': return 'status-unpublished'
        case 'draft': return 'status-draft'
        default: return ''
      }
    },
    
    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 'published': return '已发布'
        case 'unpublished': return '已下架'
        case 'draft': return '草稿'
        default: return '未知'
      }
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '-'
      const date = new Date(dateStr)
      return date.toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.version-management {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部操作栏 */
.top-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.btn-primary, .btn-secondary {
  padding: 20rpx 30rpx;
  border-radius: 10rpx;
  border: none;
  font-size: 28rpx;
  color: white;
}

.btn-primary {
  background-color: #2196F3;
}

.btn-secondary {
  background-color: #666;
}

/* 筛选器 */
.filters {
  display: flex;
  gap: 30rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: white;
  border-radius: 10rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.filter-label {
  font-size: 28rpx;
  color: #333;
}

.picker-value {
  padding: 10rpx 20rpx;
  background-color: #f0f0f0;
  border-radius: 5rpx;
  font-size: 26rpx;
}

/* 版本列表 */
.version-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.version-item {
  background-color: white;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.version-info {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.version-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.version-platform {
  font-size: 24rpx;
  color: #666;
}

.version-status {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.status-published {
  background-color: #4CAF50;
}

.status-unpublished {
  background-color: #FF9800;
}

.status-draft {
  background-color: #9E9E9E;
}

.version-details {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.version-code, .version-date, .version-force {
  font-size: 26rpx;
  color: #666;
}

.version-force {
  color: #F44336;
  font-weight: bold;
}

.version-content {
  margin-bottom: 20rpx;
}

.content-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.content-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.version-actions {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.btn-small {
  padding: 15rpx 25rpx;
  border-radius: 8rpx;
  border: none;
  font-size: 24rpx;
  color: white;
}

.btn-info {
  background-color: #2196F3;
}

.btn-warning {
  background-color: #FF9800;
}

.btn-success {
  background-color: #4CAF50;
}

.btn-danger {
  background-color: #F44336;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 100rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 发布对话框 */
.publish-dialog {
  width: 600rpx;
  background-color: white;
  border-radius: 15rpx;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #2196F3;
  color: white;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: bold;
}

.dialog-close {
  font-size: 40rpx;
  cursor: pointer;
}

.dialog-content {
  padding: 30rpx;
  max-height: 800rpx;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-textarea {
  height: 150rpx;
  resize: none;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.checkbox-label {
  font-size: 28rpx;
  color: #333;
}

.dialog-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background-color: #f5f5f5;
}

.dialog-actions .btn-primary,
.dialog-actions .btn-secondary {
  flex: 1;
  text-align: center;
}
</style>
