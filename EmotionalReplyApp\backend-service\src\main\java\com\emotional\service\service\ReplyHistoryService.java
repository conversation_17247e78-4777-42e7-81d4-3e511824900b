package com.emotional.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.emotional.service.entity.ReplyHistory;

import java.util.List;

/**
 * 回复历史服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface ReplyHistoryService extends IService<ReplyHistory> {
    
    /**
     * 根据用户ID获取历史记录
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 历史记录列表
     */
    List<ReplyHistory> getHistoryByUserId(Long userId, int page, int size);
    
    /**
     * 根据用户ID获取收藏的历史记录
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 收藏的历史记录列表
     */
    List<ReplyHistory> getFavoriteHistoryByUserId(Long userId, int page, int size);
    
    /**
     * 切换收藏状态
     * 
     * @param historyId 历史记录ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean toggleFavorite(Long historyId, Long userId);
    
    /**
     * 删除历史记录
     * 
     * @param historyId 历史记录ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteHistory(Long historyId, Long userId);
    
    /**
     * 获取用户今日使用次数
     * 
     * @param userId 用户ID
     * @return 今日使用次数
     */
    int getTodayUsageCount(Long userId);
    
    /**
     * 获取用户总使用次数
     * 
     * @param userId 用户ID
     * @return 总使用次数
     */
    int getTotalUsageCount(Long userId);
    
    /**
     * 用户反馈
     * 
     * @param historyId 历史记录ID
     * @param userId 用户ID
     * @param rating 评分
     * @param feedback 反馈内容
     * @return 是否成功
     */
    boolean submitFeedback(Long historyId, Long userId, Integer rating, String feedback);
}
