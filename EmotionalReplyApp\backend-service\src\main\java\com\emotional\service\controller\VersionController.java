package com.emotional.service.controller;

import com.emotional.service.entity.AppVersion;
import com.emotional.service.service.VersionService;
import com.emotional.service.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 版本管理控制器
 */
@RestController
@RequestMapping("/version")
@CrossOrigin(origins = "*")
public class VersionController {

    @Autowired
    private VersionService versionService;

    /**
     * 检查更新
     */
    @GetMapping("/check")
    public Result<?> checkUpdate(@RequestParam String currentVersion, 
                                @RequestParam String platform) {
        try {
            Map<String, Object> result = versionService.checkUpdate(currentVersion, platform);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("检查更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取最新版本信息
     */
    @GetMapping("/latest")
    public Result<AppVersion> getLatestVersion(@RequestParam String platform) {
        try {
            AppVersion latestVersion = versionService.getLatestVersion(platform);
            if (latestVersion != null) {
                return Result.success(latestVersion);
            } else {
                return Result.error("未找到该平台的版本信息");
            }
        } catch (Exception e) {
            return Result.error("获取最新版本失败: " + e.getMessage());
        }
    }

    /**
     * 获取版本历史
     */
    @GetMapping("/history")
    public Result<?> getVersionHistory(@RequestParam(defaultValue = "1") int page,
                                      @RequestParam(defaultValue = "10") int size) {
        try {
            Map<String, Object> result = versionService.getVersionHistory(page, size);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("获取版本历史失败: " + e.getMessage());
        }
    }

    /**
     * 记录用户更新行为
     */
    @PostMapping("/log")
    public Result<?> logUpdateAction(@RequestBody Map<String, Object> logData) {
        try {
            versionService.logUpdateAction(logData);
            return Result.success("记录成功");
        } catch (Exception e) {
            return Result.error("记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前版本信息（管理端使用）
     */
    @GetMapping("/info/{id}")
    public Result<AppVersion> getVersionInfo(@PathVariable Long id) {
        try {
            AppVersion version = versionService.getVersionById(id);
            if (version != null) {
                return Result.success(version);
            } else {
                return Result.error("版本信息不存在");
            }
        } catch (Exception e) {
            return Result.error("获取版本信息失败: " + e.getMessage());
        }
    }

    /**
     * 发布新版本（管理端使用）
     */
    @PostMapping("/publish")
    public Result<?> publishVersion(@RequestBody AppVersion appVersion) {
        try {
            versionService.publishVersion(appVersion);
            return Result.success("版本发布成功");
        } catch (Exception e) {
            return Result.error("版本发布失败: " + e.getMessage());
        }
    }

    /**
     * 下架版本（管理端使用）
     */
    @PutMapping("/unpublish/{id}")
    public Result<?> unpublishVersion(@PathVariable Long id) {
        try {
            versionService.unpublishVersion(id);
            return Result.success("版本下架成功");
        } catch (Exception e) {
            return Result.error("版本下架失败: " + e.getMessage());
        }
    }

    /**
     * 删除版本（管理端使用）
     */
    @DeleteMapping("/{id}")
    public Result<?> deleteVersion(@PathVariable Long id) {
        try {
            versionService.deleteVersion(id);
            return Result.success("版本删除成功");
        } catch (Exception e) {
            return Result.error("版本删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有平台的版本列表（管理端使用）
     */
    @GetMapping("/list")
    public Result<?> getVersionList(@RequestParam(defaultValue = "1") int page,
                                   @RequestParam(defaultValue = "10") int size,
                                   @RequestParam(required = false) String platform,
                                   @RequestParam(required = false) String status) {
        try {
            Map<String, Object> result = versionService.getVersionList(page, size, platform, status);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("获取版本列表失败: " + e.getMessage());
        }
    }
}
