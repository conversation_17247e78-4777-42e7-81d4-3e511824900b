package com.emotional.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.emotional.service.entity.UserSettings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户设置 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface UserSettingsMapper extends BaseMapper<UserSettings> {
    
    /**
     * 根据用户ID获取用户设置
     * 
     * @param userId 用户ID
     * @return 用户设置
     */
    @Select("SELECT * FROM user_settings WHERE user_id = #{userId}")
    UserSettings getByUserId(@Param("userId") Long userId);
}
