<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="header">
      <text class="title">意见反馈</text>
      <text class="subtitle">您的建议是我们前进的动力</text>
    </view>

    <!-- 反馈表单 -->
    <view class="feedback-form">
      <!-- 问题类型选择 -->
      <view class="form-section">
        <text class="section-title">问题类型 *</text>
        <view class="type-selector">
          <view 
            class="type-item" 
            :class="{ active: feedbackType === item.value }"
            v-for="item in feedbackTypes" 
            :key="item.value"
            @click="selectFeedbackType(item.value)"
          >
            <text class="type-icon">{{ item.icon }}</text>
            <text class="type-text">{{ item.label }}</text>
          </view>
        </view>
      </view>

      <!-- 问题描述 -->
      <view class="form-section">
        <text class="section-title">问题描述 *</text>
        <textarea 
          class="description-input"
          v-model="description"
          placeholder="请详细描述您遇到的问题或建议，我们会认真对待每一条反馈"
          maxlength="500"
          :show-confirm-bar="false"
        />
        <text class="char-count">{{ description.length }}/500</text>
      </view>

      <!-- 联系方式 -->
      <view class="form-section">
        <text class="section-title">联系方式</text>
        <input 
          class="contact-input"
          v-model="contactInfo"
          placeholder="请留下您的邮箱或微信，方便我们回复（可选）"
          maxlength="100"
        />
      </view>

      <!-- 图片上传 -->
      <view class="form-section">
        <text class="section-title">相关截图</text>
        <view class="image-upload">
          <view class="image-list">
            <view 
              class="image-item" 
              v-for="(image, index) in uploadedImages" 
              :key="index"
            >
              <image :src="image" mode="aspectFill" @click="previewImage(image)" />
              <view class="delete-btn" @click="removeImage(index)">×</view>
            </view>
            <view 
              class="upload-btn" 
              v-if="uploadedImages.length < 3"
              @click="chooseImage"
            >
              <text class="upload-icon">📷</text>
              <text class="upload-text">添加图片</text>
            </view>
          </view>
          <text class="upload-tip">最多上传3张图片，支持jpg、png格式</text>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <button 
          class="submit-btn" 
          :class="{ disabled: !canSubmit }"
          :disabled="!canSubmit"
          @click="submitFeedback"
        >
          {{ submitting ? '提交中...' : '提交反馈' }}
        </button>
      </view>
    </view>

    <!-- 历史反馈 -->
    <view class="history-section" v-if="feedbackHistory.length > 0">
      <text class="section-title">我的反馈记录</text>
      <view class="history-list">
        <view 
          class="history-item" 
          v-for="item in feedbackHistory" 
          :key="item.id"
          @click="viewFeedbackDetail(item)"
        >
          <view class="history-header">
            <text class="history-type">{{ getFeedbackTypeLabel(item.type) }}</text>
            <text class="history-status" :class="item.status">{{ getStatusText(item.status) }}</text>
          </view>
          <text class="history-content">{{ item.description.substring(0, 50) }}...</text>
          <text class="history-time">{{ formatTime(item.createTime) }}</text>
        </view>
      </view>
    </view>

    <!-- 联系方式卡片 -->
    <view class="contact-card">
      <text class="card-title">其他联系方式</text>
      <view class="contact-methods">
        <view class="contact-method" @click="copyEmail">
          <text class="method-icon">📧</text>
          <text class="method-text">邮箱：<EMAIL></text>
        </view>
        <view class="contact-method" @click="copyWechat">
          <text class="method-icon">💬</text>
          <text class="method-text">微信：EmotionalReplyApp</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { UserManager } from '../../utils/user.js'

export default {
  name: 'FeedbackPage',
  
  data() {
    return {
      feedbackType: '',
      description: '',
      contactInfo: '',
      uploadedImages: [],
      submitting: false,
      feedbackHistory: [],
      
      feedbackTypes: [
        { value: 'bug', label: '功能异常', icon: '🐛' },
        { value: 'suggestion', label: '功能建议', icon: '💡' },
        { value: 'ui', label: '界面问题', icon: '🎨' },
        { value: 'performance', label: '性能问题', icon: '⚡' },
        { value: 'content', label: '内容问题', icon: '📝' },
        { value: 'other', label: '其他问题', icon: '❓' }
      ]
    }
  },

  computed: {
    canSubmit() {
      return this.feedbackType && 
             this.description.trim().length >= 10 && 
             !this.submitting
    }
  },

  onLoad() {
    this.loadFeedbackHistory()
    this.loadUserContact()
  },

  methods: {
    // 选择反馈类型
    selectFeedbackType(type) {
      this.feedbackType = type
    },

    // 选择图片
    chooseImage() {
      uni.chooseImage({
        count: 3 - this.uploadedImages.length,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.uploadedImages.push(...res.tempFilePaths)
        }
      })
    },

    // 预览图片
    previewImage(image) {
      uni.previewImage({
        urls: this.uploadedImages,
        current: image
      })
    },

    // 删除图片
    removeImage(index) {
      this.uploadedImages.splice(index, 1)
    },

    // 提交反馈
    async submitFeedback() {
      if (!this.canSubmit) return

      try {
        this.submitting = true

        // 构建反馈数据
        const feedbackData = {
          type: this.feedbackType,
          description: this.description.trim(),
          contactInfo: this.contactInfo.trim(),
          images: this.uploadedImages,
          userInfo: UserManager.getUserInfo(),
          deviceInfo: this.getDeviceInfo(),
          appVersion: '1.0.0',
          createTime: new Date().toISOString()
        }

        // 这里应该调用API提交反馈
        // const result = await submitFeedback(feedbackData)
        
        // 模拟API调用
        await this.simulateSubmit(feedbackData)

        uni.showModal({
          title: '提交成功',
          content: '感谢您的反馈！我们会在24小时内处理并回复您。',
          showCancel: false,
          success: () => {
            this.resetForm()
            this.loadFeedbackHistory()
          }
        })

      } catch (error) {
        console.error('提交反馈失败:', error)
        uni.showModal({
          title: '提交失败',
          content: '网络异常，请稍后重试',
          showCancel: false
        })
      } finally {
        this.submitting = false
      }
    },

    // 模拟提交
    async simulateSubmit(data) {
      return new Promise((resolve) => {
        setTimeout(() => {
          // 保存到本地存储
          const history = uni.getStorageSync('feedback_history') || []
          const newFeedback = {
            id: Date.now(),
            ...data,
            status: 'pending'
          }
          history.unshift(newFeedback)
          uni.setStorageSync('feedback_history', history)
          resolve(newFeedback)
        }, 1000)
      })
    },

    // 重置表单
    resetForm() {
      this.feedbackType = ''
      this.description = ''
      this.contactInfo = ''
      this.uploadedImages = []
    },

    // 加载反馈历史
    loadFeedbackHistory() {
      try {
        const history = uni.getStorageSync('feedback_history') || []
        this.feedbackHistory = history.slice(0, 5) // 只显示最近5条
      } catch (error) {
        console.error('加载反馈历史失败:', error)
        this.feedbackHistory = []
      }
    },

    // 加载用户联系方式
    loadUserContact() {
      const userInfo = UserManager.getUserInfo()
      if (userInfo && userInfo.email) {
        this.contactInfo = userInfo.email
      }
    },

    // 查看反馈详情
    viewFeedbackDetail(item) {
      uni.showModal({
        title: this.getFeedbackTypeLabel(item.type),
        content: `状态：${this.getStatusText(item.status)}\n\n${item.description}`,
        showCancel: false
      })
    },

    // 获取反馈类型标签
    getFeedbackTypeLabel(type) {
      const typeItem = this.feedbackTypes.find(item => item.value === type)
      return typeItem ? typeItem.label : '未知类型'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        pending: '待处理',
        processing: '处理中',
        resolved: '已解决',
        closed: '已关闭'
      }
      return statusMap[status] || '未知状态'
    },

    // 格式化时间
    formatTime(timeStr) {
      const time = new Date(timeStr)
      const now = new Date()
      const diff = now - time
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      return `${Math.floor(diff / 86400000)}天前`
    },

    // 获取设备信息
    getDeviceInfo() {
      const systemInfo = uni.getSystemInfoSync()
      return {
        platform: systemInfo.platform,
        system: systemInfo.system,
        version: systemInfo.version,
        model: systemInfo.model
      }
    },

    // 复制邮箱
    copyEmail() {
      uni.setClipboardData({
        data: '<EMAIL>',
        success: () => {
          uni.showToast({
            title: '邮箱已复制',
            icon: 'success'
          })
        }
      })
    },

    // 复制微信
    copyWechat() {
      uni.setClipboardData({
        data: 'EmotionalReplyApp',
        success: () => {
          uni.showToast({
            title: '微信号已复制',
            icon: 'success'
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.header {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  color: white;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

.feedback-form {
  margin: 30rpx 30rpx 0;
}

.form-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.type-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.type-item {
  flex: 1;
  min-width: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.3s;
}

.type-item.active {
  border-color: #2196F3;
  background-color: #f3f8ff;
}

.type-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  display: block;
}

.type-text {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.type-item.active .type-text {
  color: #2196F3;
  font-weight: bold;
}

.description-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.6;
  resize: none;
}

.char-count {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  margin-top: 10rpx;
  display: block;
}

.contact-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.image-upload {
  margin-top: 10rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.image-item image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.delete-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #ccc;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.upload-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.upload-text {
  font-size: 24rpx;
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 15rpx;
  display: block;
}

.submit-section {
  margin: 40rpx 30rpx;
}

.submit-btn {
  width: 100%;
  padding: 30rpx;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-btn.disabled {
  background-color: #ccc;
  color: #999;
}

.history-section, .contact-card {
  margin: 30rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.history-list {
  margin-top: 20rpx;
}

.history-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.history-type {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.history-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  color: white;
}

.history-status.pending {
  background-color: #ff9800;
}

.history-status.processing {
  background-color: #2196F3;
}

.history-status.resolved {
  background-color: #4caf50;
}

.history-status.closed {
  background-color: #999;
}

.history-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8rpx;
  display: block;
}

.history-time {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.contact-method {
  display: flex;
  align-items: center;
  padding: 15rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.method-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.method-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
</style>
