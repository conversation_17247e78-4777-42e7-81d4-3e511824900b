<template>
  <view class="container">
    <!-- 二维码区域 -->
    <view class="qrcode-section">
      <view class="qrcode-container">
        <image
          class="qrcode-image"
          src="/static/images/wechat-qrcode.jpg"
          mode="aspectFit"
          @click="previewQRCode"
        />
        <text class="qrcode-title">情感回复助手</text>
      </view>


    </view>

    <!-- 使用说明 -->
    <view class="guide-section">
      <text class="guide-title">📋 使用说明</text>
      <view class="guide-steps">
        <view class="step-item">
          <view class="step-number">1</view>
          <text class="step-text">扫描上方二维码关注公众号</text>
        </view>

        <view class="step-item">
          <view class="step-number">2</view>
          <text class="step-text">在公众号中发送您的问题或建议</text>
        </view>

        <view class="step-item">
          <view class="step-number">3</view>
          <text class="step-text">我们会在24小时内回复您</text>
        </view>
      </view>
    </view>

    <!-- 温馨提示 -->
    <view class="tips-section">
      <text class="tips-title">💡 温馨提示</text>
      <view class="tips-content">
        <text class="tip-item">• 请详细描述您遇到的问题，以便我们更好地帮助您</text>
        <text class="tip-item">• 如有截图或错误信息，可直接发送给我们</text>
        <text class="tip-item">• 您的每一条反馈都是我们改进的动力</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FeedbackPage',

  methods: {
    // 预览二维码
    previewQRCode() {
      uni.previewImage({
        urls: ['/static/images/wechat-qrcode.jpg'],
        current: '/static/images/wechat-qrcode.jpg'
      })
    }
  }
}
</script>

<style scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.header {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  color: white;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
  line-height: 1.5;
}

/* 二维码区域 */
.qrcode-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 50rpx 40rpx;
  margin: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
  text-align: center;
}

.qrcode-container {
  margin-bottom: 0;
}

.qrcode-image {
  width: 450rpx;
  height: 450rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  margin-bottom: 25rpx;
}

.qrcode-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}


/* 使用说明 */
.guide-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.guide-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  display: block;
}

.guide-steps {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.step-number {
  width: 50rpx;
  height: 50rpx;
  background-color: #2196F3;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.step-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

/* 温馨提示 */
.tips-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.tips-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  display: block;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: block;
}
</style>
