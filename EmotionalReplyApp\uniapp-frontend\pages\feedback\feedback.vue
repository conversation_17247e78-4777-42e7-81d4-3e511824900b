<template>
  <view class="container">
    <!-- 页面头部 -->
    <view class="header">
      <text class="title">意见反馈</text>
      <text class="subtitle">您的建议是我们前进的动力</text>
    </view>

    <!-- 微信公众号反馈 -->
    <view class="wechat-feedback">
      <view class="feedback-intro">
        <text class="intro-title">💬 通过微信公众号反馈</text>
        <text class="intro-desc">关注我们的微信公众号，获得更及时的回复和技术支持</text>
      </view>

      <!-- 二维码区域 -->
      <view class="qrcode-section">
        <view class="qrcode-container">
          <image
            class="qrcode-image"
            src="/static/images/wechat-qrcode.jpg"
            mode="aspectFit"
            @click="previewQRCode"
          />
          <text class="qrcode-tip">长按识别二维码关注公众号</text>
        </view>

        <view class="qrcode-actions">
          <button class="action-btn primary" @click="saveQRCode">
            <text class="btn-icon">💾</text>
            <text class="btn-text">保存二维码</text>
          </button>
          <button class="action-btn secondary" @click="previewQRCode">
            <text class="btn-icon">🔍</text>
            <text class="btn-text">查看大图</text>
          </button>
        </view>
      </view>

      <!-- 反馈指南 -->
      <view class="feedback-guide">
        <text class="guide-title">📋 反馈指南</text>
        <view class="guide-steps">
          <view class="step-item">
            <view class="step-number">1</view>
            <view class="step-content">
              <text class="step-title">扫码关注公众号</text>
              <text class="step-desc">使用微信扫描上方二维码关注我们</text>
            </view>
          </view>

          <view class="step-item">
            <view class="step-number">2</view>
            <view class="step-content">
              <text class="step-title">发送反馈内容</text>
              <text class="step-desc">在公众号中直接发送您的问题或建议</text>
            </view>
          </view>

          <view class="step-item">
            <view class="step-number">3</view>
            <view class="step-content">
              <text class="step-title">获得及时回复</text>
              <text class="step-desc">我们会在24小时内回复您的消息</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 反馈类型提示 -->
      <view class="feedback-types">
        <text class="types-title">🏷️ 反馈类型参考</text>
        <view class="types-grid">
          <view class="type-card" v-for="type in feedbackTypes" :key="type.value">
            <text class="type-icon">{{ type.icon }}</text>
            <text class="type-label">{{ type.label }}</text>
            <text class="type-example">{{ type.example }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 其他联系方式 -->
    <view class="contact-section">
      <text class="section-title">📞 其他联系方式</text>
      <view class="contact-list">
        <view class="contact-item" @click="copyEmail">
          <view class="contact-icon-wrapper">
            <text class="contact-icon">📧</text>
          </view>
          <view class="contact-info">
            <text class="contact-label">邮箱反馈</text>
            <text class="contact-value"><EMAIL></text>
          </view>
          <text class="contact-action">点击复制</text>
        </view>

        <view class="contact-item" @click="copyWechat">
          <view class="contact-icon-wrapper">
            <text class="contact-icon">💬</text>
          </view>
          <view class="contact-info">
            <text class="contact-label">微信客服</text>
            <text class="contact-value">EmotionalReplyApp</text>
          </view>
          <text class="contact-action">点击复制</text>
        </view>

        <view class="contact-item" @click="openOfficialWebsite">
          <view class="contact-icon-wrapper">
            <text class="contact-icon">🌐</text>
          </view>
          <view class="contact-info">
            <text class="contact-label">官方网站</text>
            <text class="contact-value">www.emotional-reply.com</text>
          </view>
          <text class="contact-action">点击访问</text>
        </view>
      </view>
    </view>

    <!-- 常见问题 -->
    <view class="faq-section">
      <text class="section-title">❓ 常见问题</text>
      <view class="faq-list">
        <view
          class="faq-item"
          v-for="(faq, index) in faqList"
          :key="index"
          @click="toggleFAQ(index)"
        >
          <view class="faq-question">
            <text class="faq-q-text">{{ faq.question }}</text>
            <text class="faq-toggle" :class="{ expanded: faq.expanded }">▼</text>
          </view>
          <view class="faq-answer" v-if="faq.expanded">
            <text class="faq-a-text">{{ faq.answer }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FeedbackPage',

  data() {
    return {
      feedbackTypes: [
        {
          value: 'bug',
          label: '功能异常',
          icon: '🐛',
          example: '如：无法生成回复、闪退等'
        },
        {
          value: 'suggestion',
          label: '功能建议',
          icon: '💡',
          example: '如：希望增加新的回复风格'
        },
        {
          value: 'ui',
          label: '界面问题',
          icon: '🎨',
          example: '如：按钮位置不合理、字体太小'
        },
        {
          value: 'performance',
          label: '性能问题',
          icon: '⚡',
          example: '如：响应慢、卡顿等'
        },
        {
          value: 'content',
          label: '内容问题',
          icon: '📝',
          example: '如：回复内容不准确、不合适'
        },
        {
          value: 'other',
          label: '其他问题',
          icon: '❓',
          example: '如：账号、付费等其他问题'
        }
      ],

      faqList: [
        {
          question: '如何快速联系客服？',
          answer: '您可以关注我们的微信公众号，在公众号中直接发送消息，我们会在24小时内回复。也可以发送邮件到 <EMAIL>。',
          expanded: false
        },
        {
          question: '反馈后多久能收到回复？',
          answer: '我们承诺在24小时内回复您的反馈。对于紧急问题，我们会优先处理并尽快回复。',
          expanded: false
        },
        {
          question: '可以提交哪些类型的反馈？',
          answer: '您可以提交功能异常、功能建议、界面问题、性能问题、内容问题等各类反馈。我们欢迎任何有助于改进产品的建议。',
          expanded: false
        },
        {
          question: '如何查看反馈处理进度？',
          answer: '关注微信公众号后，您可以随时在公众号中询问反馈处理进度。我们也会主动推送处理结果。',
          expanded: false
        }
      ]
    }
  },

  methods: {
    // 预览二维码
    previewQRCode() {
      uni.previewImage({
        urls: ['/static/images/wechat-qrcode.jpg'],
        current: '/static/images/wechat-qrcode.jpg'
      })
    },

    // 保存二维码
    saveQRCode() {
      // #ifdef APP-PLUS
      uni.saveImageToPhotosAlbum({
        filePath: '/static/images/wechat-qrcode.jpg',
        success: () => {
          uni.showToast({
            title: '二维码已保存到相册',
            icon: 'success'
          })
        },
        fail: () => {
          uni.showModal({
            title: '保存失败',
            content: '请检查是否授权访问相册',
            showCancel: false
          })
        }
      })
      // #endif

      // #ifdef H5
      uni.showModal({
        title: '提示',
        content: '请长按二维码图片保存到本地',
        showCancel: false
      })
      // #endif

      // #ifdef MP
      uni.showModal({
        title: '提示',
        content: '请长按二维码图片保存到相册',
        showCancel: false
      })
      // #endif
    },

    // 复制邮箱
    copyEmail() {
      uni.setClipboardData({
        data: '<EMAIL>',
        success: () => {
          uni.showToast({
            title: '邮箱已复制',
            icon: 'success'
          })
        }
      })
    },

    // 复制微信
    copyWechat() {
      uni.setClipboardData({
        data: 'EmotionalReplyApp',
        success: () => {
          uni.showToast({
            title: '微信号已复制',
            icon: 'success'
          })
        }
      })
    },

    // 打开官方网站
    openOfficialWebsite() {
      // #ifdef APP-PLUS
      plus.runtime.openURL('https://www.emotional-reply.com')
      // #endif

      // #ifdef H5
      window.open('https://www.emotional-reply.com', '_blank')
      // #endif

      // #ifdef MP
      uni.setClipboardData({
        data: 'https://www.emotional-reply.com',
        success: () => {
          uni.showToast({
            title: '网址已复制',
            icon: 'success'
          })
        }
      })
      // #endif
    },

    // 切换FAQ展开状态
    toggleFAQ(index) {
      this.faqList[index].expanded = !this.faqList[index].expanded
    }
  }
}
</script>

<style scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.header {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  color: white;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

/* 微信反馈区域 */
.wechat-feedback {
  margin: 30rpx;
}

.feedback-intro {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
  text-align: center;
}

.intro-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.intro-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: block;
}

/* 二维码区域 */
.qrcode-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
  text-align: center;
}

.qrcode-container {
  margin-bottom: 30rpx;
}

.qrcode-image {
  width: 400rpx;
  height: 400rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  margin-bottom: 20rpx;
}

.qrcode-tip {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.qrcode-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  border: none;
  min-width: 200rpx;
  justify-content: center;
}

.action-btn.primary {
  background-color: #2196F3;
  color: white;
}

.action-btn.secondary {
  background-color: #f0f0f0;
  color: #333;
}

.btn-icon {
  margin-right: 8rpx;
}

/* 反馈指南 */
.feedback-guide {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.guide-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  display: block;
}

.guide-steps {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.step-number {
  width: 50rpx;
  height: 50rpx;
  background-color: #2196F3;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: block;
}

/* 反馈类型 */
.feedback-types {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.types-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  display: block;
}

.types-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.type-card {
  padding: 25rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.3s;
}

.type-card:hover {
  border-color: #2196F3;
  background-color: #f8f9ff;
}

.type-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
  display: block;
}

.type-label {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.type-example {
  font-size: 22rpx;
  color: #999;
  line-height: 1.4;
  display: block;
}

/* 联系方式 */
.contact-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  display: block;
}

.contact-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 25rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s;
}

.contact-item:active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

.contact-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  background-color: #2196F3;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.contact-icon {
  font-size: 28rpx;
  color: white;
}

.contact-info {
  flex: 1;
}

.contact-label {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
  display: block;
}

.contact-value {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.contact-action {
  font-size: 24rpx;
  color: #2196F3;
}

/* 常见问题 */
.faq-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.faq-list {
  margin-top: 20rpx;
}

.faq-item {
  border-bottom: 1rpx solid #f0f0f0;
  padding: 20rpx 0;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.faq-q-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.faq-toggle {
  font-size: 20rpx;
  color: #666;
  transition: transform 0.3s;
}

.faq-toggle.expanded {
  transform: rotate(180deg);
}

.faq-answer {
  margin-top: 15rpx;
  padding-top: 15rpx;
  border-top: 1rpx solid #f8f9fa;
}

.faq-a-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: block;
}
</style>
