/**
 * Vuex 状态管理入口
 */
import { createStore } from 'vuex'
import user from './modules/user.js'
import message from './modules/message.js'
import settings from './modules/settings.js'
import reply from './modules/reply.js'

const store = createStore({
  modules: {
    user,
    message,
    settings,
    reply
  },
  
  state: {
    // 全局加载状态
    globalLoading: false,
    // 网络状态
    networkStatus: true,
    // 应用版本
    appVersion: '1.0.0'
  },
  
  mutations: {
    SET_GLOBAL_LOADING(state, loading) {
      state.globalLoading = loading
    },
    
    SET_NETWORK_STATUS(state, status) {
      state.networkStatus = status
    },
    
    SET_APP_VERSION(state, version) {
      state.appVersion = version
    }
  },
  
  actions: {
    // 设置全局加载状态
    setGlobalLoading({ commit }, loading) {
      commit('SET_GLOBAL_LOADING', loading)
    },
    
    // 检查网络状态
    checkNetworkStatus({ commit }) {
      uni.getNetworkType({
        success: (res) => {
          const isConnected = res.networkType !== 'none'
          commit('SET_NETWORK_STATUS', isConnected)
          
          if (!isConnected) {
            uni.showToast({
              title: '网络连接异常',
              icon: 'none'
            })
          }
        }
      })
    },
    
    // 初始化应用数据
    async initApp({ dispatch }) {
      try {
        // 检查网络状态
        await dispatch('checkNetworkStatus')
        
        // 加载用户数据
        await dispatch('user/loadUserData')
        
        // 加载设置
        await dispatch('settings/loadSettings')
        
        console.log('App initialized successfully')
      } catch (error) {
        console.error('App initialization failed:', error)
        throw error
      }
    }
  },
  
  getters: {
    // 是否正在加载
    isLoading: state => state.globalLoading,
    
    // 网络是否连接
    isNetworkConnected: state => state.networkStatus,
    
    // 应用信息
    appInfo: state => ({
      version: state.appVersion,
      networkStatus: state.networkStatus
    })
  }
})

export default store
