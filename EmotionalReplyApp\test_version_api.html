<!DOCTYPE html>
<html>
<head>
    <title>测试版本API</title>
</head>
<body>
    <h1>版本API测试</h1>
    <button onclick="testControllerAPI()">测试控制器连接</button>
    <button onclick="testVersionAPI()">测试检查更新API</button>
    <div id="result"></div>

    <script>
        async function testControllerAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '测试控制器连接中...';

            try {
                const response = await fetch('http://localhost:8080/api/version/test');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                resultDiv.innerHTML = '<h3>控制器测试结果:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                resultDiv.innerHTML = '控制器连接错误: ' + error.message;
                console.error('控制器测试失败:', error);
            }
        }

        async function testVersionAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '测试版本检查中...';

            try {
                const response = await fetch('http://localhost:8080/api/version/check?currentVersion=1.0.0&platform=h5');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                resultDiv.innerHTML = '<h3>版本检查结果:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                resultDiv.innerHTML = '版本检查错误: ' + error.message;
                console.error('API测试失败:', error);
            }
        }
    </script>
</body>
</html>
