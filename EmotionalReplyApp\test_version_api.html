<!DOCTYPE html>
<html>
<head>
    <title>测试版本API</title>
</head>
<body>
    <h1>版本API测试</h1>
    <button onclick="testVersionAPI()">测试检查更新API</button>
    <div id="result"></div>

    <script>
        async function testVersionAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '测试中...';

            try {
                const response = await fetch('http://localhost:8080/api/version/check?currentVersion=1.0.0&platform=h5');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                resultDiv.innerHTML = '错误: ' + error.message;
                console.error('API测试失败:', error);
            }
        }
    </script>
</body>
</html>
