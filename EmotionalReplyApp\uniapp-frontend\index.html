<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>情感回复助手</title>
    <link rel="icon" href="/static/logo.png">
    <script>
        var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
        document.write('<meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0' + (coverSupport ? ',viewport-fit=cover' : '') + '" />')
    </script>
    <style>
        html, body {
            -webkit-user-select: none;
            user-select: none;
            width: 100%;
        }
        body {
            background-color: #f8f9fa;
        }
        uni-page-body {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- uni-app -->
    </div>
    <script type="module" src="/main.js"></script>
</body>
</html>
