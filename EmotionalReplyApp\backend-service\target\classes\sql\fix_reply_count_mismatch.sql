-- 修复回复数量与风格数量不匹配的问题
-- 执行日期: 2025-01-26
-- 说明: 确保用户设置的回复风格数量与首选生成数量一致

USE emotional_reply;

-- 1. 查看当前用户设置情况
SELECT 
    id,
    user_id,
    preferred_reply_count,
    reply_styles,
    reply_generation_mode,
    JSON_LENGTH(reply_styles) as style_count
FROM user_settings 
WHERE reply_styles IS NOT NULL;

-- 2. 修复智能选择模式下的用户设置
-- 对于智能选择模式，确保默认风格数量与首选数量一致
UPDATE user_settings 
SET reply_styles = CASE 
    WHEN preferred_reply_count = 1 THEN '["warm_caring"]'
    WHEN preferred_reply_count = 2 THEN '["warm_caring", "humorous"]'
    WHEN preferred_reply_count = 3 THEN '["warm_caring", "humorous", "high_eq"]'
    ELSE '["warm_caring", "humorous"]'
END
WHERE reply_generation_mode = 'smart' 
AND (reply_styles IS NULL OR JSON_LENGTH(reply_styles) != preferred_reply_count);

-- 3. 修复自定义模式下风格数量超过首选数量的情况
UPDATE user_settings 
SET reply_styles = CASE 
    WHEN preferred_reply_count = 1 THEN JSON_ARRAY(JSON_UNQUOTE(JSON_EXTRACT(reply_styles, '$[0]')))
    WHEN preferred_reply_count = 2 THEN JSON_ARRAY(
        JSON_UNQUOTE(JSON_EXTRACT(reply_styles, '$[0]')),
        JSON_UNQUOTE(JSON_EXTRACT(reply_styles, '$[1]'))
    )
    WHEN preferred_reply_count = 3 THEN JSON_ARRAY(
        JSON_UNQUOTE(JSON_EXTRACT(reply_styles, '$[0]')),
        JSON_UNQUOTE(JSON_EXTRACT(reply_styles, '$[1]')),
        JSON_UNQUOTE(JSON_EXTRACT(reply_styles, '$[2]'))
    )
    ELSE reply_styles
END
WHERE reply_generation_mode = 'custom' 
AND JSON_LENGTH(reply_styles) > preferred_reply_count;

-- 4. 验证修复结果
SELECT 
    '修复后的用户设置' as check_type,
    id,
    user_id,
    preferred_reply_count,
    reply_styles,
    reply_generation_mode,
    JSON_LENGTH(reply_styles) as style_count,
    CASE 
        WHEN JSON_LENGTH(reply_styles) = preferred_reply_count THEN '✓ 匹配'
        ELSE '✗ 不匹配'
    END as status
FROM user_settings 
WHERE reply_styles IS NOT NULL
ORDER BY user_id;

-- 显示修复完成信息
SELECT 
    '修复完成' as status,
    '回复风格数量已与首选生成数量保持一致' as message,
    NOW() as update_time;
