// 本地存储工具类
class StorageUtil {
  // 保存数据
  static set(key, value) {
    try {
      uni.setStorageSync(key, JSON.stringify(value))
      return true
    } catch (error) {
      console.error('Storage set error:', error)
      return false
    }
  }
  
  // 获取数据
  static get(key, defaultValue = null) {
    try {
      const value = uni.getStorageSync(key)
      return value ? JSON.parse(value) : defaultValue
    } catch (error) {
      console.error('Storage get error:', error)
      return defaultValue
    }
  }
  
  // 删除数据
  static remove(key) {
    try {
      uni.removeStorageSync(key)
      return true
    } catch (error) {
      console.error('Storage remove error:', error)
      return false
    }
  }
  
  // 清空所有数据
  static clear() {
    try {
      uni.clearStorageSync()
      return true
    } catch (error) {
      console.error('Storage clear error:', error)
      return false
    }
  }
}

// 历史记录管理
class HistoryManager {
  static HISTORY_KEY = 'reply_history'
  static MAX_HISTORY = 100
  
  // 添加历史记录
  static addHistory(originalMessage, replyList, emotionResult) {
    const history = this.getHistory()
    const newRecord = {
      id: Date.now(),
      originalMessage,
      replyList,
      emotionResult,
      createTime: new Date().toISOString(),
      isFavorite: false
    }
    
    history.unshift(newRecord)
    
    // 限制历史记录数量
    if (history.length > this.MAX_HISTORY) {
      history.splice(this.MAX_HISTORY)
    }
    
    StorageUtil.set(this.HISTORY_KEY, history)
    return newRecord
  }
  
  // 获取历史记录
  static getHistory() {
    return StorageUtil.get(this.HISTORY_KEY, [])
  }
  
  // 删除历史记录
  static removeHistory(id) {
    const history = this.getHistory()
    const index = history.findIndex(item => item.id === id)
    if (index > -1) {
      history.splice(index, 1)
      StorageUtil.set(this.HISTORY_KEY, history)
      return true
    }
    return false
  }
  
  // 收藏/取消收藏
  static toggleFavorite(id) {
    const history = this.getHistory()
    const item = history.find(item => item.id === id)
    if (item) {
      item.isFavorite = !item.isFavorite
      StorageUtil.set(this.HISTORY_KEY, history)
      return item.isFavorite
    }
    return false
  }
  
  // 获取收藏列表
  static getFavorites() {
    return this.getHistory().filter(item => item.isFavorite)
  }
  
  // 清空历史记录
  static clearHistory() {
    StorageUtil.remove(this.HISTORY_KEY)
  }
}

// 用户设置管理
class SettingsManager {
  static SETTINGS_KEY = 'user_settings'
  
  static getDefaultSettings() {
    return {
      theme: 'light',
      fontSize: 'medium',
      autoSave: true,
      showFloatingBubble: false,
      replyStyles: ['warm_caring', 'humorous', 'rational', 'concise', 'romantic'],
      dailyQuota: 10,
      notifications: {
        enabled: true,
        sound: true,
        vibrate: true
      }
    }
  }
  
  // 获取设置
  static getSettings() {
    return StorageUtil.get(this.SETTINGS_KEY, this.getDefaultSettings())
  }
  
  // 更新设置
  static updateSettings(newSettings) {
    const currentSettings = this.getSettings()
    const updatedSettings = { ...currentSettings, ...newSettings }
    StorageUtil.set(this.SETTINGS_KEY, updatedSettings)
    return updatedSettings
  }
  
  // 重置设置
  static resetSettings() {
    const defaultSettings = this.getDefaultSettings()
    StorageUtil.set(this.SETTINGS_KEY, defaultSettings)
    return defaultSettings
  }
}

// 缓存管理工具类
class CacheManager {
  // 缓存类型定义
  static CACHE_TYPES = {
    USER_DATA: 'user_data',
    SETTINGS: 'settings',
    HISTORY: 'history',
    TEMP_DATA: 'temp_data',
    IMAGE_CACHE: 'image_cache',
    API_CACHE: 'api_cache'
  }

  // 需要保留的关键数据（清除缓存时不删除）
  static PROTECTED_KEYS = [
    'token',
    'user_info',
    'current_user_id'
  ]

  /**
   * 计算缓存大小
   * @returns {Promise<Object>} 缓存大小信息
   */
  static async calculateCacheSize() {
    try {
      let totalSize = 0
      const cacheDetails = {}

      // 获取所有存储的key
      const storageInfo = await this.getStorageInfo()

      // 计算本地存储大小
      const localStorageSize = this.calculateLocalStorageSize()
      totalSize += localStorageSize
      cacheDetails.localStorage = this.formatSize(localStorageSize)

      // 计算图片缓存大小（估算）
      const imageCacheSize = await this.calculateImageCacheSize()
      totalSize += imageCacheSize
      cacheDetails.imageCache = this.formatSize(imageCacheSize)

      // 计算临时文件大小
      const tempFileSize = await this.calculateTempFileSize()
      totalSize += tempFileSize
      cacheDetails.tempFiles = this.formatSize(tempFileSize)

      return {
        total: this.formatSize(totalSize),
        totalBytes: totalSize,
        details: cacheDetails,
        itemCount: storageInfo.keys.length
      }
    } catch (error) {
      console.error('计算缓存大小失败:', error)
      return {
        total: '未知',
        totalBytes: 0,
        details: {},
        itemCount: 0
      }
    }
  }

  /**
   * 获取存储信息
   */
  static async getStorageInfo() {
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      plus.storage.getLength((length) => {
        const keys = []
        let processedCount = 0

        if (length === 0) {
          resolve({ keys: [], length: 0 })
          return
        }

        for (let i = 0; i < length; i++) {
          plus.storage.key(i, (key) => {
            keys.push(key)
            processedCount++
            if (processedCount === length) {
              resolve({ keys, length })
            }
          })
        }
      })
      // #endif

      // #ifndef APP-PLUS
      // H5和小程序环境的处理
      try {
        const storageInfo = uni.getStorageInfoSync()
        resolve({
          keys: storageInfo.keys || [],
          length: storageInfo.keys ? storageInfo.keys.length : 0
        })
      } catch (error) {
        resolve({ keys: [], length: 0 })
      }
      // #endif
    })
  }

  /**
   * 计算本地存储大小
   */
  static calculateLocalStorageSize() {
    try {
      const storageInfo = uni.getStorageInfoSync()
      // 估算：每个字符约1字节，加上key的长度
      let size = 0

      if (storageInfo.keys) {
        storageInfo.keys.forEach(key => {
          try {
            const value = uni.getStorageSync(key)
            const valueStr = typeof value === 'string' ? value : JSON.stringify(value)
            size += key.length + valueStr.length
          } catch (error) {
            // 忽略单个key的错误
          }
        })
      }

      return size
    } catch (error) {
      return 0
    }
  }

  /**
   * 计算图片缓存大小（估算）
   */
  static async calculateImageCacheSize() {
    // 这里是估算值，实际图片缓存大小难以精确计算
    // 可以根据历史记录中的图片数量进行估算
    try {
      const historyData = HistoryManager.getAllHistory()
      const imageCount = historyData.length * 0.1 // 假设10%的记录包含图片
      return imageCount * 50 * 1024 // 每张图片平均50KB
    } catch (error) {
      return 1024 * 1024 // 默认1MB
    }
  }

  /**
   * 计算临时文件大小
   */
  static async calculateTempFileSize() {
    // 临时文件大小估算
    return 512 * 1024 // 默认512KB
  }

  /**
   * 格式化文件大小
   */
  static formatSize(bytes) {
    if (bytes === 0) return '0B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + sizes[i]
  }

  /**
   * 清除缓存
   * @param {Object} options 清除选项
   */
  static async clearCache(options = {}) {
    const {
      clearUserData = false,
      clearSettings = false,
      clearHistory = true,
      clearTempData = true,
      clearImageCache = true,
      clearApiCache = true
    } = options

    const results = {
      success: true,
      clearedItems: [],
      errors: [],
      freedSpace: 0
    }

    try {
      // 获取清除前的大小
      const beforeSize = await this.calculateCacheSize()

      // 获取所有存储的key
      const storageInfo = await this.getStorageInfo()

      for (const key of storageInfo.keys) {
        try {
          // 检查是否为受保护的key
          if (this.PROTECTED_KEYS.includes(key)) {
            continue
          }

          let shouldClear = false

          // 根据选项决定是否清除
          if (clearUserData && this.isUserDataKey(key)) {
            shouldClear = true
          } else if (clearSettings && this.isSettingsKey(key)) {
            shouldClear = true
          } else if (clearHistory && this.isHistoryKey(key)) {
            shouldClear = true
          } else if (clearTempData && this.isTempDataKey(key)) {
            shouldClear = true
          } else if (clearApiCache && this.isApiCacheKey(key)) {
            shouldClear = true
          }

          if (shouldClear) {
            uni.removeStorageSync(key)
            results.clearedItems.push(key)
          }
        } catch (error) {
          results.errors.push(`清除${key}失败: ${error.message}`)
        }
      }

      // 清除图片缓存
      if (clearImageCache) {
        await this.clearImageCache()
        results.clearedItems.push('图片缓存')
      }

      // 清除临时文件
      if (clearTempData) {
        await this.clearTempFiles()
        results.clearedItems.push('临时文件')
      }

      // 计算释放的空间
      const afterSize = await this.calculateCacheSize()
      results.freedSpace = beforeSize.totalBytes - afterSize.totalBytes

    } catch (error) {
      results.success = false
      results.errors.push(`清除缓存失败: ${error.message}`)
    }

    return results
  }

  /**
   * 判断key类型的辅助方法
   */
  static isUserDataKey(key) {
    return key.includes('user_') || key.includes('profile_')
  }

  static isSettingsKey(key) {
    return key.includes('settings') || key.includes('config')
  }

  static isHistoryKey(key) {
    return key.includes('history') || key.includes('record')
  }

  static isTempDataKey(key) {
    return key.includes('temp_') || key.includes('cache_') || key.includes('tmp_')
  }

  static isApiCacheKey(key) {
    return key.includes('api_') || key.includes('request_')
  }

  /**
   * 清除图片缓存
   */
  static async clearImageCache() {
    // #ifdef APP-PLUS
    try {
      // App端清除图片缓存
      plus.cache.clear(() => {
        console.log('图片缓存已清除')
      })
    } catch (error) {
      console.error('清除图片缓存失败:', error)
    }
    // #endif
  }

  /**
   * 清除临时文件
   */
  static async clearTempFiles() {
    // #ifdef APP-PLUS
    try {
      // App端清除临时文件
      const tempDir = plus.io.convertLocalFileSystemURL('_doc/temp/')
      plus.io.resolveLocalFileSystemURL(tempDir, (entry) => {
        entry.removeRecursively(() => {
          console.log('临时文件已清除')
        })
      })
    } catch (error) {
      console.error('清除临时文件失败:', error)
    }
    // #endif
  }

  /**
   * 获取缓存统计信息
   */
  static async getCacheStats() {
    const cacheSize = await this.calculateCacheSize()
    const storageInfo = await this.getStorageInfo()

    return {
      totalSize: cacheSize.total,
      totalBytes: cacheSize.totalBytes,
      itemCount: storageInfo.length,
      details: cacheSize.details,
      lastCalculated: new Date().toISOString()
    }
  }
}

export {
  StorageUtil,
  HistoryManager,
  SettingsManager,
  CacheManager
}
