// 本地存储工具类
class StorageUtil {
  // 保存数据
  static set(key, value) {
    try {
      uni.setStorageSync(key, JSON.stringify(value))
      return true
    } catch (error) {
      console.error('Storage set error:', error)
      return false
    }
  }
  
  // 获取数据
  static get(key, defaultValue = null) {
    try {
      const value = uni.getStorageSync(key)
      return value ? JSON.parse(value) : defaultValue
    } catch (error) {
      console.error('Storage get error:', error)
      return defaultValue
    }
  }
  
  // 删除数据
  static remove(key) {
    try {
      uni.removeStorageSync(key)
      return true
    } catch (error) {
      console.error('Storage remove error:', error)
      return false
    }
  }
  
  // 清空所有数据
  static clear() {
    try {
      uni.clearStorageSync()
      return true
    } catch (error) {
      console.error('Storage clear error:', error)
      return false
    }
  }
}

// 历史记录管理
class HistoryManager {
  static HISTORY_KEY = 'reply_history'
  static MAX_HISTORY = 100
  
  // 添加历史记录
  static addHistory(originalMessage, replyList, emotionResult) {
    const history = this.getHistory()
    const newRecord = {
      id: Date.now(),
      originalMessage,
      replyList,
      emotionResult,
      createTime: new Date().toISOString(),
      isFavorite: false
    }
    
    history.unshift(newRecord)
    
    // 限制历史记录数量
    if (history.length > this.MAX_HISTORY) {
      history.splice(this.MAX_HISTORY)
    }
    
    StorageUtil.set(this.HISTORY_KEY, history)
    return newRecord
  }
  
  // 获取历史记录
  static getHistory() {
    return StorageUtil.get(this.HISTORY_KEY, [])
  }
  
  // 删除历史记录
  static removeHistory(id) {
    const history = this.getHistory()
    const index = history.findIndex(item => item.id === id)
    if (index > -1) {
      history.splice(index, 1)
      StorageUtil.set(this.HISTORY_KEY, history)
      return true
    }
    return false
  }
  
  // 收藏/取消收藏
  static toggleFavorite(id) {
    const history = this.getHistory()
    const item = history.find(item => item.id === id)
    if (item) {
      item.isFavorite = !item.isFavorite
      StorageUtil.set(this.HISTORY_KEY, history)
      return item.isFavorite
    }
    return false
  }
  
  // 获取收藏列表
  static getFavorites() {
    return this.getHistory().filter(item => item.isFavorite)
  }
  
  // 清空历史记录
  static clearHistory() {
    StorageUtil.remove(this.HISTORY_KEY)
  }
}

// 用户设置管理
class SettingsManager {
  static SETTINGS_KEY = 'user_settings'
  
  static getDefaultSettings() {
    return {
      theme: 'light',
      fontSize: 'medium',
      autoSave: true,
      showFloatingBubble: false,
      replyStyles: ['warm_caring', 'humorous', 'rational', 'concise', 'romantic'],
      dailyQuota: 10,
      notifications: {
        enabled: true,
        sound: true,
        vibrate: true
      }
    }
  }
  
  // 获取设置
  static getSettings() {
    return StorageUtil.get(this.SETTINGS_KEY, this.getDefaultSettings())
  }
  
  // 更新设置
  static updateSettings(newSettings) {
    const currentSettings = this.getSettings()
    const updatedSettings = { ...currentSettings, ...newSettings }
    StorageUtil.set(this.SETTINGS_KEY, updatedSettings)
    return updatedSettings
  }
  
  // 重置设置
  static resetSettings() {
    const defaultSettings = this.getDefaultSettings()
    StorageUtil.set(this.SETTINGS_KEY, defaultSettings)
    return defaultSettings
  }
}

export {
  StorageUtil,
  HistoryManager,
  SettingsManager
}
