#!/bin/bash

# 一键部署脚本 - 前后端分离部署
# 使用方法: ./deploy-all.sh [环境] [部署方式]
# 示例: ./deploy-all.sh prod docker

ENVIRONMENT=${1:-dev}
DEPLOY_TYPE=${2:-traditional}

echo "🚀 开始一键部署情感回复助手应用"
echo "环境: $ENVIRONMENT"
echo "部署方式: $DEPLOY_TYPE"
echo "=================================="

# 检查必要的工具
check_requirements() {
    echo "🔍 检查部署环境..."
    
    if [ "$DEPLOY_TYPE" = "docker" ]; then
        if ! command -v docker &> /dev/null; then
            echo "❌ Docker 未安装，请先安装 Docker"
            exit 1
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
            exit 1
        fi
    else
        if ! command -v java &> /dev/null; then
            echo "❌ Java 未安装，请先安装 Java 8+"
            exit 1
        fi
        
        if ! command -v nginx &> /dev/null; then
            echo "❌ Nginx 未安装，请先安装 Nginx"
            exit 1
        fi
        
        if ! command -v mysql &> /dev/null; then
            echo "❌ MySQL 未安装，请先安装 MySQL"
            exit 1
        fi
    fi
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    echo "✅ 环境检查通过"
}

# 构建后端
build_backend() {
    echo "🔨 构建后端服务..."
    cd ../backend-service
    
    # 运行测试
    echo "🧪 运行测试..."
    mvn test
    if [ $? -ne 0 ]; then
        echo "❌ 测试失败，停止部署"
        exit 1
    fi
    
    # 构建JAR包
    echo "📦 构建JAR包..."
    mvn clean package -DskipTests
    if [ $? -ne 0 ]; then
        echo "❌ 后端构建失败"
        exit 1
    fi
    
    echo "✅ 后端构建完成"
    cd ../deploy
}

# 构建前端
build_frontend() {
    echo "🔨 构建前端应用..."
    cd ../uniapp-frontend
    
    # 安装依赖
    echo "📦 安装前端依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 前端依赖安装失败"
        exit 1
    fi
    
    # 构建H5版本
    echo "🌐 构建H5版本..."
    npm run build:h5
    if [ $? -ne 0 ]; then
        echo "❌ 前端构建失败"
        exit 1
    fi
    
    echo "✅ 前端构建完成"
    cd ../deploy
}

# Docker 部署
deploy_with_docker() {
    echo "🐳 使用 Docker 部署..."
    
    # 检查环境变量文件
    if [ ! -f ".env" ]; then
        echo "⚠️ 未找到 .env 文件，复制示例文件..."
        cp .env.example .env
        echo "❗ 请编辑 .env 文件并填入正确的配置值"
        echo "编辑完成后重新运行部署脚本"
        exit 1
    fi
    
    # 停止现有容器
    echo "⏹️ 停止现有容器..."
    docker-compose down
    
    # 构建并启动容器
    echo "🚀 启动容器..."
    docker-compose up -d --build
    
    # 等待服务启动
    echo "⏳ 等待服务启动..."
    sleep 30
    
    # 健康检查
    echo "🔍 健康检查..."
    if curl -f http://localhost/api/actuator/health > /dev/null 2>&1; then
        echo "✅ Docker 部署成功!"
    else
        echo "❌ 健康检查失败"
        docker-compose logs
        exit 1
    fi
}

# 传统部署
deploy_traditional() {
    echo "🏗️ 使用传统方式部署..."
    
    # 部署后端
    echo "📡 部署后端服务..."
    ./backend-deploy.sh $ENVIRONMENT
    if [ $? -ne 0 ]; then
        echo "❌ 后端部署失败"
        exit 1
    fi
    
    # 部署前端
    echo "🌐 部署前端应用..."
    ./frontend-deploy.sh $ENVIRONMENT h5
    if [ $? -ne 0 ]; then
        echo "❌ 前端部署失败"
        exit 1
    fi
    
    echo "✅ 传统部署完成!"
}

# 部署后检查
post_deploy_check() {
    echo "🔍 部署后检查..."
    
    # 检查后端API
    echo "检查后端API..."
    for i in {1..10}; do
        if curl -f http://localhost:8080/api/actuator/health > /dev/null 2>&1; then
            echo "✅ 后端API正常"
            break
        fi
        echo "⏳ 等待后端启动... ($i/10)"
        sleep 3
    done
    
    # 检查前端
    echo "检查前端应用..."
    if curl -f http://localhost/ > /dev/null 2>&1; then
        echo "✅ 前端应用正常"
    else
        echo "❌ 前端应用异常"
    fi
    
    # 显示部署信息
    echo ""
    echo "🎉 部署完成!"
    echo "=================================="
    echo "📱 前端地址: http://localhost"
    echo "📡 后端API: http://localhost:8080/api"
    echo "📊 健康检查: http://localhost:8080/api/actuator/health"
    if [ "$DEPLOY_TYPE" = "docker" ]; then
        echo "📈 监控面板: http://localhost:3000 (Grafana)"
        echo "📊 指标收集: http://localhost:9090 (Prometheus)"
    fi
    echo "=================================="
}

# 主流程
main() {
    check_requirements
    build_backend
    build_frontend
    
    case $DEPLOY_TYPE in
        "docker")
            deploy_with_docker
            ;;
        "traditional")
            deploy_traditional
            ;;
        *)
            echo "❌ 不支持的部署方式: $DEPLOY_TYPE"
            echo "支持的部署方式: docker, traditional"
            exit 1
            ;;
    esac
    
    post_deploy_check
}

# 执行主流程
main
