package com.emotional.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.emotional.service.entity.UserSettings;
import com.emotional.service.mapper.UserSettingsMapper;
import com.emotional.service.service.UserSettingsService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 用户设置服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserSettingsServiceImpl extends ServiceImpl<UserSettingsMapper, UserSettings> implements UserSettingsService {
    
    private final UserSettingsMapper userSettingsMapper;
    private final ObjectMapper objectMapper;
    
    @Override
    public UserSettings getByUserId(Long userId) {
        log.info("查询用户设置: userId={}", userId);
        UserSettings settings = userSettingsMapper.getByUserId(userId);

        if (settings == null) {
            log.info("用户{}没有设置记录，创建默认设置", userId);
            // 如果用户没有设置，创建默认设置
            settings = initDefaultSettings(userId);
        } else {
            log.info("用户{}的设置: replyGenerationMode={}, primaryStyle={}, replyStyles={}",
                    userId, settings.getReplyGenerationMode(), settings.getPrimaryStyle(), settings.getReplyStyles());
        }
        return settings;
    }
    
    @Override
    public List<String> getUserReplyStyles(Long userId) {
        UserSettings settings = getByUserId(userId);
        if (settings == null || settings.getReplyStyles() == null) {
            // 返回与默认生成数量一致的风格列表
            return Arrays.asList("romantic", "humorous");
        }

        try {
            return objectMapper.readValue(settings.getReplyStyles(), new TypeReference<List<String>>() {});
        } catch (Exception e) {
            log.error("解析用户回复风格失败: userId={}", userId, e);
            // 返回与默认生成数量一致的风格列表
            return Arrays.asList("romantic", "humorous");
        }
    }
    
    @Override
    public Integer getUserPreferredReplyCount(Long userId) {
        UserSettings settings = getByUserId(userId);
        if (settings == null || settings.getPreferredReplyCount() == null) {
            return 2; // 默认生成2个回复
        }
        return settings.getPreferredReplyCount();
    }
    
    @Override
    public String getUserReplyGenerationMode(Long userId) {
        UserSettings settings = getByUserId(userId);
        if (settings == null || settings.getReplyGenerationMode() == null) {
            return "smart"; // 默认智能模式
        }
        return settings.getReplyGenerationMode();
    }
    
    @Override
    public String getUserPrimaryStyle(Long userId) {
        UserSettings settings = getByUserId(userId);
        if (settings == null || settings.getPrimaryStyle() == null) {
            return "romantic"; // 默认撩妹风格
        }
        return settings.getPrimaryStyle();
    }
    
    @Override
    public boolean updateUserSettings(Long userId, UserSettings newSettings) {
        try {
            UserSettings existingSettings = getByUserId(userId);
            
            if (existingSettings == null) {
                // 创建新设置
                newSettings.setUserId(userId);
                newSettings.setCreateTime(LocalDateTime.now());
                newSettings.setUpdateTime(LocalDateTime.now());
                return this.save(newSettings);
            } else {
                // 更新现有设置
                existingSettings.setTheme(newSettings.getTheme());
                existingSettings.setFontSize(newSettings.getFontSize());
                existingSettings.setAutoSave(newSettings.getAutoSave());
                existingSettings.setShowFloatingBubble(newSettings.getShowFloatingBubble());
                existingSettings.setReplyStyles(newSettings.getReplyStyles());
                existingSettings.setPreferredReplyCount(newSettings.getPreferredReplyCount());
                existingSettings.setReplyGenerationMode(newSettings.getReplyGenerationMode());
                existingSettings.setPrimaryStyle(newSettings.getPrimaryStyle());
                existingSettings.setNotificationEnabled(newSettings.getNotificationEnabled());
                existingSettings.setNotificationSound(newSettings.getNotificationSound());
                existingSettings.setNotificationVibrate(newSettings.getNotificationVibrate());
                existingSettings.setUpdateTime(LocalDateTime.now());
                
                return this.updateById(existingSettings);
            }
        } catch (Exception e) {
            log.error("更新用户设置失败: userId={}", userId, e);
            return false;
        }
    }
    
    @Override
    public UserSettings initDefaultSettings(Long userId) {
        try {
            UserSettings defaultSettings = new UserSettings();
            defaultSettings.setUserId(userId);
            defaultSettings.setTheme("light");
            defaultSettings.setFontSize("medium");
            defaultSettings.setAutoSave(1);
            defaultSettings.setShowFloatingBubble(0);
            // 确保默认风格数量与首选生成数量一致
            defaultSettings.setPreferredReplyCount(2);
            defaultSettings.setReplyStyles("[\"romantic\", \"humorous\"]");
            defaultSettings.setReplyGenerationMode("smart");
            defaultSettings.setPrimaryStyle("romantic");
            defaultSettings.setNotificationEnabled(1);
            defaultSettings.setNotificationSound(1);
            defaultSettings.setNotificationVibrate(1);
            defaultSettings.setCreateTime(LocalDateTime.now());
            defaultSettings.setUpdateTime(LocalDateTime.now());
            
            if (this.save(defaultSettings)) {
                log.info("初始化用户默认设置成功: userId={}", userId);
                return defaultSettings;
            } else {
                log.error("初始化用户默认设置失败: userId={}", userId);
                return null;
            }
        } catch (Exception e) {
            log.error("初始化用户默认设置异常: userId={}", userId, e);
            return null;
        }
    }
}
