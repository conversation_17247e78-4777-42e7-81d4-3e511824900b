package com.emotionalreply.controller;

import com.emotionalreply.common.Result;
import com.emotionalreply.service.VersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 版本管理控制器
 */
@Api(tags = "版本管理")
@RestController
@RequestMapping("/api/version")
@CrossOrigin(origins = "*")
public class VersionController {

    @Autowired
    private VersionService versionService;

    /**
     * 检查应用更新
     */
    @ApiOperation("检查应用更新")
    @GetMapping("/check")
    public Result<Map<String, Object>> checkUpdate(
            @ApiParam("当前版本号") @RequestParam String currentVersion,
            @ApiParam("平台类型") @RequestParam(defaultValue = "android") String platform) {
        
        try {
            Map<String, Object> updateInfo = versionService.checkUpdate(currentVersion, platform);
            return Result.success("检查更新成功", updateInfo);
        } catch (Exception e) {
            return Result.error("检查更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前最新版本信息
     */
    @ApiOperation("获取最新版本信息")
    @GetMapping("/latest")
    public Result<Map<String, Object>> getLatestVersion(
            @ApiParam("平台类型") @RequestParam(defaultValue = "android") String platform) {
        
        try {
            Map<String, Object> versionInfo = versionService.getLatestVersion(platform);
            return Result.success("获取版本信息成功", versionInfo);
        } catch (Exception e) {
            return Result.error("获取版本信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取版本更新历史
     */
    @ApiOperation("获取版本更新历史")
    @GetMapping("/history")
    public Result<Map<String, Object>> getVersionHistory(
            @ApiParam("页码") @RequestParam(defaultValue = "1") int page,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") int size) {
        
        try {
            Map<String, Object> history = versionService.getVersionHistory(page, size);
            return Result.success("获取版本历史成功", history);
        } catch (Exception e) {
            return Result.error("获取版本历史失败: " + e.getMessage());
        }
    }

    /**
     * 记录用户更新行为
     */
    @ApiOperation("记录用户更新行为")
    @PostMapping("/update-log")
    public Result<Void> logUpdateAction(@RequestBody Map<String, Object> logData) {
        try {
            versionService.logUpdateAction(logData);
            return Result.success("记录更新行为成功");
        } catch (Exception e) {
            return Result.error("记录更新行为失败: " + e.getMessage());
        }
    }
}
