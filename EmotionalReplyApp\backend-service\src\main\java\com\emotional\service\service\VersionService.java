package com.emotional.service.service;

import com.emotional.service.entity.AppVersion;
import com.emotional.service.mapper.AppVersionMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 版本管理服务
 */
@Service
public class VersionService {

    @Autowired
    private AppVersionMapper appVersionMapper;

    /**
     * 检查更新
     */
    public Map<String, Object> checkUpdate(String currentVersion, String platform) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取该平台最新版本
        AppVersion latestVersion = getLatestVersion(platform);
        
        if (latestVersion == null) {
            result.put("hasUpdate", false);
            result.put("message", "未找到该平台的版本信息");
            return result;
        }
        
        // 比较版本号
        int compareResult = compareVersions(currentVersion, latestVersion.getVersionName());
        
        if (compareResult < 0) {
            // 有新版本
            result.put("hasUpdate", true);
            result.put("versionInfo", latestVersion);
            result.put("isForceUpdate", latestVersion.getIsForceUpdate());
        } else {
            // 已是最新版本
            result.put("hasUpdate", false);
            result.put("message", "已是最新版本");
        }
        
        return result;
    }

    /**
     * 获取最新版本
     */
    public AppVersion getLatestVersion(String platform) {
        QueryWrapper<AppVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("platform", platform)
                   .eq("status", "published")
                   .orderByDesc("version_code")
                   .last("LIMIT 1");
        
        return appVersionMapper.selectOne(queryWrapper);
    }

    /**
     * 获取版本历史
     */
    public Map<String, Object> getVersionHistory(int page, int size) {
        Page<AppVersion> pageObj = new Page<>(page, size);
        QueryWrapper<AppVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", "published")
                   .orderByDesc("release_date");
        
        IPage<AppVersion> versionPage = appVersionMapper.selectPage(pageObj, queryWrapper);
        
        Map<String, Object> result = new HashMap<>();
        result.put("list", versionPage.getRecords());
        result.put("total", versionPage.getTotal());
        result.put("totalPages", versionPage.getPages());
        result.put("currentPage", page);
        result.put("pageSize", size);
        
        return result;
    }

    /**
     * 记录用户更新行为
     */
    public void logUpdateAction(Map<String, Object> logData) {
        // 这里可以记录到数据库或日志文件
        // 暂时只打印日志
        System.out.println("用户更新行为记录: " + logData.toString());
        
        // 如果需要存储到数据库，可以创建 VersionUpdateLog 实体和对应的 Mapper
        // 然后在这里插入记录
    }

    /**
     * 版本号比较
     * 返回值：-1表示version1 < version2，0表示相等，1表示version1 > version2
     */
    private int compareVersions(String version1, String version2) {
        if (version1.equals(version2)) {
            return 0;
        }
        
        String[] v1Parts = version1.split("\\.");
        String[] v2Parts = version2.split("\\.");
        
        int maxLength = Math.max(v1Parts.length, v2Parts.length);
        
        for (int i = 0; i < maxLength; i++) {
            int v1Part = i < v1Parts.length ? Integer.parseInt(v1Parts[i]) : 0;
            int v2Part = i < v2Parts.length ? Integer.parseInt(v2Parts[i]) : 0;
            
            if (v1Part < v2Part) {
                return -1;
            } else if (v1Part > v2Part) {
                return 1;
            }
        }
        
        return 0;
    }

    /**
     * 根据ID获取版本信息
     */
    public AppVersion getVersionById(Long id) {
        return appVersionMapper.selectById(id);
    }

    /**
     * 发布新版本
     */
    public void publishVersion(AppVersion appVersion) {
        appVersion.setStatus("published");
        appVersion.setReleaseDate(LocalDateTime.now());
        appVersionMapper.insert(appVersion);
    }

    /**
     * 下架版本
     */
    public void unpublishVersion(Long id) {
        AppVersion appVersion = new AppVersion();
        appVersion.setId(id);
        appVersion.setStatus("unpublished");
        appVersionMapper.updateById(appVersion);
    }

    /**
     * 删除版本
     */
    public void deleteVersion(Long id) {
        appVersionMapper.deleteById(id);
    }

    /**
     * 获取版本列表（管理端使用）
     */
    public Map<String, Object> getVersionList(int page, int size, String platform, String status) {
        Page<AppVersion> pageObj = new Page<>(page, size);
        QueryWrapper<AppVersion> queryWrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(platform)) {
            queryWrapper.eq("platform", platform);
        }
        
        if (StringUtils.hasText(status)) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.orderByDesc("create_time");
        
        IPage<AppVersion> versionPage = appVersionMapper.selectPage(pageObj, queryWrapper);
        
        Map<String, Object> result = new HashMap<>();
        result.put("list", versionPage.getRecords());
        result.put("total", versionPage.getTotal());
        result.put("totalPages", versionPage.getPages());
        result.put("currentPage", page);
        result.put("pageSize", size);
        
        return result;
    }
}
