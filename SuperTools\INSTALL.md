# SuperTools 安装指南

## 📋 系统要求

- **Python**: 3.8 或更高版本
- **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **内存**: 建议 4GB 以上
- **存储**: 建议 2GB 可用空间

## 🚀 快速安装

### 1. 克隆项目
```bash
git clone <repository-url>
cd SuperTools
```

### 2. 创建虚拟环境
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

### 3. 安装核心依赖
```bash
pip install -r requirements.txt
```

### 4. 配置数据库
```bash
# 创建 MySQL 数据库
mysql -u root -p
CREATE DATABASE supertools CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 5. 配置环境变量
复制 `scripts/production.env.example` 为 `scripts/production.env` 并填写配置：
```bash
cp scripts/production.env.example scripts/production.env
```

### 6. 初始化数据库
```bash
python scripts/init_vote_data.py
```

### 7. 启动应用
```bash
python run.py
```

## 🔧 功能模块安装

### 证件照功能
证件照功能需要额外的依赖：
```bash
# 安装证件照相关依赖
pip install opencv-python==********
pip install onnxruntime==1.16.3
pip install fastapi==0.104.1
pip install uvicorn==0.24.0
```

### 网页截图功能
CSDN 文章截图需要 Playwright：
```bash
pip install playwright==1.40.0
playwright install chromium
```

### 高级爬虫功能
抖音解析需要 DrissionPage：
```bash
pip install DrissionPage==*******
```

## 🌐 生产环境部署

### 使用 Gunicorn (Linux/macOS)
```bash
pip install gunicorn==21.2.0
gunicorn -w 4 -b 0.0.0.0:5000 "backend.main:app"
```

### 使用 Waitress (Windows)
```bash
pip install waitress==2.1.2
waitress-serve --host=0.0.0.0 --port=5000 backend.main:app
```

## 📦 依赖说明

### 核心依赖 (requirements.txt)
- **Flask**: Web 框架
- **SQLAlchemy**: 数据库 ORM
- **Requests**: HTTP 请求库
- **BeautifulSoup4**: HTML 解析
- **Pillow**: 图像处理
- **PyMySQL**: MySQL 数据库驱动

### 可选依赖 (requirements-optional.txt)
- **Playwright**: 网页截图
- **DrissionPage**: 高级爬虫
- **OpenCV**: 图像处理
- **ONNX Runtime**: AI 模型推理

## 🔍 故障排除

### 常见问题

1. **MySQL 连接失败**
   - 检查数据库服务是否启动
   - 验证连接参数是否正确
   - 确保数据库用户有足够权限

2. **证件照功能不可用**
   - 确保安装了 OpenCV 和 ONNX Runtime
   - 检查 HivisionIDPhotos 服务是否启动
   - 验证模型文件是否存在

3. **爬虫功能异常**
   - 检查网络连接
   - 验证 Cookie 配置
   - 确保目标网站可访问

4. **内存不足**
   - 减少并发数量
   - 增加系统内存
   - 使用轻量级配置

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

## 🔒 安全配置

### 生产环境建议
1. 修改默认密钥
2. 启用 HTTPS
3. 配置防火墙
4. 定期更新依赖
5. 监控系统资源

### 环境变量配置
```bash
# 必需配置
SECRET_KEY=your-secret-key
MYSQL_PASSWORD=your-db-password

# 可选配置
QQ_EMAIL=<EMAIL>
QQ_AUTH_CODE=your-auth-code
```

## 📞 技术支持

如果遇到安装问题，请：
1. 检查系统要求
2. 查看错误日志
3. 参考故障排除指南
4. 提交 Issue 报告

---

**注意**: 首次安装可能需要较长时间下载依赖包，请耐心等待。
