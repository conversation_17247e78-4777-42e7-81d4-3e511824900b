# SuperTools 生产环境配置示例
# 复制此文件为 production.env 并填入真实的配置值

# 应用环境配置
FLASK_ENV=production
DEBUG=False
PRODUCTION=true
SECRET_KEY=your_secret_key_here_at_least_32_characters_long

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=supertools_user
MYSQL_PASSWORD=your_secure_database_password
MYSQL_DATABASE=supertools

# 容联云通讯短信配置
RONGLIAN_ACC_ID=your_account_id
RONGLIAN_ACC_TOKEN=your_account_token
RONGLIAN_APP_ID=your_app_id
RONGLIAN_VERIFY_TEMPLATE_ID=your_template_id

# 第三方平台Cookie配置（可选）
KUAI_SHOU_COOKIE=your_kuaishou_cookie_here
DOU_YIN_COOKIE=your_douyin_cookie_here
BILIBILI_COOKIE=your_bilibili_cookie_here

# 日志配置
LOG_LEVEL=WARNING

# 安全设置
HIDE_INTERNAL_IPS=true
ENABLE_SECURITY_MIDDLEWARE=true

# 服务器配置
HOST=0.0.0.0
PORT=5000

# 证件照服务配置（可选）
HIVISION_API_URL=http://localhost:8080

# 邮件配置（可选）
MAIL_SERVER=smtp.example.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password

# Redis配置（可选，用于缓存）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# 文件上传配置
MAX_CONTENT_LENGTH=********  # 50MB
UPLOAD_FOLDER=uploads

# 备份配置
BACKUP_ENABLED=true
BACKUP_INTERVAL=24  # 小时
BACKUP_RETENTION=7  # 天

# 监控配置
ENABLE_MONITORING=true
HEALTH_CHECK_INTERVAL=60  # 秒

# 注意事项：
# 1. 请将此文件复制为 production.env
# 2. 填入真实的配置值
# 3. 确保 production.env 已添加到 .gitignore
# 4. 定期更新密码和密钥
# 5. 使用强密码和随机密钥
