package com.emotional.service.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户设置实体类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_settings")
public class UserSettings {
    
    /**
     * 设置ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 主题：light-浅色，dark-深色
     */
    @TableField("theme")
    private String theme;
    
    /**
     * 字体大小：small-小，medium-中，large-大
     */
    @TableField("font_size")
    private String fontSize;
    
    /**
     * 自动保存：0-关闭，1-开启
     */
    @TableField("auto_save")
    private Integer autoSave;
    
    /**
     * 显示悬浮气泡：0-关闭，1-开启
     */
    @TableField("show_floating_bubble")
    private Integer showFloatingBubble;
    
    /**
     * 偏好的回复风格列表（JSON格式）
     */
    @TableField("reply_styles")
    private String replyStyles;
    
    /**
     * 偏好生成回复数量：1-3个
     */
    @TableField("preferred_reply_count")
    private Integer preferredReplyCount;
    
    /**
     * 回复生成模式：smart-智能选择，custom-自定义风格，single-单一风格
     */
    @TableField("reply_generation_mode")
    private String replyGenerationMode;
    
    /**
     * 主要回复风格
     */
    @TableField("primary_style")
    private String primaryStyle;
    
    /**
     * 通知开启：0-关闭，1-开启
     */
    @TableField("notification_enabled")
    private Integer notificationEnabled;
    
    /**
     * 通知声音：0-关闭，1-开启
     */
    @TableField("notification_sound")
    private Integer notificationSound;
    
    /**
     * 通知震动：0-关闭，1-开启
     */
    @TableField("notification_vibrate")
    private Integer notificationVibrate;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
