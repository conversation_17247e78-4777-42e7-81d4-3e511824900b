<template>
  <view class="container">
    <!-- 当前版本信息 -->
    <view class="current-version">
      <view class="version-header">
        <image class="app-icon" src="/static/logo.png" mode="aspectFit"></image>
        <view class="version-info">
          <text class="app-name">情感回复助手</text>
          <text class="version-text">当前版本 {{ currentVersion.versionName }}</text>
        </view>
      </view>
      
      <button class="check-btn" @click="checkUpdate" :loading="checking">
        {{ checking ? '检查中...' : '检查更新' }}
      </button>
    </view>

    <!-- 最新版本信息 -->
    <view class="latest-version" v-if="latestVersionInfo">
      <view class="section-title">
        <text class="title-text">最新版本</text>
        <text class="version-tag" :class="{ 'update-available': hasUpdate }">
          {{ hasUpdate ? '有更新' : '最新' }}
        </text>
      </view>
      
      <view class="version-card">
        <view class="version-header">
          <text class="version-name">{{ latestVersionInfo.versionName }}</text>
          <text class="release-date">{{ formatDate(latestVersionInfo.releaseDate) }}</text>
        </view>
        
        <view class="version-details">
          <text class="platform">{{ getPlatformName(latestVersionInfo.platform) }}</text>
          <text class="file-size" v-if="latestVersionInfo.fileSize">{{ latestVersionInfo.fileSize }}</text>
        </view>
        
        <view class="update-content" v-if="latestVersionInfo.updateContent">
          <text class="content-title">更新内容：</text>
          <text class="content-text">{{ latestVersionInfo.updateContent }}</text>
        </view>
        
        <button class="update-btn" v-if="hasUpdate" @click="handleUpdate">
          立即更新
        </button>
      </view>
    </view>

    <!-- 版本历史 -->
    <view class="version-history">
      <view class="section-title">
        <text class="title-text">版本历史</text>
        <text class="history-count" v-if="versionHistory.length">{{ versionHistory.length }}个版本</text>
      </view>
      
      <view class="history-list">
        <view class="history-item" v-for="version in versionHistory" :key="version.id">
          <view class="history-header">
            <text class="history-version">{{ version.versionName }}</text>
            <text class="history-date">{{ formatDate(version.releaseDate) }}</text>
          </view>
          
          <view class="history-content" v-if="version.updateContent">
            <text class="history-text">{{ version.updateContent }}</text>
          </view>
        </view>
      </view>
      
      <view class="load-more" v-if="canLoadMore">
        <button class="load-more-btn" @click="loadMoreHistory" :loading="loadingMore">
          {{ loadingMore ? '加载中...' : '加载更多' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { getVersionHistory, getLatestVersion, compareVersions, checkUpdate, logUpdateAction } from '../../api/version.js'

export default {
  name: 'VersionPage',
  data() {
    return {
      currentVersion: {
        versionName: '1.0.0',
        versionCode: 100,
        platform: 'h5'
      },
      latestVersionInfo: null,
      hasUpdate: false,
      checking: false,
      versionHistory: [],
      currentPage: 1,
      pageSize: 10,
      canLoadMore: true,
      loadingMore: false
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  methods: {
    // 初始化页面
    async initPage() {
      await this.getCurrentVersionInfo()
      await this.getLatestVersionInfo()
      await this.loadVersionHistory()
    },
    
    // 获取当前版本信息
    async getCurrentVersionInfo() {
      try {
        const { getCurrentVersion } = await import('../../api/version.js')
        this.currentVersion = getCurrentVersion()
      } catch (error) {
        console.error('获取当前版本信息失败:', error)
      }
    },
    
    // 获取最新版本信息
    async getLatestVersionInfo() {
      try {
        const response = await getLatestVersion(this.currentVersion.platform)
        
        if (response.success && response.data) {
          this.latestVersionInfo = response.data
          this.hasUpdate = compareVersions(this.currentVersion.versionName, this.latestVersionInfo.versionName) < 0
        }
      } catch (error) {
        console.error('获取最新版本信息失败:', error)
      }
    },
    
    // 检查更新
    async checkUpdate() {
      this.checking = true
      
      try {
        // 记录检查行为
        await logUpdateAction({
          action: 'check',
          currentVersion: this.currentVersion.versionName,
          platform: this.currentVersion.platform
        })
        
        const response = await checkUpdate(this.currentVersion.versionName, this.currentVersion.platform)
        
        if (response.success && response.data) {
          const updateInfo = response.data
          this.hasUpdate = updateInfo.hasUpdate
          
          if (updateInfo.hasUpdate && updateInfo.versionInfo) {
            this.latestVersionInfo = updateInfo.versionInfo
            uni.showToast({
              title: '发现新版本',
              icon: 'success'
            })
          } else {
            uni.showToast({
              title: '已是最新版本',
              icon: 'success'
            })
          }
        }
      } catch (error) {
        console.error('检查更新失败:', error)
        uni.showToast({
          title: '检查失败',
          icon: 'none'
        })
      } finally {
        this.checking = false
      }
    },
    
    // 处理更新
    async handleUpdate() {
      if (!this.latestVersionInfo) return
      
      try {
        // 记录更新行为
        await logUpdateAction({
          action: 'download',
          currentVersion: this.currentVersion.versionName,
          targetVersion: this.latestVersionInfo.versionName,
          platform: this.currentVersion.platform
        })
        
        if (this.latestVersionInfo.downloadUrl) {
          // #ifdef APP-PLUS
          plus.runtime.openURL(this.latestVersionInfo.downloadUrl)
          // #endif
          
          // #ifdef H5
          window.open(this.latestVersionInfo.downloadUrl, '_blank')
          // #endif
          
          uni.showToast({
            title: '正在跳转下载',
            icon: 'success'
          })
        } else {
          uni.showModal({
            title: '更新提示',
            content: '请前往应用商店搜索"情感回复助手"进行更新',
            showCancel: false
          })
        }
      } catch (error) {
        console.error('处理更新失败:', error)
        uni.showToast({
          title: '更新失败',
          icon: 'none'
        })
      }
    },
    
    // 加载版本历史
    async loadVersionHistory() {
      try {
        const response = await getVersionHistory(this.currentPage, this.pageSize)
        
        if (response.success && response.data) {
          const { list, totalPages } = response.data
          
          if (this.currentPage === 1) {
            this.versionHistory = list
          } else {
            this.versionHistory.push(...list)
          }
          
          this.canLoadMore = this.currentPage < totalPages
        }
      } catch (error) {
        console.error('加载版本历史失败:', error)
      }
    },
    
    // 加载更多历史
    async loadMoreHistory() {
      if (!this.canLoadMore || this.loadingMore) return
      
      this.loadingMore = true
      this.currentPage++
      
      try {
        await this.loadVersionHistory()
      } finally {
        this.loadingMore = false
      }
    },
    
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      
      const date = new Date(dateString)
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (days === 0) {
        return '今天'
      } else if (days === 1) {
        return '昨天'
      } else if (days < 7) {
        return `${days}天前`
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    },
    
    // 获取平台名称
    getPlatformName(platform) {
      const { getPlatformName } = require('../../api/version.js')
      return getPlatformName(platform)
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.current-version {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .version-header {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    
    .app-icon {
      width: 80rpx;
      height: 80rpx;
      margin-right: 20rpx;
    }
    
    .version-info {
      flex: 1;
      
      .app-name {
        display: block;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .version-text {
        display: block;
        font-size: 26rpx;
        color: #666;
      }
    }
  }
  
  .check-btn {
    width: 100%;
    background: linear-gradient(135deg, #2196F3, #21CBF3);
    color: white;
    border: none;
    border-radius: 15rpx;
    padding: 25rpx;
    font-size: 30rpx;
  }
}

.latest-version, .version-history {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  
  .title-text {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
  
  .version-tag {
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 22rpx;
    background: #f0f0f0;
    color: #666;
    
    &.update-available {
      background: #ff4757;
      color: white;
    }
  }
  
  .history-count {
    font-size: 24rpx;
    color: #999;
  }
}

.version-card {
  border: 2rpx solid #f0f0f0;
  border-radius: 15rpx;
  padding: 25rpx;
  
  .version-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15rpx;
    
    .version-name {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
    }
    
    .release-date {
      font-size: 24rpx;
      color: #999;
    }
  }
  
  .version-details {
    display: flex;
    gap: 20rpx;
    margin-bottom: 20rpx;
    
    .platform, .file-size {
      font-size: 24rpx;
      color: #666;
      padding: 6rpx 12rpx;
      background: #f8f9fa;
      border-radius: 8rpx;
    }
  }
  
  .update-content {
    margin-bottom: 25rpx;
    
    .content-title {
      display: block;
      font-size: 26rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }
    
    .content-text {
      display: block;
      font-size: 24rpx;
      color: #666;
      line-height: 1.6;
      white-space: pre-line;
    }
  }
  
  .update-btn {
    width: 100%;
    background: #ff4757;
    color: white;
    border: none;
    border-radius: 12rpx;
    padding: 20rpx;
    font-size: 28rpx;
  }
}

.history-list {
  .history-item {
    border-bottom: 2rpx solid #f0f0f0;
    padding: 20rpx 0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10rpx;
      
      .history-version {
        font-size: 26rpx;
        font-weight: bold;
        color: #333;
      }
      
      .history-date {
        font-size: 22rpx;
        color: #999;
      }
    }
    
    .history-content {
      .history-text {
        font-size: 24rpx;
        color: #666;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }
}

.load-more {
  text-align: center;
  margin-top: 20rpx;
  
  .load-more-btn {
    background: #f8f9fa;
    color: #666;
    border: none;
    border-radius: 12rpx;
    padding: 20rpx 40rpx;
    font-size: 26rpx;
  }
}
</style>
