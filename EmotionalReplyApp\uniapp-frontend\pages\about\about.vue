<template>
  <view class="container">
    <!-- 应用信息 -->
    <view class="app-info">
      <image class="app-logo" src="/static/logo.png" mode="aspectFit"></image>
      <text class="app-name">情感回复助手</text>
      <text class="app-version">版本 1.0.0</text>
      <text class="app-slogan">智能分析，贴心回复</text>
    </view>

    <!-- 功能介绍 -->
    <view class="feature-section">
      <text class="section-title">✨ 核心功能</text>
      <view class="feature-list">
        <view class="feature-item">
          <text class="feature-icon">🧠</text>
          <view class="feature-content">
            <text class="feature-title">智能情感分析</text>
            <text class="feature-desc">准确识别消息中的情感倾向</text>
          </view>
        </view>
        
        <view class="feature-item">
          <text class="feature-icon">💬</text>
          <view class="feature-content">
            <text class="feature-title">多风格回复生成</text>
            <text class="feature-desc">温暖关怀、幽默风趣、理性分析等多种风格</text>
          </view>
        </view>
        
        <view class="feature-item">
          <text class="feature-icon">📋</text>
          <view class="feature-content">
            <text class="feature-title">一键复制使用</text>
            <text class="feature-desc">快速复制回复内容到聊天应用</text>
          </view>
        </view>
        
        <view class="feature-item">
          <text class="feature-icon">📚</text>
          <view class="feature-content">
            <text class="feature-title">历史记录管理</text>
            <text class="feature-desc">保存和管理历史回复记录</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用说明 -->
    <view class="usage-section">
      <text class="section-title">📖 使用说明</text>
      <view class="usage-steps">
        <view class="step-item">
          <text class="step-number">1</text>
          <text class="step-text">复制收到的消息内容</text>
        </view>
        <view class="step-item">
          <text class="step-number">2</text>
          <text class="step-text">在应用中粘贴或输入消息</text>
        </view>
        <view class="step-item">
          <text class="step-number">3</text>
          <text class="step-text">系统自动分析情感并生成回复</text>
        </view>
        <view class="step-item">
          <text class="step-number">4</text>
          <text class="step-text">选择合适的回复并复制使用</text>
        </view>
      </view>
    </view>

    <!-- 技术信息 -->
    <view class="tech-section">
      <text class="section-title">🔧 技术架构</text>
      <view class="tech-list">
        <text class="tech-item">前端：uni-app 跨平台开发</text>
        <text class="tech-item">后端：Spring Boot + MySQL</text>
        <text class="tech-item">AI：智能情感分析引擎</text>
        <text class="tech-item">部署：Docker 容器化部署</text>
      </view>
    </view>

    <!-- 联系信息 -->
    <view class="contact-section">
      <text class="section-title">📞 联系我们</text>
      <view class="contact-list">
        <view class="contact-item" @click="copyEmail">
          <text class="contact-icon">📧</text>
          <text class="contact-text">邮箱：<EMAIL></text>
        </view>
        <view class="contact-item">
          <text class="contact-icon">🌐</text>
          <text class="contact-text">官网：www.emotional-reply.com</text>
        </view>
        <view class="contact-item">
          <text class="contact-icon">📱</text>
          <text class="contact-text">微信：EmotionalReplyApp</text>
        </view>
      </view>
    </view>

    <!-- 版权信息 -->
    <view class="copyright">
      <text class="copyright-text">© 2024 情感回复助手</text>
      <text class="copyright-text">保留所有权利</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AboutPage',
  
  methods: {
    // 复制邮箱地址
    copyEmail() {
      uni.setClipboardData({
        data: '<EMAIL>',
        success: () => {
          uni.showToast({
            title: '邮箱地址已复制',
            icon: 'success'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 20rpx;
}

.app-info {
  background: linear-gradient(135deg, #2196F3, #21CBF3);
  border-radius: 20rpx;
  padding: 50rpx;
  text-align: center;
  margin-bottom: 30rpx;
  color: white;
  
  .app-logo {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 20rpx;
  }
  
  .app-name {
    display: block;
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
  }
  
  .app-version {
    display: block;
    font-size: 24rpx;
    opacity: 0.8;
    margin-bottom: 15rpx;
  }
  
  .app-slogan {
    display: block;
    font-size: 28rpx;
    opacity: 0.9;
  }
}

.feature-section, .usage-section, .tech-section, .contact-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
}

.feature-list {
  .feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .feature-icon {
      font-size: 40rpx;
      margin-right: 20rpx;
      margin-top: 5rpx;
    }
    
    .feature-content {
      flex: 1;
      
      .feature-title {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .feature-desc {
        font-size: 24rpx;
        color: #666;
        line-height: 1.5;
      }
    }
  }
}

.usage-steps {
  .step-item {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .step-number {
      width: 60rpx;
      height: 60rpx;
      background: #2196F3;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      font-weight: bold;
      margin-right: 20rpx;
    }
    
    .step-text {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.tech-list {
  .tech-item {
    display: block;
    font-size: 26rpx;
    color: #666;
    margin-bottom: 15rpx;
    padding-left: 20rpx;
    position: relative;
    
    &:before {
      content: '•';
      position: absolute;
      left: 0;
      color: #2196F3;
      font-weight: bold;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.contact-list {
  .contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .contact-icon {
      font-size: 32rpx;
      margin-right: 15rpx;
    }
    
    .contact-text {
      font-size: 26rpx;
      color: #333;
    }
  }
}

.copyright {
  text-align: center;
  padding: 30rpx;
  
  .copyright-text {
    display: block;
    font-size: 22rpx;
    color: #999;
    margin-bottom: 5rpx;
  }
}
</style>
