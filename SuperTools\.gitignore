# SuperTools .gitignore

# 敏感配置文件
scripts/production.env
.env
*.env
!*.env.example

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 日志文件
logs/
*.log

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 上传文件
uploads/
media/
static/uploads/

# 备份文件
*.bak
*.backup
backup/

# 缓存
.cache/
*.cache

# 测试覆盖率
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 文档构建
docs/_build/

# 密钥和证书
*.key
*.pem
*.crt
*.p12
*.pfx

# 第三方服务配置
config/local.py
config/production.py

# 数据文件
data/
*.csv
*.json
*.xml
!requirements.txt
!package.json

# 编译文件
*.pyc
*.pyo
*.pyd

# 安全扫描报告
security_report.*
vulnerability_report.*

# 性能分析
*.prof

# 本地开发配置
local_settings.py
settings_local.py

# 容器相关
.dockerignore
docker-compose.override.yml

# 前端构建文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 证件照处理临时文件
backend/utils/HivisionIDPhotos-master/output/
backend/utils/HivisionIDPhotos-master/temp/

# 短信SDK临时文件
backend/utils/python-sms-sdk-master/temp/
backend/utils/python-sms-sdk-master/logs/
