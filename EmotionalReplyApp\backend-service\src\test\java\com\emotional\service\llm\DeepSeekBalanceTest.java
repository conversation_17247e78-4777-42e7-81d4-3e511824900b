package com.emotional.service.llm;

import com.emotional.service.llm.impl.DeepSeekService;
import com.emotional.service.service.AdminNotificationService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DeepSeek余额解析测试
 */
@ExtendWith(MockitoExtension.class)
class DeepSeekBalanceTest {
    
    @Mock
    private RestTemplate restTemplate;
    
    @Mock
    private AdminNotificationService adminNotificationService;
    
    private DeepSeekService deepSeekService;
    private ObjectMapper objectMapper;
    
    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        deepSeekService = new DeepSeekService(restTemplate, objectMapper, adminNotificationService);
        
        // 设置测试配置
        ReflectionTestUtils.setField(deepSeekService, "enabled", true);
        ReflectionTestUtils.setField(deepSeekService, "apiKey", "test-api-key");
        ReflectionTestUtils.setField(deepSeekService, "baseUrl", "https://api.deepseek.com/v1");
        ReflectionTestUtils.setField(deepSeekService, "balanceCheckEnabled", true);
        ReflectionTestUtils.setField(deepSeekService, "balanceWarningThreshold", 50.0);
    }
    
    @Test
    void testParseBalanceResponse_NewFormat_CNY() {
        // 模拟DeepSeek API的新格式响应（人民币）
        String responseBody = "{\n" +
            "  \"is_available\": true,\n" +
            "  \"balance_infos\": [\n" +
            "    {\n" +
            "      \"currency\": \"CNY\",\n" +
            "      \"total_balance\": \"110.50\",\n" +
            "      \"granted_balance\": \"10.50\",\n" +
            "      \"topped_up_balance\": \"100.00\"\n" +
            "    }\n" +
            "  ]\n" +
            "}";
        
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class)))
                .thenReturn(new ResponseEntity<>(responseBody, HttpStatus.OK));
        
        DeepSeekService.BalanceInfo result = deepSeekService.checkBalance();
        
        assertTrue(result.getSuccess());
        assertEquals(110.50, result.getTotalBalance(), 0.01);
        assertEquals(110.50, result.getAvailableBalance(), 0.01);
        assertEquals("CNY", result.getCurrency());
        assertFalse(result.getNeedWarning()); // 110.50 > 50.0 阈值
    }
    
    @Test
    void testParseBalanceResponse_NewFormat_LowBalance() {
        // 模拟余额不足的情况
        String responseBody = "{\n" +
            "  \"is_available\": true,\n" +
            "  \"balance_infos\": [\n" +
            "    {\n" +
            "      \"currency\": \"CNY\",\n" +
            "      \"total_balance\": \"25.30\",\n" +
            "      \"granted_balance\": \"5.30\",\n" +
            "      \"topped_up_balance\": \"20.00\"\n" +
            "    }\n" +
            "  ]\n" +
            "}";
        
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class)))
                .thenReturn(new ResponseEntity<>(responseBody, HttpStatus.OK));
        
        DeepSeekService.BalanceInfo result = deepSeekService.checkBalance();
        
        assertTrue(result.getSuccess());
        assertEquals(25.30, result.getTotalBalance(), 0.01);
        assertEquals(25.30, result.getAvailableBalance(), 0.01);
        assertEquals("CNY", result.getCurrency());
        assertTrue(result.getNeedWarning()); // 25.30 < 50.0 阈值
        
        // 验证管理员通知被调用
        verify(adminNotificationService).notifyInsufficientBalance(
                contains("25.30 CNY"), 
                isNull()
        );
    }
    
    @Test
    void testParseBalanceResponse_ZeroBalance() {
        // 模拟余额为0的情况
        String responseBody = "{\n" +
            "  \"is_available\": false,\n" +
            "  \"balance_infos\": [\n" +
            "    {\n" +
            "      \"currency\": \"CNY\",\n" +
            "      \"total_balance\": \"0.00\",\n" +
            "      \"granted_balance\": \"0.00\",\n" +
            "      \"topped_up_balance\": \"0.00\"\n" +
            "    }\n" +
            "  ]\n" +
            "}";
        
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class)))
                .thenReturn(new ResponseEntity<>(responseBody, HttpStatus.OK));
        
        DeepSeekService.BalanceInfo result = deepSeekService.checkBalance();
        
        assertTrue(result.getSuccess());
        assertEquals(0.0, result.getTotalBalance(), 0.01);
        assertEquals(0.0, result.getAvailableBalance(), 0.01);
        assertEquals("CNY", result.getCurrency());
        assertTrue(result.getNeedWarning()); // 0.0 < 50.0 阈值
    }
    
    @Test
    void testParseBalanceResponse_ServiceUnavailable() {
        // 模拟服务不可用
        ReflectionTestUtils.setField(deepSeekService, "enabled", false);
        
        DeepSeekService.BalanceInfo result = deepSeekService.checkBalance();
        
        assertFalse(result.getSuccess());
        assertEquals("DeepSeek服务不可用", result.getErrorMessage());
    }
    
    @Test
    void testParseBalanceResponse_ApiError() {
        // 模拟API错误
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class)))
                .thenReturn(new ResponseEntity<>("Unauthorized", HttpStatus.UNAUTHORIZED));
        
        DeepSeekService.BalanceInfo result = deepSeekService.checkBalance();
        
        assertFalse(result.getSuccess());
        assertTrue(result.getErrorMessage().contains("获取余额失败"));
    }
    
    @Test
    void testCurrencyDisplay() {
        // 测试货币显示
        String responseBody = "{\n" +
            "  \"is_available\": true,\n" +
            "  \"balance_infos\": [\n" +
            "    {\n" +
            "      \"currency\": \"CNY\",\n" +
            "      \"total_balance\": \"88.88\",\n" +
            "      \"granted_balance\": \"8.88\",\n" +
            "      \"topped_up_balance\": \"80.00\"\n" +
            "    }\n" +
            "  ]\n" +
            "}";
        
        when(restTemplate.exchange(anyString(), any(), any(), eq(String.class)))
                .thenReturn(new ResponseEntity<>(responseBody, HttpStatus.OK));
        
        DeepSeekService.BalanceInfo result = deepSeekService.checkBalance();
        
        assertTrue(result.getSuccess());
        assertEquals("CNY", result.getCurrency());
        
        // 验证日志输出格式（这里只是验证数据正确性）
        String expectedLogFormat = String.format("可用余额=%.2f %s", 
                result.getAvailableBalance(), result.getCurrency());
        assertEquals("可用余额=88.88 CNY", expectedLogFormat);
    }
}
