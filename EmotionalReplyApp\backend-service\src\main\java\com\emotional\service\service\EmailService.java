package com.emotional.service.service;

/**
 * 邮件服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface EmailService {
    
    /**
     * 发送验证码邮件
     * 
     * @param to 收件人邮箱
     * @param code 验证码
     * @return 是否发送成功
     */
    boolean sendVerificationCode(String to, String code);
    
    /**
     * 发送密码重置验证码邮件
     * 
     * @param to 收件人邮箱
     * @param code 验证码
     * @return 是否发送成功
     */
    boolean sendPasswordResetCode(String to, String code);
    
    /**
     * 发送简单文本邮件
     * 
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 是否发送成功
     */
    boolean sendSimpleEmail(String to, String subject, String content);
    
    /**
     * 发送HTML邮件
     * 
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param htmlContent HTML邮件内容
     * @return 是否发送成功
     */
    boolean sendHtmlEmail(String to, String subject, String htmlContent);
}
