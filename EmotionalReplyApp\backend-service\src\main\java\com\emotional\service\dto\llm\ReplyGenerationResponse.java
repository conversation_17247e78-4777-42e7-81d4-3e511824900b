package com.emotional.service.dto.llm;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 回复生成响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReplyGenerationResponse {
    
    /**
     * 生成的回复列表
     */
    private List<GeneratedReply> replies;
    
    /**
     * 处理时间（毫秒）
     */
    private Long processingTime;
    
    /**
     * 使用的模型名称
     */
    private String modelName;
    
    /**
     * 是否成功
     */
    private Boolean success = true;
    
    /**
     * 错误信息（如果有）
     */
    private String errorMessage;
    
    /**
     * 生成的回复项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GeneratedReply {
        
        /**
         * 回复内容
         */
        private String content;
        
        /**
         * 回复风格
         */
        private String style;
        
        /**
         * 风格名称
         */
        private String styleName;
        
        /**
         * 质量评分 (0.0-1.0)
         */
        private Double qualityScore;
        
        /**
         * 情感匹配度 (0.0-1.0)
         */
        private Double emotionMatch;
        
        /**
         * 推荐指数 (1-5)
         */
        private Integer recommendationScore;
        
        /**
         * 生成原因/解释
         */
        private String explanation;
    }
}
