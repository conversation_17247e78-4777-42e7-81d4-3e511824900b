package com.emotional.service.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 通知频率控制服务
 * 防止管理员邮箱被大量重复通知轰炸
 */
@Slf4j
@Service
public class NotificationThrottleService {
    
    private final RedisTemplate<String, String> redisTemplate;
    
    @Value("${app.admin.notification.throttle.api-key-error:86400}")
    private long apiKeyErrorThrottleSeconds; // API密钥错误通知间隔（默认24小时）
    
    @Value("${app.admin.notification.throttle.balance-error:3600}")
    private long balanceErrorThrottleSeconds; // 余额不足通知间隔（默认1小时）
    
    @Value("${app.admin.notification.throttle.service-error:1800}")
    private long serviceErrorThrottleSeconds; // 服务异常通知间隔（默认30分钟）
    
    @Value("${app.admin.notification.throttle.enabled:true}")
    private boolean throttleEnabled;
    
    public NotificationThrottleService(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    
    /**
     * 检查是否可以发送API密钥错误通知
     * @param errorType 错误类型
     * @return 是否可以发送
     */
    public boolean canSendApiKeyErrorNotification(String errorType) {
        if (!throttleEnabled) {
            return true;
        }
        
        String key = "notification:api_key_error:" + errorType;
        return checkAndSetThrottle(key, apiKeyErrorThrottleSeconds, "API密钥错误");
    }
    
    /**
     * 检查是否可以发送余额不足通知
     * @return 是否可以发送
     */
    public boolean canSendBalanceErrorNotification() {
        if (!throttleEnabled) {
            return true;
        }
        
        String key = "notification:balance_error";
        return checkAndSetThrottle(key, balanceErrorThrottleSeconds, "余额不足");
    }
    
    /**
     * 检查是否可以发送服务异常通知
     * @param errorType 错误类型
     * @return 是否可以发送
     */
    public boolean canSendServiceErrorNotification(String errorType) {
        if (!throttleEnabled) {
            return true;
        }
        
        String key = "notification:service_error:" + errorType;
        return checkAndSetThrottle(key, serviceErrorThrottleSeconds, "服务异常");
    }
    
    /**
     * 检查并设置节流
     * @param key Redis键
     * @param throttleSeconds 节流时间（秒）
     * @param notificationType 通知类型（用于日志）
     * @return 是否可以发送
     */
    private boolean checkAndSetThrottle(String key, long throttleSeconds, String notificationType) {
        try {
            // 检查是否存在节流记录
            String lastSent = redisTemplate.opsForValue().get(key);
            
            if (lastSent != null) {
                log.debug("{}通知被节流限制，上次发送时间: {}", notificationType, lastSent);
                return false;
            }
            
            // 设置节流记录
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            redisTemplate.opsForValue().set(key, currentTime, throttleSeconds, TimeUnit.SECONDS);
            
            log.info("{}通知通过节流检查，设置节流时间: {}秒", notificationType, throttleSeconds);
            return true;
            
        } catch (Exception e) {
            log.error("节流检查失败，允许发送通知", e);
            // 如果Redis出错，为了确保重要通知能发送，返回true
            return true;
        }
    }
    
    /**
     * 获取通知发送统计信息
     * @return 统计信息
     */
    public NotificationStats getNotificationStats() {
        try {
            NotificationStats stats = new NotificationStats();
            
            // 检查各种通知的节流状态
            stats.setApiKeyErrorThrottled(isThrottled("notification:api_key_error:*"));
            stats.setBalanceErrorThrottled(isThrottled("notification:balance_error"));
            stats.setServiceErrorThrottled(isThrottled("notification:service_error:*"));
            
            // 获取下次可发送时间
            stats.setApiKeyErrorNextSendTime(getNextSendTime("notification:api_key_error:*"));
            stats.setBalanceErrorNextSendTime(getNextSendTime("notification:balance_error"));
            stats.setServiceErrorNextSendTime(getNextSendTime("notification:service_error:*"));
            
            return stats;
            
        } catch (Exception e) {
            log.error("获取通知统计信息失败", e);
            return new NotificationStats();
        }
    }
    
    /**
     * 检查是否被节流
     */
    private boolean isThrottled(String keyPattern) {
        try {
            if (keyPattern.contains("*")) {
                // 模糊匹配
                Set<String> keys = redisTemplate.keys(keyPattern);
                return keys != null && !keys.isEmpty();
            } else {
                // 精确匹配
                return Boolean.TRUE.equals(redisTemplate.hasKey(keyPattern));
            }
        } catch (Exception e) {
            log.error("检查节流状态失败", e);
            return false;
        }
    }
    
    /**
     * 获取下次可发送时间
     */
    private String getNextSendTime(String keyPattern) {
        try {
            if (keyPattern.contains("*")) {
                // 模糊匹配，获取第一个匹配的key
                Set<String> keys = redisTemplate.keys(keyPattern);
                if (keys != null && !keys.isEmpty()) {
                    String key = keys.iterator().next();
                    Long ttl = redisTemplate.getExpire(key, TimeUnit.SECONDS);
                    if (ttl > 0) {
                        return LocalDateTime.now().plusSeconds(ttl)
                                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    }
                }
            } else {
                // 精确匹配
                Long ttl = redisTemplate.getExpire(keyPattern, TimeUnit.SECONDS);
                if (ttl > 0) {
                    return LocalDateTime.now().plusSeconds(ttl)
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                }
            }
            return "可立即发送";
        } catch (Exception e) {
            log.error("获取下次发送时间失败", e);
            return "未知";
        }
    }
    
    /**
     * 清除所有节流记录（管理员手动重置用）
     */
    public void clearAllThrottles() {
        try {
            Set<String> keys = redisTemplate.keys("notification:*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("已清除所有通知节流记录，共{}条", keys.size());
            }
        } catch (Exception e) {
            log.error("清除节流记录失败", e);
        }
    }
    
    /**
     * 通知统计信息DTO
     */
    public static class NotificationStats {
        private boolean apiKeyErrorThrottled;
        private boolean balanceErrorThrottled;
        private boolean serviceErrorThrottled;
        private String apiKeyErrorNextSendTime;
        private String balanceErrorNextSendTime;
        private String serviceErrorNextSendTime;
        
        // Getters and Setters
        public boolean isApiKeyErrorThrottled() { return apiKeyErrorThrottled; }
        public void setApiKeyErrorThrottled(boolean apiKeyErrorThrottled) { this.apiKeyErrorThrottled = apiKeyErrorThrottled; }
        
        public boolean isBalanceErrorThrottled() { return balanceErrorThrottled; }
        public void setBalanceErrorThrottled(boolean balanceErrorThrottled) { this.balanceErrorThrottled = balanceErrorThrottled; }
        
        public boolean isServiceErrorThrottled() { return serviceErrorThrottled; }
        public void setServiceErrorThrottled(boolean serviceErrorThrottled) { this.serviceErrorThrottled = serviceErrorThrottled; }
        
        public String getApiKeyErrorNextSendTime() { return apiKeyErrorNextSendTime; }
        public void setApiKeyErrorNextSendTime(String apiKeyErrorNextSendTime) { this.apiKeyErrorNextSendTime = apiKeyErrorNextSendTime; }
        
        public String getBalanceErrorNextSendTime() { return balanceErrorNextSendTime; }
        public void setBalanceErrorNextSendTime(String balanceErrorNextSendTime) { this.balanceErrorNextSendTime = balanceErrorNextSendTime; }
        
        public String getServiceErrorNextSendTime() { return serviceErrorNextSendTime; }
        public void setServiceErrorNextSendTime(String serviceErrorNextSendTime) { this.serviceErrorNextSendTime = serviceErrorNextSendTime; }
    }
}
