package com.emotional.service.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 更新用户资料请求DTO
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@Schema(description = "更新用户资料请求")
public class UpdateUserProfileRequest {
    
    /**
     * 昵称
     */
    @Schema(description = "昵称", example = "小明")
    @Size(max = 100, message = "昵称长度不能超过100个字符")
    private String nickname;
    
    /**
     * 邮箱
     */
    @Schema(description = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;
    
    /**
     * 手机号
     */
    @Schema(description = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    /**
     * 头像URL
     */
    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    @Size(max = 500, message = "头像URL长度不能超过500个字符")
    private String avatar;
}
