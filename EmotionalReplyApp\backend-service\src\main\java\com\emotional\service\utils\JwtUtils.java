package com.emotional.service.utils;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.SecretKey;
import java.util.Date;

/**
 * JWT工具类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Component
@Slf4j
public class JwtUtils {
    
    @Value("${app.security.jwt-secret:emotional-reply-service-super-secure-jwt-secret-key-for-hs512-algorithm-2024-very-long}")
    private String jwtSecret;

    @Value("${app.security.jwt-expire:86400}")
    private int jwtExpiration;

    /**
     * 初始化时验证JWT配置
     */
    @PostConstruct
    public void init() {
        byte[] keyBytes = jwtSecret.getBytes();
        log.info("JWT配置初始化:");
        log.info("- 密钥长度: {} 字符, {} 位", jwtSecret.length(), keyBytes.length * 8);
        log.info("- 过期时间: {} 秒 ({} 小时)", jwtExpiration, jwtExpiration / 3600);
        log.info("- 签名算法: HS256");

        if (keyBytes.length >= 32) {
            log.info("✅ JWT密钥长度符合HS256要求 (>= 256位)");
        } else {
            log.warn("⚠️ JWT密钥长度不足，将使用自动生成的安全密钥");
        }
    }

    /**
     * 获取签名密钥
     *
     * @return 签名密钥
     */
    private SecretKey getSigningKey() {
        byte[] keyBytes = jwtSecret.getBytes();
        log.debug("JWT密钥长度: {} 字符, {} 位", jwtSecret.length(), keyBytes.length * 8);

        // 检查密钥长度是否足够
        if (keyBytes.length < 32) {
            log.warn("JWT密钥长度不足，当前: {} 位，HS256要求: >= 256 位", keyBytes.length * 8);
            // 如果密钥太短，使用自动生成的安全密钥
            log.info("使用自动生成的安全密钥");
            return Keys.secretKeyFor(SignatureAlgorithm.HS256);
        }

        return Keys.hmacShaKeyFor(keyBytes);
    }
    
    /**
     * 生成JWT令牌
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @return JWT令牌
     */
    public String generateToken(Long userId, String username) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + jwtExpiration * 1000L);
        
        return Jwts.builder()
                .setSubject(String.valueOf(userId))
                .claim("username", username)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();
    }
    
    /**
     * 从令牌中获取用户ID
     * 
     * @param token JWT令牌
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            
            return Long.valueOf(claims.getSubject());
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("无法从token中获取用户ID: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 从令牌中获取用户名
     * 
     * @param token JWT令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            
            return claims.get("username", String.class);
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("无法从token中获取用户名: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 验证JWT令牌
     * 
     * @param token JWT令牌
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token);
            return true;
        } catch (SecurityException e) {
            log.warn("JWT签名无效: {}", e.getMessage());
        } catch (MalformedJwtException e) {
            log.warn("JWT格式错误: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            log.warn("JWT已过期: {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
            log.warn("不支持的JWT: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.warn("JWT参数为空: {}", e.getMessage());
        }
        return false;
    }
    
    /**
     * 获取令牌过期时间
     * 
     * @param token JWT令牌
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            
            return claims.getExpiration();
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("无法从token中获取过期时间: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 检查令牌是否过期
     * 
     * @param token JWT令牌
     * @return 是否过期
     */
    public boolean isTokenExpired(String token) {
        Date expiration = getExpirationDateFromToken(token);
        return expiration != null && expiration.before(new Date());
    }
    
    /**
     * 刷新令牌
     * 
     * @param token 原令牌
     * @return 新令牌
     */
    public String refreshToken(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            
            Long userId = Long.valueOf(claims.getSubject());
            String username = claims.get("username", String.class);
            
            return generateToken(userId, username);
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("无法刷新token: {}", e.getMessage());
            return null;
        }
    }
}
