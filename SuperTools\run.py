#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SuperTools 运行脚本
使用此脚本启动应用，自动处理包导入问题
支持一键启动主应用和证件照服务
"""

import os
import sys
import time
import threading
import subprocess
import requests
import argparse
from pathlib import Path

# 确保当前目录在pythonpath中
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_hivision_service():
    """检查HivisionIDPhotos服务是否运行"""
    try:
        response = requests.get('http://127.0.0.1:8080/docs', timeout=3)
        return response.status_code == 200
    except:
        return False

def start_hivision_service():
    """在后台启动HivisionIDPhotos服务"""
    project_root = Path(current_dir)
    hivision_path = project_root / 'backend' / 'utils' / 'HivisionIDPhotos-master'

    if not hivision_path.exists():
        print(f"⚠️  HivisionIDPhotos项目路径不存在: {hivision_path}")
        print("   证件照功能将不可用")
        return None

    print(f"📁 HivisionIDPhotos路径: {hivision_path}")

    # 检查主要依赖
    try:
        import cv2
        import onnxruntime
        import numpy
        import fastapi
        import uvicorn
        print("✅ 证件照服务依赖已安装")
    except ImportError as e:
        print(f"⚠️  证件照服务依赖缺失: {e}")
        print("   证件照功能将不可用")
        return None

    # 检查模型文件
    weights_dir = hivision_path / 'hivision' / 'creator' / 'weights'
    if weights_dir.exists():
        model_files = list(weights_dir.glob('*.onnx'))
        if model_files:
            print(f"✅ 找到 {len(model_files)} 个模型文件")
        else:
            print("⚠️  未找到模型文件，将使用默认模型")

    def run_hivision():
        """在子线程中运行证件照服务"""
        try:
            # 添加HivisionIDPhotos路径到Python路径
            original_cwd = os.getcwd()
            sys.path.insert(0, str(hivision_path))
            os.chdir(hivision_path)

            # 导入并启动FastAPI应用
            from deploy_api import app
            import uvicorn

            # 在8080端口运行证件照API服务
            uvicorn.run(app, host="0.0.0.0", port=8080, log_level="warning")

        except Exception as e:
            print(f"⚠️  证件照服务启动失败: {e}")
        finally:
            # 恢复原始工作目录
            os.chdir(original_cwd)

    # 在后台线程中启动服务
    print("🚀 正在启动证件照服务...")
    thread = threading.Thread(target=run_hivision, daemon=True)
    thread.start()

    # 等待服务启动
    for i in range(15):  # 最多等待15秒
        time.sleep(1)
        if check_hivision_service():
            print("✅ 证件照服务启动成功! (http://127.0.0.1:8080)")
            return thread

    print("⚠️  证件照服务启动可能需要更多时间，请稍后检查")
    return thread

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='SuperTools 启动器')
    parser.add_argument('--no-idphoto', action='store_true', help='不启动证件照服务')
    parser.add_argument('--port', type=int, default=5000, help='Web服务端口 (默认: 5000)')
    parser.add_argument('--host', default='0.0.0.0', help='Web服务主机 (默认: 0.0.0.0)')
    parser.add_argument('--debug', action='store_true', default=True, help='启用调试模式')
    args = parser.parse_args()

    # 检测是否为重载进程
    is_reloader = os.environ.get('WERKZEUG_RUN_MAIN') == 'true'

    if not is_reloader:
        print("=" * 60)
        print("🚀 SuperTools 启动中...")
        print(f"🌐 Web界面: http://127.0.0.1:{args.port}")
        if not args.no_idphoto:
            print("📸 证件照服务: http://127.0.0.1:8080 (如果可用)")
        print("=" * 60)

        # 检查证件照服务状态
        if not args.no_idphoto:
            if check_hivision_service():
                print("✅ 证件照服务已在运行")
            else:
                print("🔄 正在启动证件照服务...")
                start_hivision_service()
        else:
            print("⏭️  跳过证件照服务启动")

    # 创建必要的目录
    os.makedirs(os.path.join(current_dir, "logs"), exist_ok=True)
    os.makedirs(os.path.join(current_dir, "data"), exist_ok=True)

    # 导入应用实例
    from backend.main import app

    # 只在主进程中打印路由映射
    if not is_reloader:
        print("\n📋 已注册的API路由:")
        for rule in app.url_map.iter_rules():
            if rule.endpoint != 'static':
                methods = ','.join(rule.methods - {'HEAD', 'OPTIONS'})
                print(f"   {methods:10} {rule.rule}")
        print("-" * 60)
        print("✅ SuperTools 启动完成!")
        print(f"💡 访问 http://127.0.0.1:{args.port} 开始使用")
        print("-" * 60)

    # 启动应用
    app.run(host=args.host, port=args.port, debug=args.debug)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
        print("👋 再见!")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)