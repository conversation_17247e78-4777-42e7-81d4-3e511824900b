#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SuperTools 安全检查脚本
检查应用中可能存在的安全问题
"""

import os
import re
import sys
from pathlib import Path

def check_hardcoded_secrets():
    """检查硬编码的敏感信息"""
    print("🔍 检查硬编码的敏感信息...")
    
    # 敏感信息模式
    patterns = [
        (r'localhost:\d+', '硬编码的localhost地址'),
        (r'127\.0\.0\.1:\d+', '硬编码的本地IP地址'),
        (r'password\s*=\s*["\'][^"\']+["\']', '硬编码的密码'),
        (r'secret_key\s*=\s*["\'][^"\']+["\']', '硬编码的密钥'),
        (r'api_key\s*=\s*["\'][^"\']+["\']', '硬编码的API密钥'),
        (r'mysql://[^"\']+', '硬编码的数据库连接字符串'),
    ]
    
    issues = []
    
    # 检查Python文件
    for py_file in Path('.').rglob('*.py'):
        if 'venv' in str(py_file) or '__pycache__' in str(py_file):
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            for pattern, description in patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    line_num = content[:match.start()].count('\n') + 1
                    issues.append({
                        'file': str(py_file),
                        'line': line_num,
                        'issue': description,
                        'content': match.group()
                    })
        except Exception as e:
            print(f"⚠️  无法读取文件 {py_file}: {e}")
    
    # 检查JavaScript文件
    for js_file in Path('.').rglob('*.js'):
        try:
            with open(js_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            for pattern, description in patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    line_num = content[:match.start()].count('\n') + 1
                    issues.append({
                        'file': str(js_file),
                        'line': line_num,
                        'issue': description,
                        'content': match.group()
                    })
        except Exception as e:
            print(f"⚠️  无法读取文件 {js_file}: {e}")
    
    return issues

def check_debug_mode():
    """检查调试模式配置"""
    print("🔍 检查调试模式配置...")
    
    issues = []
    
    # 检查主配置文件
    main_py = Path('backend/main.py')
    if main_py.exists():
        with open(main_py, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if "app.config['DEBUG'] = True" in content and 'production' not in content.lower():
            issues.append({
                'file': str(main_py),
                'issue': '调试模式可能在生产环境中启用',
                'suggestion': '确保生产环境中禁用调试模式'
            })
    
    return issues

def check_error_handling():
    """检查错误处理"""
    print("🔍 检查错误处理...")
    
    issues = []
    
    # 检查是否有适当的错误处理
    for py_file in Path('.').rglob('*.py'):
        if 'venv' in str(py_file) or '__pycache__' in str(py_file):
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查是否有裸露的异常处理
            if re.search(r'except\s*:', content):
                issues.append({
                    'file': str(py_file),
                    'issue': '发现裸露的except语句',
                    'suggestion': '使用具体的异常类型'
                })
                
        except Exception as e:
            continue
    
    return issues

def check_environment_config():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    issues = []
    
    # 检查是否存在生产环境配置
    prod_env = Path('scripts/production.env')
    prod_example = Path('scripts/production.env.example')
    
    if not prod_example.exists():
        issues.append({
            'file': 'scripts/production.env.example',
            'issue': '缺少生产环境配置示例文件',
            'suggestion': '创建生产环境配置示例文件'
        })
    
    if prod_env.exists():
        issues.append({
            'file': 'scripts/production.env',
            'issue': '生产环境配置文件存在于代码库中',
            'suggestion': '将此文件添加到.gitignore中'
        })
    
    return issues

def main():
    """主函数"""
    print("🛡️  SuperTools 安全检查")
    print("=" * 50)
    
    all_issues = []
    
    # 执行各项检查
    all_issues.extend(check_hardcoded_secrets())
    all_issues.extend(check_debug_mode())
    all_issues.extend(check_error_handling())
    all_issues.extend(check_environment_config())
    
    # 输出结果
    if all_issues:
        print(f"\n❌ 发现 {len(all_issues)} 个安全问题:")
        print("-" * 50)
        
        for i, issue in enumerate(all_issues, 1):
            print(f"{i}. {issue['issue']}")
            print(f"   文件: {issue['file']}")
            if 'line' in issue:
                print(f"   行号: {issue['line']}")
            if 'content' in issue:
                print(f"   内容: {issue['content']}")
            if 'suggestion' in issue:
                print(f"   建议: {issue['suggestion']}")
            print()
        
        print("🔧 修复建议:")
        print("1. 移除所有硬编码的敏感信息")
        print("2. 使用环境变量管理配置")
        print("3. 确保生产环境禁用调试模式")
        print("4. 添加适当的错误处理")
        print("5. 不要将敏感配置文件提交到版本控制")
        
        return 1
    else:
        print("✅ 未发现明显的安全问题")
        print("💡 建议定期运行此检查脚本")
        return 0

if __name__ == '__main__':
    sys.exit(main())
