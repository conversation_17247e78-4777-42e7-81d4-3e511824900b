/**
 * 用户相关 API
 */
import { post, get, put, upload } from '../utils/request.js'

/**
 * 用户注册
 * @param {object} registerData - 注册数据
 * @returns {Promise} 注册结果
 */
export const register = (registerData) => {
  return post('/user/register', registerData)
}

/**
 * 用户登录
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @returns {Promise} 登录结果
 */
export const login = (username, password) => {
  return post('/user/login', {
    username,
    password,
    rememberMe: false
  })
}

/**
 * 获取用户信息
 * @param {number} userId - 用户ID
 * @returns {Promise} 用户信息
 */
export const getUserInfo = (userId) => {
  return get(`/user/info/${userId}`)
}

/**
 * 更新用户信息
 * @param {number} userId - 用户ID
 * @param {object} userInfo - 用户信息
 * @returns {Promise} 更新结果
 */
export const updateUserInfo = (userId, userInfo) => {
  return put(`/user/info/${userId}`, userInfo)
}

/**
 * 更新用户资料
 * @param {number} userId - 用户ID
 * @param {object} profileData - 用户资料数据
 * @returns {Promise} 更新结果
 */
export const updateUserProfile = (userId, profileData) => {
  return put(`/user/profile/${userId}`, profileData)
}

/**
 * 修改密码
 * @param {number} userId - 用户ID
 * @param {string} oldPassword - 旧密码
 * @param {string} newPassword - 新密码
 * @returns {Promise} 修改结果
 */
export const changePassword = (userId, oldPassword, newPassword) => {
  return put(`/user/password/${userId}`, {
    oldPassword,
    newPassword
  })
}

/**
 * 修改用户密码
 * @param {number} userId - 用户ID
 * @param {object} passwordData - 密码数据
 * @returns {Promise} 修改结果
 */
export const changeUserPassword = (userId, passwordData) => {
  return put(`/user/change-password/${userId}`, passwordData)
}



/**
 * 验证用户是否存在
 * @param {string} identifier - 用户标识（手机号或邮箱）
 * @returns {Promise} 验证结果
 */
export const checkUserExists = (identifier) => {
  return post('/user/check-exists', {
    identifier
  })
}

/**
 * 发送注册验证码
 * @param {string} email - 邮箱地址
 * @returns {Promise} 发送结果
 */
export const sendRegisterCode = (email) => {
  return post('/user/send-register-code', {
    email
  })
}

/**
 * 发送忘记密码验证码
 * @param {string} identifier - 用户标识（手机号或邮箱）
 * @returns {Promise} 发送结果
 */
export const sendResetPasswordCode = (identifier) => {
  return post('/user/send-reset-code', {
    identifier
  })
}

/**
 * 验证注册验证码
 * @param {string} email - 邮箱地址
 * @param {string} code - 验证码
 * @returns {Promise} 验证结果
 */
export const verifyRegisterCode = (email, code) => {
  return post('/user/verify-register-code', {
    email,
    code
  })
}

/**
 * 验证重置密码验证码
 * @param {string} identifier - 用户标识
 * @param {string} code - 验证码
 * @returns {Promise} 验证结果
 */
export const verifyResetCode = (identifier, code) => {
  return post('/user/verify-reset-code', {
    identifier,
    code
  })
}

/**
 * 重置密码
 * @param {string} identifier - 用户标识
 * @param {string} code - 验证码
 * @param {string} newPassword - 新密码
 * @returns {Promise} 重置结果
 */
export const resetPassword = (identifier, code, newPassword) => {
  return post('/user/reset-password', {
    identifier,
    code,
    newPassword
  })
}

/**
 * 获取用户统计信息
 * @param {number} userId - 用户ID
 * @returns {Promise} 统计信息
 */
export const getUserStats = (userId) => {
  return get(`/user/stats/${userId}`)
}

/**
 * 增加用户使用次数
 * @param {number} userId - 用户ID
 * @returns {Promise} 操作结果
 */
export const incrementUsage = (userId) => {
  return post(`/user/usage/${userId}`)
}

/**
 * 检查用户配额
 * @param {number} userId - 用户ID
 * @returns {Promise} 配额信息
 */
export const checkQuota = (userId) => {
  return get(`/user/quota/${userId}`)
}

/**
 * 激活VIP
 * @param {number} userId - 用户ID
 * @param {string} activationCode - 激活码
 * @returns {Promise} 激活结果
 */
export const activateVip = (userId, activationCode) => {
  return post(`/user/activate-vip`, {
    userId,
    activationCode
  })
}

/**
 * 获取用户回复偏好设置
 * @param {number} userId - 用户ID
 * @returns {Promise} 偏好设置
 */
export const getUserReplyPreferences = (userId) => {
  return get(`/user/settings/${userId}/reply-styles`)
}

/**
 * 更新用户回复偏好设置
 * @param {number} userId - 用户ID
 * @param {object} preferences - 偏好设置
 * @returns {Promise} 更新结果
 */
export const updateReplyPreferences = (userId, preferences) => {
  return put(`/user/settings/${userId}/reply-styles`, preferences)
}

/**
 * 获取用户完整设置
 * @param {number} userId - 用户ID
 * @returns {Promise} 用户设置
 */
export const getUserSettings = (userId) => {
  return get(`/user/settings/${userId}`)
}

/**
 * 更新用户设置
 * @param {number} userId - 用户ID
 * @param {object} settings - 设置信息
 * @returns {Promise} 更新结果
 */
export const updateUserSettings = (userId, settings) => {
  return put(`/user/settings/${userId}`, settings)
}

export default {
  register,
  login,
  getUserInfo,
  updateUserInfo,
  updateUserProfile,
  changePassword,
  changeUserPassword,
  checkUserExists,
  sendRegisterCode,
  verifyRegisterCode,
  sendResetPasswordCode,
  verifyResetCode,
  resetPassword,
  getUserStats,
  incrementUsage,
  checkQuota,
  activateVip,
  getUserReplyPreferences,
  updateReplyPreferences,
  getUserSettings,
  updateUserSettings
}
