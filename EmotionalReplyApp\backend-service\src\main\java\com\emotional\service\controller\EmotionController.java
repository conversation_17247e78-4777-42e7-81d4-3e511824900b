package com.emotional.service.controller;

import com.emotional.service.common.Result;
import com.emotional.service.dto.EmotionAnalyzeRequest;
import com.emotional.service.dto.EmotionAnalyzeResponse;
import com.emotional.service.service.EmotionService;
import com.emotional.service.service.UserStatsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 情感分析控制器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/emotion")
@RequiredArgsConstructor
@Validated
@CrossOrigin(originPatterns = "*", maxAge = 3600)
@Tag(name = "情感分析", description = "情感分析和回复生成相关接口")
public class EmotionController {

    private final EmotionService emotionService;
    private final UserStatsService userStatsService;
    
    /**
     * 分析情感并生成回复
     */
    @Operation(summary = "情感分析", description = "分析消息的情感并生成相应的回复建议")
    @PostMapping("/analyze")
    public Result<EmotionAnalyzeResponse> analyzeEmotion(
            @Valid @RequestBody EmotionAnalyzeRequest request,
            HttpServletRequest httpRequest) {

        log.info("收到情感分析请求: message={}, userId={}, saveHistory={}",
                 request.getMessage(), request.getUserId(), request.getSaveHistory());

        try {
            // 1. 配额检查 - 只有在需要保存历史记录时才检查配额
            if (request.getSaveHistory() && request.getUserId() != null && request.getUserId() > 0) {
                boolean hasQuota = userStatsService.hasQuota(request.getUserId());
                if (!hasQuota) {
                    log.warn("用户配额不足: userId={}", request.getUserId());
                    return Result.error("今日使用次数已达上限，请明天再试或升级VIP");
                }

                // 2. 扣减配额 - 在处理前先扣减，确保并发安全
                boolean quotaConsumed = userStatsService.incrementUsage(request.getUserId());
                if (!quotaConsumed) {
                    log.warn("配额扣减失败: userId={}", request.getUserId());
                    return Result.error("配额扣减失败，请稍后重试");
                }

                log.info("配额检查通过，已扣减配额: userId={}", request.getUserId());
            }

            // 3. 获取客户端信息
            String clientIp = getClientIp(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");

            // 4. 执行情感分析和回复生成
            EmotionAnalyzeResponse response = emotionService.analyzeAndGenerateReply(
                request, clientIp, userAgent);

            log.info("情感分析完成，历史记录ID: {}", response.getHistoryId());

            return Result.success("分析成功", response);

        } catch (Exception e) {
            log.error("情感分析失败", e);
            return Result.error("分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取支持的回复风格列表
     */
    @GetMapping("/styles")
    public Result<Object> getReplyStyles() {
        try {
            Object styles = emotionService.getReplyStyles();
            return Result.success("获取成功", styles);
        } catch (Exception e) {
            log.error("获取回复风格失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 重新生成回复
     */
    @PostMapping("/regenerate/{historyId}")
    public Result<EmotionAnalyzeResponse> regenerateReply(
            @PathVariable Long historyId,
            @RequestBody(required = false) EmotionAnalyzeRequest request) {
        
        log.info("重新生成回复，历史记录ID: {}", historyId);
        
        try {
            EmotionAnalyzeResponse response = emotionService.regenerateReply(historyId, request);
            return Result.success("重新生成成功", response);
        } catch (Exception e) {
            log.error("重新生成回复失败", e);
            return Result.error("重新生成失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取客户端真实IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况，取第一个
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }
}
