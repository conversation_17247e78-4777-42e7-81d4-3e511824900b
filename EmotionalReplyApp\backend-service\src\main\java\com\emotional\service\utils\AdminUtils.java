package com.emotional.service.utils;

import com.emotional.service.entity.User;
import lombok.extern.slf4j.Slf4j;

/**
 * 管理员权限工具类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
public class AdminUtils {
    
    /**
     * 判断用户是否为管理员
     * 
     * @param user 用户对象
     * @return true-管理员，false-普通用户
     */
    public static boolean isAdmin(User user) {
        if (user == null) {
            log.warn("用户对象为空，判断为非管理员");
            return false;
        }
        
        // 检查用户状态是否正常
        if (user.getStatus() != null && user.getStatus() != 0) {
            log.warn("用户 {} 状态异常：{}，判断为非管理员", user.getUsername(), user.getStatus());
            return false;
        }
        
        // 检查是否被逻辑删除
        if (user.getDeleted() != null && user.getDeleted() != 0) {
            log.warn("用户 {} 已被删除，判断为非管理员", user.getUsername());
            return false;
        }
        
        // 检查管理员标志
        boolean isAdmin = user.getIsAdmin() != null && user.getIsAdmin() == 1;
        
        if (isAdmin) {
            log.info("用户 {} 具有管理员权限", user.getUsername());
        } else {
            log.debug("用户 {} 为普通用户", user.getUsername());
        }
        
        return isAdmin;
    }
    
    /**
     * 判断用户ID是否为管理员
     * 
     * @param userId 用户ID
     * @param isAdmin 管理员标志
     * @return true-管理员，false-普通用户
     */
    public static boolean isAdmin(Long userId, Integer isAdmin) {
        if (userId == null) {
            log.warn("用户ID为空，判断为非管理员");
            return false;
        }
        
        boolean result = isAdmin != null && isAdmin == 1;
        
        if (result) {
            log.info("用户ID {} 具有管理员权限", userId);
        } else {
            log.debug("用户ID {} 为普通用户", userId);
        }
        
        return result;
    }
    
    /**
     * 判断用户是否为超级管理员（简化版本：管理员就是管理员）
     *
     * @param user 用户对象
     * @return true-管理员，false-普通用户
     */
    public static boolean isSuperAdmin(User user) {
        // 简化逻辑：管理员就是管理员，不区分超级管理员
        return isAdmin(user);
    }
    
    /**
     * 检查管理员权限并抛出异常
     * 
     * @param user 用户对象
     * @throws RuntimeException 如果不是管理员则抛出异常
     */
    public static void requireAdmin(User user) {
        if (!isAdmin(user)) {
            String username = user != null ? user.getUsername() : "unknown";
            log.warn("用户 {} 尝试访问管理员功能但权限不足", username);
            throw new RuntimeException("权限不足，需要管理员权限");
        }
    }
    
    /**
     * 检查超级管理员权限并抛出异常（简化版本：等同于管理员检查）
     *
     * @param user 用户对象
     * @throws RuntimeException 如果不是管理员则抛出异常
     */
    public static void requireSuperAdmin(User user) {
        // 简化逻辑：超级管理员检查等同于管理员检查
        requireAdmin(user);
    }
    
    /**
     * 获取用户权限级别描述
     *
     * @param user 用户对象
     * @return 权限级别描述
     */
    public static String getUserRoleDescription(User user) {
        if (user == null) {
            return "未知用户";
        }

        // 简化权限检查：管理员优先级最高
        if (isAdmin(user)) {
            return "管理员";
        } else if (user.getIsVip() != null && user.getIsVip() == 1) {
            return "VIP用户";
        } else {
            return "普通用户";
        }
    }
    
    /**
     * 检查用户是否有访问管理功能的权限
     * 
     * @param user 用户对象
     * @param functionName 功能名称（用于日志记录）
     * @return true-有权限，false-无权限
     */
    public static boolean hasAdminAccess(User user, String functionName) {
        boolean hasAccess = isAdmin(user);
        
        if (hasAccess) {
            log.info("用户 {} 访问管理功能：{}", user.getUsername(), functionName);
        } else {
            log.warn("用户 {} 尝试访问管理功能 {} 但权限不足", 
                    user != null ? user.getUsername() : "unknown", functionName);
        }
        
        return hasAccess;
    }
}
