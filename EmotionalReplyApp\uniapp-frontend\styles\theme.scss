/**
 * 主题样式定义
 */

/* 浅色主题 */
.theme-light {
  --bg-color: #f8f9fa;
  --bg-color-secondary: #ffffff;
  --text-color: #333333;
  --text-color-secondary: #666666;
  --text-color-muted: #999999;
  --border-color: #e0e0e0;
  --border-color-light: #f0f0f0;
  --primary-color: #2196F3;
  --primary-gradient: linear-gradient(135deg, #2196F3, #21CBF3);
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --error-color: #F44336;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --card-bg: #ffffff;
  --input-bg: #ffffff;
  --input-border: #e0e0e0;
  --switch-bg: #e0e0e0;
  --switch-active: #2196F3;
}

/* 深色主题 */
.theme-dark {
  --bg-color: #121212;
  --bg-color-secondary: #1e1e1e;
  --text-color: #ffffff;
  --text-color-secondary: #cccccc;
  --text-color-muted: #888888;
  --border-color: #333333;
  --border-color-light: #2a2a2a;
  --primary-color: #2196F3;
  --primary-gradient: linear-gradient(135deg, #1976D2, #1565C0);
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --error-color: #F44336;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  --card-bg: #1e1e1e;
  --input-bg: #2a2a2a;
  --input-border: #333333;
  --switch-bg: #333333;
  --switch-active: #2196F3;
}

/* 跟随系统主题 */
.theme-auto {
  /* 默认使用浅色主题变量 */
  --bg-color: #f8f9fa;
  --bg-color-secondary: #ffffff;
  --text-color: #333333;
  --text-color-secondary: #666666;
  --text-color-muted: #999999;
  --border-color: #e0e0e0;
  --border-color-light: #f0f0f0;
  --primary-color: #2196F3;
  --primary-gradient: linear-gradient(135deg, #2196F3, #21CBF3);
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --error-color: #F44336;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --card-bg: #ffffff;
  --input-bg: #ffffff;
  --input-border: #e0e0e0;
  --switch-bg: #e0e0e0;
  --switch-active: #2196F3;
}

/* 系统深色模式时的样式 */
@media (prefers-color-scheme: dark) {
  .theme-auto {
    --bg-color: #121212;
    --bg-color-secondary: #1e1e1e;
    --text-color: #ffffff;
    --text-color-secondary: #cccccc;
    --text-color-muted: #888888;
    --border-color: #333333;
    --border-color-light: #2a2a2a;
    --primary-color: #2196F3;
    --primary-gradient: linear-gradient(135deg, #1976D2, #1565C0);
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --error-color: #F44336;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    --card-bg: #1e1e1e;
    --input-bg: #2a2a2a;
    --input-border: #333333;
    --switch-bg: #333333;
    --switch-active: #2196F3;
  }
}

/* 通用样式应用 */
.themed {
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.themed-card {
  background-color: var(--card-bg);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.themed-input {
  background-color: var(--input-bg);
  color: var(--text-color);
  border: 1px solid var(--input-border);
  transition: all 0.3s ease;
  
  &::placeholder {
    color: var(--text-color-muted);
  }
}

.themed-text {
  color: var(--text-color);
  transition: color 0.3s ease;
}

.themed-text-secondary {
  color: var(--text-color-secondary);
  transition: color 0.3s ease;
}

.themed-text-muted {
  color: var(--text-color-muted);
  transition: color 0.3s ease;
}

.themed-border {
  border-color: var(--border-color);
  transition: border-color 0.3s ease;
}

.themed-primary {
  background: var(--primary-gradient);
  transition: background 0.3s ease;
}
