#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
初始化投票数据脚本
创建投票相关的数据库表并初始化数据
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

def init_vote_data():
    """初始化投票数据"""
    try:
        # 导入应用和模型
        from backend.main import app, db
        from backend.models.platform_vote import PlatformVote, UserVote
        
        with app.app_context():
            print("🔄 正在创建投票相关数据表...")
            
            # 创建表
            db.create_all()
            print("✅ 数据表创建成功")
            
            # 初始化平台投票数据
            platforms = [
                {'platform': 'zhihu', 'vote_count': 18},
                {'platform': 'weibo', 'vote_count': 12}
            ]
            
            for platform_data in platforms:
                platform = platform_data['platform']
                existing = PlatformVote.query.filter_by(platform=platform).first()
                
                if not existing:
                    vote = PlatformVote(
                        platform=platform,
                        vote_count=platform_data['vote_count']
                    )
                    db.session.add(vote)
                    print(f"✅ 初始化 {platform} 平台投票数据，初始票数: {platform_data['vote_count']}")
                else:
                    print(f"ℹ️  {platform} 平台投票数据已存在，当前票数: {existing.vote_count}")
            
            # 提交更改
            db.session.commit()
            print("✅ 投票数据初始化完成!")
            
            # 显示当前状态
            print("\n📊 当前投票状态:")
            all_votes = PlatformVote.get_all_votes()
            for platform, count in all_votes.items():
                print(f"   {platform}: {count} 票")
                
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🗳️  SuperTools 投票系统初始化")
    print("=" * 50)
    
    if init_vote_data():
        print("\n🎉 投票系统初始化成功!")
        print("现在用户可以为平台投票，数据将持久化存储。")
    else:
        print("\n💥 投票系统初始化失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
