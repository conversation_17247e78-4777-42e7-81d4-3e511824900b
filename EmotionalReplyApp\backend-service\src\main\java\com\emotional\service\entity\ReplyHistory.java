package com.emotional.service.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 回复历史实体类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("reply_history")
public class ReplyHistory {
    
    /**
     * 历史记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 原始消息
     */
    @TableField("original_message")
    private String originalMessage;
    
    /**
     * 情感分析结果
     */
    @TableField("emotion_result")
    private String emotionResult;
    
    /**
     * 情感置信度
     */
    @TableField("emotion_confidence")
    private Double emotionConfidence;
    
    /**
     * 生成的回复列表（JSON格式）
     */
    @TableField("reply_list")
    private String replyList;
    
    /**
     * 用户选择的回复
     */
    @TableField("selected_reply")
    private String selectedReply;
    
    /**
     * 选择的回复风格
     */
    @TableField("selected_style")
    private String selectedStyle;
    
    /**
     * 是否收藏：0-未收藏，1-已收藏
     */
    @TableField("is_favorite")
    private Integer isFavorite;
    
    /**
     * 用户评分：1-5分
     */
    @TableField("user_rating")
    private Integer userRating;
    
    /**
     * 用户反馈
     */
    @TableField("user_feedback")
    private String userFeedback;
    
    /**
     * 处理耗时（毫秒）
     */
    @TableField("process_time")
    private Long processTime;
    
    /**
     * 客户端IP
     */
    @TableField("client_ip")
    private String clientIp;
    
    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 逻辑删除标志：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
