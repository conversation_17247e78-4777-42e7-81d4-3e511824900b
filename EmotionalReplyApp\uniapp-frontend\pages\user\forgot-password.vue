<template>
  <view class="container">
    <!-- 步骤指示器 -->
    <view class="step-indicator">
      <view class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
        <view class="step-number">1</view>
        <text class="step-text">验证身份</text>
      </view>
      <view class="step-line" :class="{ active: currentStep > 1 }"></view>
      <view class="step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
        <view class="step-number">2</view>
        <text class="step-text">验证码</text>
      </view>
      <view class="step-line" :class="{ active: currentStep > 2 }"></view>
      <view class="step" :class="{ active: currentStep >= 3 }">
        <view class="step-number">3</view>
        <text class="step-text">重置密码</text>
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 步骤1: 验证身份 -->
      <view v-if="currentStep === 1" class="step-content">
        <view class="step-title">
          <text class="title">验证您的身份</text>
          <text class="subtitle">请输入您的注册邮箱</text>
        </view>

        <view class="input-group">
          <view class="input-wrapper" :class="{ 'error': identifierError, 'dropdown-active': showEmailDropdown }">
            <text class="input-icon">📧</text>
            <input
              v-model="emailUsername"
              class="input-field"
              placeholder="请输入用户名"
              type="text"
              :maxlength="30"
              @input="onEmailUsernameInput"
              @focus="onEmailFocus"
              @blur="onEmailBlur"
            />
            <view class="email-suffix-container">
              <text class="email-suffix" @click="toggleEmailDropdown">
                {{ selectedEmailSuffix }}
              </text>
              <text class="dropdown-arrow" @click="toggleEmailDropdown">
                {{ showEmailDropdown ? '▲' : '▼' }}
              </text>
            </view>
          </view>

          <!-- 邮箱后缀下拉选择 -->
          <view class="email-dropdown" v-if="showEmailDropdown">
            <view
              class="dropdown-item"
              v-for="(suffix, index) in emailSuffixes"
              :key="index"
              @click="selectEmailSuffix(suffix)"
              :class="{ 'selected': suffix === selectedEmailSuffix }"
            >
              <text class="suggestion-text">{{ suffix }}</text>
              <text class="check-icon" v-if="suffix === selectedEmailSuffix">✓</text>
            </view>
          </view>

          <text v-if="identifierError" class="error-text">{{ identifierError }}</text>
        </view>

        <button
          class="primary-btn"
          :class="{ disabled: !resetForm.identifier.trim(), loading: loading }"
          @click="handleStep1"
        >
          <text class="btn-text">{{ loading ? '验证中...' : '下一步' }}</text>
          <view class="loading-spinner" v-if="loading"></view>
        </button>
      </view>

      <!-- 步骤2: 验证码 -->
      <view v-if="currentStep === 2" class="step-content">
        <view class="step-title">
          <text class="title">输入验证码</text>
          <text class="subtitle">验证码已发送至 {{ maskedIdentifier }}</text>
        </view>

        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">🔢</text>
            <input
              v-model="resetForm.verificationCode"
              class="input-field"
              placeholder="请输入6位验证码"
              type="number"
              :maxlength="6"
            />
          </view>
        </view>

        <view class="verification-info">
          <text class="info-text">
            {{ countdown > 0 ? `${countdown}秒后可重新发送` : '没收到验证码？' }}
          </text>
          <text
            v-if="countdown === 0"
            class="resend-link"
            @click="resendCode"
          >
            重新发送
          </text>
        </view>

        <button
          class="primary-btn"
          :class="{ disabled: !canProceedStep2, loading: loading }"
          @click="handleStep2"
        >
          <text class="btn-text">{{ loading ? '验证中...' : '验证' }}</text>
          <view class="loading-spinner" v-if="loading"></view>
        </button>
      </view>

      <!-- 步骤3: 重置密码 -->
      <view v-if="currentStep === 3" class="step-content">
        <view class="step-title">
          <text class="title">设置新密码</text>
          <text class="subtitle">请设置您的新密码</text>
        </view>

        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">🔒</text>
            <input
              v-model="resetForm.newPassword"
              class="input-field"
              placeholder="请输入新密码"
              :type="showNewPassword ? 'text' : 'password'"
              :maxlength="20"
            />
            <text class="password-toggle" @click="toggleNewPassword">
              {{ showNewPassword ? '👁️' : '👁️‍🗨️' }}
            </text>
          </view>
        </view>

        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">🔒</text>
            <input
              v-model="resetForm.confirmPassword"
              class="input-field"
              placeholder="请确认新密码"
              :type="showConfirmPassword ? 'text' : 'password'"
              :maxlength="20"
            />
            <text class="password-toggle" @click="toggleConfirmPassword">
              {{ showConfirmPassword ? '👁️' : '👁️‍🗨️' }}
            </text>
          </view>
        </view>

        <view class="password-tips">
          <text class="tips-title">密码要求：</text>
          <text class="tips-item">• 长度6-20位</text>
          <text class="tips-item">• 必须包含数字</text>
          <text class="tips-item">• 可包含字母和特殊符号：!@#$%^&amp;*()_+-=[]{}|;:,.&lt;&gt;?</text>
        </view>

        <button
          class="primary-btn"
          :class="{ disabled: !canProceedStep3, loading: loading }"
          @click="handleStep3"
        >
          <text class="btn-text">{{ loading ? '重置中...' : '完成重置' }}</text>
          <view class="loading-spinner" v-if="loading"></view>
        </button>
      </view>

      <!-- 返回登录 -->
      <view class="back-section">
        <text class="back-text">想起密码了？</text>
        <text class="back-link" @click="goToLogin">返回登录</text>
      </view>
    </view>
  </view>
</template>

<script>
import { checkUserExists, sendResetPasswordCode, verifyResetCode, resetPassword } from '../../api/user.js'

export default {
  name: 'ForgotPasswordPage',

  data() {
    return {
      currentStep: 1,
      resetForm: {
        identifier: '', // 手机号或邮箱
        verificationCode: '',
        newPassword: '',
        confirmPassword: ''
      },
      showNewPassword: false,
      showConfirmPassword: false,
      loading: false,
      countdown: 0,
      countdownTimer: null,
      maskedIdentifier: '',
      identifierType: '', // 'phone' 或 'email'
      identifierError: '', // 输入验证错误信息
      emailUsername: '',
      selectedEmailSuffix: '@qq.com',
      showEmailDropdown: false,
      emailSuffixes: [
        '@qq.com',
        '@163.com',
        '@126.com',
        '@gmail.com'
      ]
    }
  },

  computed: {
    canProceedStep2() {
      return this.resetForm.verificationCode.trim().length === 6 && !this.loading
    },

    canProceedStep3() {
      return this.resetForm.newPassword.trim() &&
             this.resetForm.confirmPassword.trim() &&
             !this.loading
    }
  },

  watch: {
    // 监听邮箱用户名和后缀变化，自动更新完整邮箱
    emailUsername: {
      handler(newVal) {
        this.updateFullEmail()
      },
      immediate: true
    },
    selectedEmailSuffix: {
      handler(newVal) {
        this.updateFullEmail()
      },
      immediate: true
    }
  },

  onUnload() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
  },

  methods: {
    // 更新完整邮箱地址
    updateFullEmail() {
      if (this.emailUsername.trim()) {
        this.resetForm.identifier = this.emailUsername + this.selectedEmailSuffix
      } else {
        this.resetForm.identifier = ''
      }
      this.validateIdentifier()
    },

    // 邮箱用户名输入处理
    onEmailUsernameInput(e) {
      this.emailUsername = e.detail.value
    },

    // 邮箱输入框获得焦点
    onEmailFocus() {
      // 焦点时不自动显示下拉框，只有点击后缀时才显示
    },

    // 邮箱输入框失去焦点
    onEmailBlur() {
      // 延迟隐藏，让点击事件能够触发
      setTimeout(() => {
        this.showEmailDropdown = false
      }, 200)
    },

    // 切换邮箱下拉框
    toggleEmailDropdown() {
      this.showEmailDropdown = !this.showEmailDropdown
    },

    // 选择邮箱后缀
    selectEmailSuffix(suffix) {
      this.selectedEmailSuffix = suffix
      this.showEmailDropdown = false
    },

    // 验证邮箱格式
    validateIdentifier() {
      const email = this.resetForm.identifier.trim()

      if (!email) {
        this.identifierError = ''
        return
      }

      const isValidEmail = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email)

      if (!isValidEmail) {
        this.identifierError = '请输入正确的邮箱格式'
      } else {
        this.identifierError = ''
      }
    },

    // 步骤1: 验证身份（检查用户是否存在）
    async handleStep1() {
      const email = this.resetForm.identifier.trim()

      // 检查是否有输入
      if (!email) {
        uni.showToast({
          title: '请输入邮箱地址',
          icon: 'none'
        })
        return
      }

      // 检查邮箱格式
      const isValidEmail = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email)
      if (!isValidEmail) {
        uni.showToast({
          title: '请输入正确的邮箱格式',
          icon: 'none'
        })
        return
      }

      // 防止重复点击
      if (this.loading) return

      this.loading = true

      try {
        // 核心功能：检查用户是否存在
        uni.showLoading({
          title: '验证邮箱信息...'
        })

        const userExistsResult = await this.checkUserExists(email)

        uni.hideLoading()

        if (!userExistsResult.exists) {
          // 用户不存在，提供注册选项
          uni.showModal({
            title: '邮箱未注册',
            content: `该邮箱尚未注册，是否前往注册？`,
            showCancel: true,
            cancelText: '取消',
            confirmText: '去注册',
            success: (res) => {
              if (res.confirm) {
                uni.navigateTo({
                  url: '/pages/user/register'
                })
              }
            }
          })
          return
        }

        // 用户存在，继续后续流程
        this.identifierType = 'email'

        // 生成邮箱掩码显示
        const [username, domain] = email.split('@')
        const maskedUsername = username.length > 2
          ? username.substring(0, 2) + '***' + username.substring(username.length - 1)
          : username
        this.maskedIdentifier = maskedUsername + '@' + domain

        // 发送验证码
        try {
          const result = await this.sendVerificationCode()

          if (result && result.success !== false) {
            // 进入下一步
            this.currentStep = 2
            this.startCountdown()

            uni.showToast({
              title: '验证码已发送到邮箱',
              icon: 'success',
              duration: 2000
            })
          } else {
            uni.showToast({
              title: result?.message || '验证码发送失败，请重试',
              icon: 'none',
              duration: 3000
            })
          }
        } catch (sendError) {
          // 获取具体的错误信息
          let errorMessage = '验证码发送失败，请重试'

          if (sendError && sendError.message) {
            errorMessage = sendError.message
          } else if (sendError && sendError.data && sendError.data.message) {
            errorMessage = sendError.data.message
          } else if (sendError && typeof sendError === 'string') {
            errorMessage = sendError
          }

          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 3000
          })
        }

      } catch (error) {
        uni.hideLoading()

        // 获取具体的错误信息
        let errorMessage = '验证失败，请重试'

        if (error && error.message) {
          errorMessage = error.message
        } else if (error && error.data && error.data.message) {
          errorMessage = error.data.message
        } else if (error && typeof error === 'string') {
          errorMessage = error
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })
      } finally {
        this.loading = false
      }
    },

    // 步骤2: 验证码验证
    async handleStep2() {
      if (!this.canProceedStep2) return

      this.loading = true

      try {
        // 调用验证码验证API
        const result = await this.verifyResetCode(
          this.resetForm.identifier,
          this.resetForm.verificationCode
        )

        if (result.success) {
          this.currentStep = 3
          uni.showToast({
            title: '验证成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: result.message || '验证码错误，请重新输入',
            icon: 'none',
            duration: 3000
          })
        }

      } catch (error) {
        uni.showToast({
          title: '验证失败，请检查网络连接',
          icon: 'none',
          duration: 3000
        })
      } finally {
        this.loading = false
      }
    },

    // 步骤3: 重置密码
    async handleStep3() {
      if (!this.canProceedStep3) return

      // 验证密码
      if (this.resetForm.newPassword.length < 6 || this.resetForm.newPassword.length > 20) {
        uni.showToast({
          title: '密码长度必须为6-20位',
          icon: 'none'
        })
        return
      }

      // 密码验证：必须包含数字，可以包含字母和特殊符号
      const hasNumber = /\d/.test(this.resetForm.newPassword)
      const validChars = /^[A-Za-z\d!@#$%^&*()_+\-=\[\]{}|;:,.<>?]*$/.test(this.resetForm.newPassword)

      if (!hasNumber) {
        uni.showToast({
          title: '密码必须包含数字',
          icon: 'none'
        })
        return
      }

      if (!validChars) {
        uni.showToast({
          title: '密码包含不支持的字符',
          icon: 'none'
        })
        return
      }

      if (this.resetForm.newPassword !== this.resetForm.confirmPassword) {
        uni.showToast({
          title: '两次密码输入不一致',
          icon: 'none'
        })
        return
      }

      this.loading = true

      try {
        // 调用密码重置API
        const result = await this.resetPassword(
          this.resetForm.identifier,
          this.resetForm.verificationCode,
          this.resetForm.newPassword
        )

        if (result.success) {
          uni.showToast({
            title: '密码重置成功',
            icon: 'success'
          })

          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          uni.showToast({
            title: result.message || '重置失败，请重试',
            icon: 'none'
          })
        }

      } catch (error) {
        uni.showToast({
          title: '重置失败，请检查网络连接',
          icon: 'none',
          duration: 3000
        })
      } finally {
        this.loading = false
      }
    },

    // 检查用户是否存在
    async checkUserExists(email) {
      const result = await checkUserExists(email)

      // request.js已经处理了响应，直接返回 { exists: true/false }
      return result
    },

    // 发送验证码
    async sendVerificationCode() {
      const result = await sendResetPasswordCode(this.resetForm.identifier)
      return result
    },

    // 验证重置验证码
    async verifyResetCode(identifier, code) {
      try {
        const result = await verifyResetCode(identifier, code)
        return result
      } catch (error) {
        throw error
      }
    },

    // 重置密码
    async resetPassword(identifier, code, newPassword) {
      try {
        const result = await resetPassword(identifier, code, newPassword)
        return result
      } catch (error) {
        throw error
      }
    },

    // 重新发送验证码
    async resendCode() {
      if (this.countdown > 0) return

      try {
        const result = await this.sendVerificationCode()

        if (result && result.success !== false) {
          this.startCountdown()
          uni.showToast({
            title: '验证码已重新发送',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: result?.message || '发送失败，请重试',
            icon: 'none'
          })
        }
      } catch (error) {
        uni.showToast({
          title: '发送失败，请检查网络连接',
          icon: 'none'
        })
      }
    },

    // 开始倒计时
    startCountdown() {
      this.countdown = 60
      this.countdownTimer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.countdownTimer)
          this.countdownTimer = null
        }
      }, 1000)
    },

    // 切换新密码显示
    toggleNewPassword() {
      this.showNewPassword = !this.showNewPassword
    },

    // 切换确认密码显示
    toggleConfirmPassword() {
      this.showConfirmPassword = !this.showConfirmPassword
    },

    // 返回登录
    goToLogin() {
      uni.navigateBack()
    },

    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 40rpx 30rpx;
}

// 步骤指示器
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60rpx;

  .step {
    display: flex;
    flex-direction: column;
    align-items: center;

    .step-number {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: #e9ecef;
      color: #6c757d;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      font-weight: bold;
      margin-bottom: 12rpx;
      transition: all 0.3s ease;
    }

    .step-text {
      color: #6c757d;
      font-size: 22rpx;
      transition: all 0.3s ease;
    }

    &.active {
      .step-number {
        background: #2196F3;
        color: white;
      }

      .step-text {
        color: #2196F3;
      }
    }

    &.completed {
      .step-number {
        background: #4CAF50;
        color: white;
      }

      .step-text {
        color: #4CAF50;
      }
    }
  }

  .step-line {
    width: 80rpx;
    height: 4rpx;
    background: #e9ecef;
    margin: 0 20rpx;
    transition: all 0.3s ease;

    &.active {
      background: #2196F3;
    }
  }
}

// 表单容器
.form-container {
  background: white;
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

  .step-content {
    .step-title {
      text-align: center;
      margin-bottom: 50rpx;

      .title {
        display: block;
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 16rpx;
      }

      .subtitle {
        color: #666;
        font-size: 26rpx;
        line-height: 1.5;
      }

      .demo-tip {
        display: block;
        color: #2196F3;
        font-size: 22rpx;
        margin-top: 12rpx;
        padding: 8rpx 16rpx;
        background: rgba(33, 150, 243, 0.1);
        border-radius: 8rpx;
        border: 1rpx solid rgba(33, 150, 243, 0.2);
      }
    }

    .input-group {
      margin-bottom: 30rpx;
      position: relative;

      .error-text {
        color: #dc3545;
        font-size: 24rpx;
        margin-top: 12rpx;
        margin-left: 20rpx;
      }

      .input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        background: #f8f9fa;
        border-radius: 16rpx;
        border: 2rpx solid #e9ecef;
        transition: all 0.3s ease;

        &:focus-within, &.dropdown-active {
          border-color: #2196F3;
          background: white;
          box-shadow: 0 0 0 6rpx rgba(33, 150, 243, 0.1);
        }

        &.error {
          border-color: #dc3545;
          background: #fff5f5;

          &:focus-within {
            border-color: #dc3545;
            box-shadow: 0 0 0 6rpx rgba(220, 53, 69, 0.1);
          }
        }

        .input-icon {
          padding: 0 20rpx;
          font-size: 32rpx;
          color: #6c757d;
        }

        .input-field {
          flex: 1;
          height: 88rpx;
          padding: 0 20rpx;
          border: none;
          background: transparent;
          font-size: 28rpx;
          color: #333;

          &::placeholder {
            color: #adb5bd;
          }
        }

        .email-suffix-container {
          display: flex;
          align-items: center;
          border-left: 1rpx solid #e9ecef;
          padding: 0 10rpx;
          min-width: 160rpx;

          .email-suffix {
            font-size: 28rpx;
            color: #6c757d;
            cursor: pointer;
            padding: 8rpx 12rpx;
            border-radius: 8rpx;
            transition: all 0.2s ease;

            &:active {
              background: rgba(108, 117, 125, 0.1);
            }
          }

          .dropdown-arrow {
            font-size: 20rpx;
            color: #6c757d;
            cursor: pointer;
            transition: transform 0.3s ease;
            margin-left: 8rpx;

            &:active {
              transform: scale(0.9);
            }
          }
        }

        .password-toggle {
          padding: 0 20rpx;
          font-size: 28rpx;
          color: #6c757d;
          cursor: pointer;

          &:active {
            opacity: 0.7;
          }
        }
      }

      .email-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 2rpx solid #e9ecef;
        border-top: none;
        border-radius: 0 0 16rpx 16rpx;
        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
        max-height: 400rpx;
        overflow-y: auto;
        z-index: 1000;

        .dropdown-item {
          padding: 20rpx 30rpx;
          border-bottom: 1rpx solid #f1f3f4;
          cursor: pointer;
          transition: background-color 0.2s ease;
          display: flex;
          justify-content: space-between;
          align-items: center;

          &:hover {
            background-color: #f8f9fa;
          }

          &:active {
            background-color: #e9ecef;
          }

          &.selected {
            background-color: rgba(33, 150, 243, 0.1);
          }

          &:last-child {
            border-bottom: none;
          }

          .suggestion-text {
            font-size: 28rpx;
            color: #333;
          }

          .check-icon {
            font-size: 24rpx;
            color: #2196F3;
            font-weight: bold;
          }
        }
      }
    }

    .verification-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;

      .info-text {
        color: #666;
        font-size: 24rpx;
      }

      .resend-link {
        color: #2196F3;
        font-size: 24rpx;
        cursor: pointer;

        &:active {
          opacity: 0.7;
        }
      }
    }

    .password-tips {
      background: #f8f9fa;
      border-radius: 12rpx;
      padding: 24rpx;
      margin-bottom: 40rpx;

      .tips-title {
        display: block;
        font-size: 26rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 16rpx;
      }

      .tips-item {
        display: block;
        font-size: 24rpx;
        color: #666;
        line-height: 1.6;
        margin-bottom: 8rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .primary-btn {
      width: 100%;
      height: 88rpx;
      background: linear-gradient(135deg, #2196F3, #21CBF3);
      border-radius: 16rpx;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 40rpx;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;

      .btn-text {
        color: white;
        font-size: 32rpx;
        font-weight: bold;
      }

      .loading-spinner {
        width: 32rpx;
        height: 32rpx;
        border: 3rpx solid rgba(255, 255, 255, 0.3);
        border-top: 3rpx solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-left: 16rpx;
      }

      &.disabled {
        background: #dee2e6;
        pointer-events: none;

        .btn-text {
          color: #6c757d;
        }
      }

      &.loading {
        pointer-events: none;
      }

      &:active:not(.disabled):not(.loading) {
        transform: translateY(2rpx);
      }
    }
  }

  .back-section {
    text-align: center;
    padding-top: 20rpx;
    border-top: 1rpx solid #f0f0f0;

    .back-text {
      font-size: 26rpx;
      color: #666;
    }

    .back-link {
      color: #2196F3;
      margin-left: 8rpx;
      font-size: 26rpx;
      cursor: pointer;

      &:active {
        opacity: 0.7;
      }
    }
  }
}

// 动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
