<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>版本管理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #1976D2;
        }
        .btn-danger {
            background: #f44336;
        }
        .btn-danger:hover {
            background: #d32f2f;
        }
        .btn-warning {
            background: #ff9800;
        }
        .btn-warning:hover {
            background: #f57c00;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .version-list {
            margin-top: 20px;
        }
        .version-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .version-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .version-name {
            font-size: 18px;
            font-weight: bold;
        }
        .version-status {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
        }
        .status-published {
            background: #4caf50;
        }
        .status-unpublished {
            background: #ff9800;
        }
        .status-draft {
            background: #9e9e9e;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>版本管理系统测试</h1>
    
    <!-- 发布新版本 -->
    <div class="container">
        <h2>发布新版本</h2>
        <form id="publishForm">
            <div class="form-group">
                <label>版本名称:</label>
                <input type="text" id="versionName" placeholder="如: 1.1.0" required>
            </div>
            <div class="form-group">
                <label>版本号:</label>
                <input type="number" id="versionCode" placeholder="如: 110" required>
            </div>
            <div class="form-group">
                <label>平台:</label>
                <select id="platform" required>
                    <option value="android">Android</option>
                    <option value="ios">iOS</option>
                    <option value="h5">H5</option>
                </select>
            </div>
            <div class="form-group">
                <label>下载地址:</label>
                <input type="text" id="downloadUrl" placeholder="下载链接或在线使用">
            </div>
            <div class="form-group">
                <label>文件大小:</label>
                <input type="text" id="fileSize" placeholder="如: 5.2MB 或 在线使用">
            </div>
            <div class="form-group">
                <label>最低支持版本:</label>
                <input type="text" id="minSupportVersion" placeholder="如: 1.0.0">
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="isForceUpdate"> 强制更新
                </label>
            </div>
            <div class="form-group">
                <label>更新内容:</label>
                <textarea id="updateContent" placeholder="请输入更新内容..." required></textarea>
            </div>
            <button type="submit" class="btn">发布版本</button>
        </form>
        <div id="publishResult" class="result" style="display: none;"></div>
    </div>

    <!-- 版本列表管理 -->
    <div class="container">
        <h2>版本列表管理</h2>
        <div>
            <label>平台筛选:</label>
            <select id="filterPlatform">
                <option value="">全部平台</option>
                <option value="android">Android</option>
                <option value="ios">iOS</option>
                <option value="h5">H5</option>
            </select>
            
            <label>状态筛选:</label>
            <select id="filterStatus">
                <option value="">全部状态</option>
                <option value="published">已发布</option>
                <option value="unpublished">已下架</option>
                <option value="draft">草稿</option>
            </select>
            
            <button class="btn" onclick="loadVersions()">加载版本列表</button>
        </div>
        
        <div id="versionList" class="version-list"></div>
        <div id="listResult" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';

        // 发布新版本
        document.getElementById('publishForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const versionData = {
                versionName: document.getElementById('versionName').value,
                versionCode: parseInt(document.getElementById('versionCode').value),
                platform: document.getElementById('platform').value,
                downloadUrl: document.getElementById('downloadUrl').value,
                fileSize: document.getElementById('fileSize').value,
                minSupportVersion: document.getElementById('minSupportVersion').value,
                isForceUpdate: document.getElementById('isForceUpdate').checked,
                updateContent: document.getElementById('updateContent').value
            };

            try {
                const response = await fetch(`${API_BASE}/version/publish`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(versionData)
                });

                const result = await response.json();
                showResult('publishResult', result, response.ok);
                
                if (response.ok) {
                    document.getElementById('publishForm').reset();
                    loadVersions(); // 刷新版本列表
                }
            } catch (error) {
                showResult('publishResult', { message: '网络错误: ' + error.message }, false);
            }
        });

        // 加载版本列表
        async function loadVersions() {
            const platform = document.getElementById('filterPlatform').value;
            const status = document.getElementById('filterStatus').value;
            
            const params = new URLSearchParams({ page: 1, size: 50 });
            if (platform) params.append('platform', platform);
            if (status) params.append('status', status);

            try {
                const response = await fetch(`${API_BASE}/version/list?${params}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    displayVersions(result.data.list || []);
                    showResult('listResult', result, true);
                } else {
                    showResult('listResult', result, false);
                }
            } catch (error) {
                showResult('listResult', { message: '网络错误: ' + error.message }, false);
            }
        }

        // 显示版本列表
        function displayVersions(versions) {
            const container = document.getElementById('versionList');
            
            if (versions.length === 0) {
                container.innerHTML = '<p>暂无版本数据</p>';
                return;
            }

            container.innerHTML = versions.map(version => `
                <div class="version-item">
                    <div class="version-header">
                        <div>
                            <div class="version-name">${version.versionName} (${version.platform})</div>
                            <div>版本号: ${version.versionCode} | 发布时间: ${formatDate(version.releaseDate)}</div>
                        </div>
                        <div class="version-status status-${version.status}">
                            ${getStatusText(version.status)}
                        </div>
                    </div>
                    <div>
                        <strong>更新内容:</strong> ${version.updateContent}
                    </div>
                    <div style="margin-top: 10px;">
                        ${version.status === 'published' ? 
                            `<button class="btn btn-warning" onclick="unpublishVersion(${version.id})">下架</button>` :
                            `<button class="btn" onclick="republishVersion(${version.id})">重新发布</button>`
                        }
                        <button class="btn btn-danger" onclick="deleteVersion(${version.id})">删除</button>
                    </div>
                </div>
            `).join('');
        }

        // 下架版本
        async function unpublishVersion(id) {
            if (!confirm('确定要下架这个版本吗？')) return;

            try {
                const response = await fetch(`${API_BASE}/version/unpublish/${id}`, {
                    method: 'PUT'
                });
                const result = await response.json();
                showResult('listResult', result, response.ok);
                
                if (response.ok) {
                    loadVersions();
                }
            } catch (error) {
                showResult('listResult', { message: '网络错误: ' + error.message }, false);
            }
        }

        // 重新发布版本
        async function republishVersion(id) {
            // 这里需要先获取版本详情，然后重新发布
            alert('重新发布功能需要先获取版本详情，暂未实现');
        }

        // 删除版本
        async function deleteVersion(id) {
            if (!confirm('确定要删除这个版本吗？此操作不可恢复！')) return;

            try {
                const response = await fetch(`${API_BASE}/version/${id}`, {
                    method: 'DELETE'
                });
                const result = await response.json();
                showResult('listResult', result, response.ok);
                
                if (response.ok) {
                    loadVersions();
                }
            } catch (error) {
                showResult('listResult', { message: '网络错误: ' + error.message }, false);
            }
        }

        // 显示结果
        function showResult(elementId, result, isSuccess) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.textContent = JSON.stringify(result, null, 2);
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            return new Date(dateStr).toLocaleString('zh-CN');
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'published': '已发布',
                'unpublished': '已下架',
                'draft': '草稿'
            };
            return statusMap[status] || status;
        }

        // 页面加载时自动加载版本列表
        window.addEventListener('load', loadVersions);
    </script>
</body>
</html>
