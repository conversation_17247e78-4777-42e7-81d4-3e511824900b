package com.emotional.service.llm.impl;

import com.emotional.service.dto.llm.EmotionAnalysisRequest;
import com.emotional.service.dto.llm.EmotionAnalysisResponse;
import com.emotional.service.dto.llm.ReplyGenerationRequest;
import com.emotional.service.dto.llm.ReplyGenerationResponse;
import com.emotional.service.service.AdminNotificationService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * DeepSeek AI服务
 * 专门用于情感分析和回复生成的AI服务
 */
@Slf4j
@Service
public class DeepSeekService {
    
    @Value("${app.llm.deepseek.api-key:}")
    private String apiKey;

    @Value("${app.llm.deepseek.base-url:https://api.deepseek.com/v1}")
    private String baseUrl;

    @Value("${app.llm.deepseek.model:deepseek-chat}")
    private String model;

    @Value("${app.llm.deepseek.enabled:true}")
    private boolean enabled;

    @Value("${app.llm.deepseek.balance-warning-threshold:10.0}")
    private Double balanceWarningThreshold;

    @Value("${app.llm.deepseek.balance-check-enabled:true}")
    private boolean balanceCheckEnabled;
    
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final AdminNotificationService adminNotificationService;

    public DeepSeekService(RestTemplate restTemplate, ObjectMapper objectMapper,
                          AdminNotificationService adminNotificationService) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
        this.adminNotificationService = adminNotificationService;
    }
    
    /**
     * 情感分析
     * @param request 分析请求
     * @return 分析结果
     */
    public EmotionAnalysisResponse analyzeEmotion(EmotionAnalysisRequest request) {
        if (!isAvailable()) {
            return EmotionAnalysisResponse.builder()
                    .success(false)
                    .errorMessage("DeepSeek服务不可用，请联系管理员配置API密钥")
                    .build();
        }
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 构建情感分析的prompt
            String prompt = buildEmotionAnalysisPrompt(request);
            
            // 调用DeepSeek API
            String response = callDeepSeek(prompt, request.getTemperature());
            
            // 解析响应
            EmotionAnalysisResponse result = parseEmotionResponse(response);
            result.setProcessingTime(System.currentTimeMillis() - startTime);
            result.setModelName(model);
            
            log.info("DeepSeek情感分析完成，耗时: {}ms", result.getProcessingTime());
            return result;
            
        } catch (Exception e) {
            log.error("DeepSeek情感分析失败", e);
            String errorMessage = parseErrorMessage(e);
            return EmotionAnalysisResponse.builder()
                    .success(false)
                    .errorMessage(errorMessage)
                    .processingTime(System.currentTimeMillis() - startTime)
                    .build();
        }
    }
    
    /**
     * 生成回复
     * @param request 生成请求
     * @return 生成结果
     */
    public ReplyGenerationResponse generateReply(ReplyGenerationRequest request) {
        if (!isAvailable()) {
            return ReplyGenerationResponse.builder()
                    .success(false)
                    .errorMessage("DeepSeek服务不可用，请联系管理员！")
                    .build();
        }
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 构建回复生成的prompt
            String prompt = buildReplyGenerationPrompt(request);
            
            // 调用DeepSeek API
            String response = callDeepSeek(prompt, request.getTemperature());
            
            // 解析响应
            ReplyGenerationResponse result = parseReplyResponse(response, request);
            result.setProcessingTime(System.currentTimeMillis() - startTime);
            result.setModelName(model);
            
            log.info("DeepSeek回复生成完成，生成{}个回复，耗时: {}ms", 
                    result.getReplies().size(), result.getProcessingTime());
            return result;
            
        } catch (Exception e) {
            log.error("DeepSeek回复生成失败", e);
            String errorMessage = parseErrorMessage(e);
            return ReplyGenerationResponse.builder()
                    .success(false)
                    .errorMessage(errorMessage)
                    .processingTime(System.currentTimeMillis() - startTime)
                    .build();
        }
    }
    
    /**
     * 检查服务是否可用
     * @return 是否可用
     */
    public boolean isAvailable() {
        return enabled && apiKey != null && !apiKey.trim().isEmpty();
    }

    /**
     * 获取服务提供商名称
     * @return 提供商名称
     */
    public String getProviderName() {
        return "DeepSeek";
    }

    /**
     * 检查账户余额
     * @return 余额信息
     */
    public BalanceInfo checkBalance() {
        if (!isAvailable()) {
            return BalanceInfo.builder()
                    .success(false)
                    .errorMessage("DeepSeek服务不可用")
                    .build();
        }

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    baseUrl + "/user/balance",
                    HttpMethod.GET,
                    entity,
                    String.class
            );

            if (response.getStatusCode() == HttpStatus.OK) {
                return parseBalanceResponse(response.getBody());
            } else {
                return BalanceInfo.builder()
                        .success(false)
                        .errorMessage("获取余额失败: " + response.getStatusCode())
                        .build();
            }

        } catch (Exception e) {
            log.error("检查DeepSeek余额失败", e);
            return BalanceInfo.builder()
                    .success(false)
                    .errorMessage("检查余额异常: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 余额信息DTO
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class BalanceInfo {
        private Boolean success;
        private Double totalBalance;
        private Double availableBalance;
        private String currency;
        private String errorMessage;
        private Boolean needWarning;
    }
    
    /**
     * 构建情感分析的prompt
     */
    private String buildEmotionAnalysisPrompt(EmotionAnalysisRequest request) {
        return String.format(
            "你是一个专业的情感分析专家。请分析以下消息的情感，并严格按照JSON格式返回结果。\n\n" +
            "消息内容：\"%s\"\n\n" +
            "请返回以下格式的JSON（不要包含任何其他文字）：\n" +
            "{\n" +
            "  \"primaryEmotion\": \"主要情感类型（从以下选择：喜悦、愤怒、悲伤、恐惧、惊讶、厌恶、中性、焦虑、期待、失望）\",\n" +
            "  \"confidence\": 置信度(0.0-1.0的数字),\n" +
            "  \"intensity\": 强度(1-5的整数),\n" +
            "  \"emotionScores\": {\n" +
            "    \"喜悦\": 0.1,\n" +
            "    \"愤怒\": 0.8,\n" +
            "    \"悲伤\": 0.1,\n" +
            "    \"恐惧\": 0.0,\n" +
            "    \"惊讶\": 0.0,\n" +
            "    \"厌恶\": 0.0,\n" +
            "    \"中性\": 0.0,\n" +
            "    \"焦虑\": 0.0,\n" +
            "    \"期待\": 0.0,\n" +
            "    \"失望\": 0.0\n" +
            "  },\n" +
            "  \"keywords\": [\"关键词1\", \"关键词2\", \"关键词3\"],\n" +
            "  \"summary\": \"简短的情感分析摘要\",\n" +
            "  \"suggestedStyles\": [\"建议的回复风格1\", \"建议的回复风格2\"]\n" +
            "}",
            request.getMessage());
    }
    
    /**
     * 构建回复生成的prompt
     */
    private String buildReplyGenerationPrompt(ReplyGenerationRequest request) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("你是一个情感回复专家。请为以下消息生成").append(request.getCount()).append("个不同风格的回复。\n\n");
        prompt.append("原始消息：\"").append(request.getOriginalMessage()).append("\"\n");
        
        if (request.getEmotionAnalysis() != null) {
            prompt.append("检测到的情感：").append(request.getEmotionAnalysis().getPrimaryEmotion()).append("\n");
        }
        
        if (request.getReplyStyles() != null && !request.getReplyStyles().isEmpty()) {
            prompt.append("要求的回复风格：").append(String.join("、", request.getReplyStyles())).append("\n");
        }

        // 添加性别信息
        if (request.getSenderGender() != null && !request.getSenderGender().trim().isEmpty()) {
            String genderDesc = "";
            switch (request.getSenderGender().toLowerCase()) {
                case "male":
                    genderDesc = "男性";
                    break;
                case "female":
                    genderDesc = "女性";
                    break;
                default:
                    genderDesc = "未知性别";
                    break;
            }
            prompt.append("对方性别：").append(genderDesc).append("\n");
        }

        prompt.append("\n回复要求：\n");
        prompt.append("- 长度：").append(request.getMinLength()).append("-").append(request.getMaxLength()).append("字符\n");
        prompt.append("- 语言：中文\n");
        prompt.append("- 要有同理心和情感共鸣\n");
        prompt.append("- 根据不同风格调整语气和内容\n");

        // 根据性别调整回复建议
        if (request.getSenderGender() != null && !request.getSenderGender().trim().isEmpty()) {
            prompt.append("- 请根据对方性别调整回复的语气和用词，使回复更加合适和自然\n");
        }

        prompt.append("\n");
        
        prompt.append("请严格按照以下JSON格式返回（不要包含任何其他文字）：\n");
        prompt.append("{\n");
        prompt.append("  \"replies\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"content\": \"具体的回复内容\",\n");
        prompt.append("      \"style\": \"回复风格标识\",\n");
        prompt.append("      \"styleName\": \"风格中文名称\",\n");
        prompt.append("      \"qualityScore\": 0.9,\n");
        prompt.append("      \"emotionMatch\": 0.8,\n");
        prompt.append("      \"recommendationScore\": 4,\n");
        prompt.append("      \"explanation\": \"为什么这样回复的简短解释\"\n");
        prompt.append("    }\n");
        prompt.append("  ]\n");
        prompt.append("}\n");
        
        return prompt.toString();
    }
    
    /**
     * 调用DeepSeek API
     */
    @SuppressWarnings("unchecked")
    private String callDeepSeek(String prompt, Double temperature) throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(apiKey);
        
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", model);

        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", prompt);
        requestBody.put("messages", Arrays.asList(userMessage));

        requestBody.put("temperature", temperature != null ? temperature : 0.7);
        requestBody.put("max_tokens", 2000);
        requestBody.put("stream", false);
        
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
        
        log.debug("调用DeepSeek API: {}", baseUrl + "/chat/completions");
        
        ResponseEntity<String> response = restTemplate.exchange(
                baseUrl + "/chat/completions",
                HttpMethod.POST,
                entity,
                String.class
        );
        
        if (response.getStatusCode() == HttpStatus.OK) {
            Map<String, Object> responseBody = objectMapper.readValue(response.getBody(), Map.class);
            List<Map<String, Object>> choices = (List<Map<String, Object>>) responseBody.get("choices");
            if (choices != null && !choices.isEmpty()) {
                Map<String, Object> message = (Map<String, Object>) choices.get(0).get("message");
                return (String) message.get("content");
            }
        }
        
        // 解析具体的错误信息
        String errorDetail = parseApiErrorResponse(response.getBody(), response.getStatusCode());
        throw new RuntimeException(errorDetail);
    }
    
    /**
     * 解析情感分析响应
     */
    @SuppressWarnings("unchecked")
    private EmotionAnalysisResponse parseEmotionResponse(String response) throws Exception {
        try {
            // 提取JSON部分
            String jsonStr = extractJson(response);
            log.debug("解析情感分析响应: {}", jsonStr);
            
            Map<String, Object> data = objectMapper.readValue(jsonStr, Map.class);
            
            return EmotionAnalysisResponse.builder()
                    .primaryEmotion((String) data.get("primaryEmotion"))
                    .confidence(((Number) data.get("confidence")).doubleValue())
                    .intensity(((Number) data.get("intensity")).intValue())
                    .emotionScores((Map<String, Double>) data.get("emotionScores"))
                    .keywords((List<String>) data.get("keywords"))
                    .summary((String) data.get("summary"))
                    .suggestedStyles((List<String>) data.get("suggestedStyles"))
                    .success(true)
                    .build();
        } catch (Exception e) {
            log.error("解析情感分析响应失败: {}", response, e);
            throw e;
        }
    }
    
    /**
     * 解析回复生成响应
     */
    @SuppressWarnings("unchecked")
    private ReplyGenerationResponse parseReplyResponse(String response, ReplyGenerationRequest request) throws Exception {
        try {
            String jsonStr = extractJson(response);
            log.debug("解析回复生成响应: {}", jsonStr);
            
            Map<String, Object> data = objectMapper.readValue(jsonStr, Map.class);
            
            List<Map<String, Object>> repliesData = (List<Map<String, Object>>) data.get("replies");
            List<ReplyGenerationResponse.GeneratedReply> replies = new ArrayList<>();
            
            for (Map<String, Object> replyData : repliesData) {
                replies.add(ReplyGenerationResponse.GeneratedReply.builder()
                        .content((String) replyData.get("content"))
                        .style((String) replyData.get("style"))
                        .styleName((String) replyData.get("styleName"))
                        .qualityScore(getDoubleValue(replyData, "qualityScore", 0.8))
                        .emotionMatch(getDoubleValue(replyData, "emotionMatch", 0.7))
                        .recommendationScore(getIntValue(replyData, "recommendationScore", 3))
                        .explanation((String) replyData.get("explanation"))
                        .build());
            }
            
            return ReplyGenerationResponse.builder()
                    .replies(replies)
                    .success(true)
                    .build();
        } catch (Exception e) {
            log.error("解析回复生成响应失败: {}", response, e);
            throw e;
        }
    }
    
    /**
     * 从响应中提取JSON
     */
    private String extractJson(String response) {
        if (response == null || response.trim().isEmpty()) {
            throw new RuntimeException("响应内容为空");
        }
        
        // 查找JSON开始和结束位置
        int start = response.indexOf("{");
        int end = response.lastIndexOf("}");
        
        if (start != -1 && end != -1 && end > start) {
            return response.substring(start, end + 1);
        }
        
        // 如果没找到完整的JSON，尝试清理响应
        String cleaned = response.trim();
        if (cleaned.startsWith("```json")) {
            cleaned = cleaned.substring(7);
        }
        if (cleaned.endsWith("```")) {
            cleaned = cleaned.substring(0, cleaned.length() - 3);
        }
        
        return cleaned.trim();
    }
    
    /**
     * 安全获取Double值
     */
    private Double getDoubleValue(Map<String, Object> map, String key, Double defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return defaultValue;
    }
    
    /**
     * 安全获取Integer值
     */
    private Integer getIntValue(Map<String, Object> map, String key, Integer defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }

    /**
     * 解析异常错误信息并通知管理员
     */
    private String parseErrorMessage(Exception e) {
        String message = e.getMessage();
        if (message == null) {
            adminNotificationService.notifyServiceError("DeepSeek服务异常，错误信息为空", null);
            return "DeepSeek服务异常，请联系管理员检查服务状态";
        }

        // 检查是否是API密钥相关错误
        if (message.contains("401") || message.contains("Unauthorized") ||
            message.contains("Invalid API key") || message.contains("authentication")) {
            adminNotificationService.notifyApiKeyIssue("API密钥无效", message, null);
            return "API密钥无效或已过期，请联系管理员更新DeepSeek API密钥";
        }

        // 检查是否是余额不足错误
        if (message.contains("402") || message.contains("insufficient") ||
            message.contains("quota") || message.contains("balance") ||
            message.contains("Payment Required")) {
            adminNotificationService.notifyInsufficientBalance(message, null);
            return "DeepSeek API余额不足，请联系管理员充值账户";
        }

        // 检查是否是请求频率限制
        if (message.contains("429") || message.contains("rate limit") ||
            message.contains("Too Many Requests")) {
            adminNotificationService.notifyServiceError("API请求频率限制: " + message, null);
            return "请求过于频繁，请稍后重试或联系管理员升级API套餐";
        }

        // 检查是否是服务器错误
        if (message.contains("500") || message.contains("502") ||
            message.contains("503") || message.contains("504")) {
            adminNotificationService.notifyServiceError("DeepSeek服务器错误: " + message, null);
            return "DeepSeek服务器暂时不可用，请稍后重试";
        }

        // 检查是否是网络连接错误
        if (message.contains("timeout") || message.contains("connection") ||
            message.contains("ConnectException")) {
            adminNotificationService.notifyServiceError("网络连接错误: " + message, null);
            return "网络连接超时，请检查网络连接或联系管理员";
        }

        // 默认错误信息
        adminNotificationService.notifyServiceError("DeepSeek服务未知异常: " + message, null);
        return "DeepSeek服务异常: " + message + "，请联系管理员处理";
    }

    /**
     * 解析API响应错误信息
     */
    @SuppressWarnings("unchecked")
    private String parseApiErrorResponse(String responseBody, HttpStatus statusCode) {
        try {
            if (responseBody != null && !responseBody.trim().isEmpty()) {
                Map<String, Object> errorResponse = objectMapper.readValue(responseBody, Map.class);

                // 尝试获取错误详情
                Object error = errorResponse.get("error");
                if (error instanceof Map) {
                    Map<String, Object> errorMap = (Map<String, Object>) error;
                    String errorMessage = (String) errorMap.get("message");
                    String errorType = (String) errorMap.get("type");

                    if (errorMessage != null) {
                        return parseSpecificError(errorMessage, errorType, statusCode);
                    }
                }

                // 尝试直接获取message字段
                String message = (String) errorResponse.get("message");
                if (message != null) {
                    return parseSpecificError(message, null, statusCode);
                }
            }
        } catch (Exception e) {
            log.warn("解析API错误响应失败: {}", e.getMessage());
        }

        // 根据状态码返回通用错误信息
        return getGenericErrorMessage(statusCode);
    }

    /**
     * 解析具体错误信息
     */
    private String parseSpecificError(String errorMessage, String errorType, HttpStatus statusCode) {
        if (errorMessage == null) {
            return getGenericErrorMessage(statusCode);
        }

        String lowerMessage = errorMessage.toLowerCase();

        // API密钥相关错误
        if (lowerMessage.contains("invalid api key") ||
            lowerMessage.contains("authentication") ||
            lowerMessage.contains("unauthorized")) {
            return "API密钥无效或已过期，请联系管理员更新DeepSeek API密钥";
        }

        // 余额不足错误
        if (lowerMessage.contains("insufficient") ||
            lowerMessage.contains("quota") ||
            lowerMessage.contains("balance") ||
            lowerMessage.contains("payment required")) {
            return "DeepSeek API余额不足，请联系管理员充值账户";
        }

        // 请求频率限制
        if (lowerMessage.contains("rate limit") ||
            lowerMessage.contains("too many requests")) {
            return "请求过于频繁，请稍后重试或联系管理员升级API套餐";
        }

        // 返回原始错误信息，但添加联系管理员的提示
        return "DeepSeek API错误: " + errorMessage + "，请联系管理员处理";
    }

    /**
     * 根据状态码获取通用错误信息
     */
    private String getGenericErrorMessage(HttpStatus statusCode) {
        switch (statusCode.value()) {
            case 401:
                return "API密钥无效或已过期，请联系管理员更新DeepSeek API密钥";
            case 402:
                return "DeepSeek API余额不足，请联系管理员充值账户";
            case 429:
                return "请求过于频繁，请稍后重试或联系管理员升级API套餐";
            case 500:
            case 502:
            case 503:
            case 504:
                return "DeepSeek服务器暂时不可用，请稍后重试";
            default:
                return "DeepSeek API调用失败 (状态码: " + statusCode.value() + ")，请联系管理员处理";
        }
    }

    /**
     * 解析余额响应
     */
    @SuppressWarnings("unchecked")
    private BalanceInfo parseBalanceResponse(String responseBody) {
        try {
            Map<String, Object> response = objectMapper.readValue(responseBody, Map.class);

            // DeepSeek API的余额响应格式：
            // {
            //   "is_available": true,
            //   "balance_infos": [
            //     {
            //       "currency": "CNY",
            //       "total_balance": "110.00",
            //       "granted_balance": "10.00",
            //       "topped_up_balance": "100.00"
            //     }
            //   ]
            // }

            Double totalBalance = null;
            Double availableBalance = null;
            String currency = "CNY"; // DeepSeek默认使用人民币

            // 解析新的API响应格式
            if (response.containsKey("balance_infos")) {
                List<Map<String, Object>> balanceInfos = (List<Map<String, Object>>) response.get("balance_infos");
                if (!balanceInfos.isEmpty()) {
                    Map<String, Object> balanceInfo = balanceInfos.get(0); // 取第一个余额信息

                    if (balanceInfo.containsKey("currency")) {
                        currency = (String) balanceInfo.get("currency");
                    }

                    if (balanceInfo.containsKey("total_balance")) {
                        String totalBalanceStr = (String) balanceInfo.get("total_balance");
                        totalBalance = Double.parseDouble(totalBalanceStr);
                        availableBalance = totalBalance; // 可用余额等于总余额
                    }
                }
            }

            // 兼容旧格式（如果新格式解析失败）
            if (totalBalance == null) {
                if (response.containsKey("balance")) {
                    Object balanceObj = response.get("balance");
                    if (balanceObj instanceof Number) {
                        availableBalance = ((Number) balanceObj).doubleValue();
                        totalBalance = availableBalance;
                    }
                }

                if (response.containsKey("total_balance")) {
                    totalBalance = ((Number) response.get("total_balance")).doubleValue();
                }

                if (response.containsKey("available_balance")) {
                    availableBalance = ((Number) response.get("available_balance")).doubleValue();
                }

                if (response.containsKey("currency")) {
                    currency = (String) response.get("currency");
                }
            }

            // 检查是否需要警告
            boolean needWarning = false;
            if (balanceCheckEnabled && availableBalance != null &&
                availableBalance <= balanceWarningThreshold) {
                needWarning = true;

                // 发送管理员通知
                String warningMessage = String.format(
                    "DeepSeek账户余额不足，当前余额: %.2f %s，警告阈值: %.2f %s",
                    availableBalance, currency, balanceWarningThreshold, currency
                );
                adminNotificationService.notifyInsufficientBalance(warningMessage, null);
            }

            return BalanceInfo.builder()
                    .success(true)
                    .totalBalance(totalBalance)
                    .availableBalance(availableBalance)
                    .currency(currency)
                    .needWarning(needWarning)
                    .build();

        } catch (Exception e) {
            log.error("解析余额响应失败: {}", responseBody, e);
            return BalanceInfo.builder()
                    .success(false)
                    .errorMessage("解析余额响应失败: " + e.getMessage())
                    .build();
        }
    }
}
