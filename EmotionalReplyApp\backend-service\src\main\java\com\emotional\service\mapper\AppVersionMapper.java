package com.emotional.service.mapper;

import com.emotional.service.entity.AppVersion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应用版本 Mapper 接口
 */
@Mapper
public interface AppVersionMapper extends BaseMapper<AppVersion> {

    /**
     * 根据平台获取最新版本
     */
    @Select("SELECT * FROM app_versions WHERE platform = #{platform} AND status = 'published' ORDER BY version_code DESC LIMIT 1")
    AppVersion getLatestVersionByPlatform(@Param("platform") String platform);

    /**
     * 根据平台和状态获取版本列表
     */
    @Select("SELECT * FROM app_versions WHERE platform = #{platform} AND status = #{status} ORDER BY version_code DESC")
    List<AppVersion> getVersionsByPlatformAndStatus(@Param("platform") String platform, @Param("status") String status);

    /**
     * 获取所有已发布的版本（用于版本历史）
     */
    @Select("SELECT * FROM app_versions WHERE status = 'published' ORDER BY release_date DESC")
    List<AppVersion> getPublishedVersions();

    /**
     * 分页获取版本历史
     */
    @Select("SELECT * FROM app_versions WHERE status = 'published' ORDER BY release_date DESC LIMIT #{offset}, #{size}")
    List<AppVersion> getVersionHistoryWithPagination(@Param("offset") int offset, @Param("size") int size);

    /**
     * 获取已发布版本总数
     */
    @Select("SELECT COUNT(*) FROM app_versions WHERE status = 'published'")
    int getPublishedVersionCount();

    /**
     * 更新版本状态
     */
    @Update("UPDATE app_versions SET status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateVersionStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 根据版本名称和平台查找版本
     */
    @Select("SELECT * FROM app_versions WHERE version_name = #{versionName} AND platform = #{platform}")
    AppVersion getVersionByNameAndPlatform(@Param("versionName") String versionName, @Param("platform") String platform);

    /**
     * 获取指定平台的所有版本号（用于版本比较）
     */
    @Select("SELECT version_code FROM app_versions WHERE platform = #{platform} AND status = 'published' ORDER BY version_code DESC")
    List<Integer> getVersionCodesByPlatform(@Param("platform") String platform);

    /**
     * 检查版本是否存在
     */
    @Select("SELECT COUNT(*) FROM app_versions WHERE version_name = #{versionName} AND platform = #{platform}")
    int checkVersionExists(@Param("versionName") String versionName, @Param("platform") String platform);

    /**
     * 获取平台统计信息
     */
    @Select("SELECT platform, COUNT(*) as count FROM app_versions WHERE status = 'published' GROUP BY platform")
    List<Object> getPlatformStatistics();

    /**
     * 软删除版本（将状态设为deleted）
     */
    @Update("UPDATE app_versions SET status = 'deleted', update_time = NOW() WHERE id = #{id}")
    int softDeleteVersion(@Param("id") Long id);

    /**
     * 批量更新版本状态
     */
    @Update("UPDATE app_versions SET status = #{status}, update_time = NOW() WHERE id IN (#{ids})")
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") String status);
}
