/**
 * SuperSpider 用户相关样式
 * 包括用户认证、个人资料、下载历史等
 */

/* 用户菜单 */
.user-menu {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
}

.user-avatar i {
    font-size: 16px;
}

.dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: var(--radius);
    transition: background-color 0.2s ease;
}

.dropdown-toggle:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.dropdown-toggle i {
    margin-left: 4px;
    font-size: 12px;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 160px;
    background-color: white;
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    padding: 8px 0;
    margin-top: 8px;
    z-index: 100;
    display: none;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-menu a {
    display: block;
    padding: 8px 16px;
    color: var(--foreground);
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.dropdown-menu a:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.dropdown-menu .divider {
    height: 1px;
    background-color: var(--border);
    margin: 8px 0;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: none;
}

.modal.show {
    display: block;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 500px;
    background-color: white;
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.modal-large {
    max-width: 800px;
}

.modal-header {
    padding: 16px;
    border-bottom: 1px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-header .close-modal {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--muted-foreground);
    font-size: 1.25rem;
    padding: 4px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.modal-header .close-modal:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.modal-body {
    padding: 16px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-footer {
    margin-top: 16px;
    text-align: center;
    color: var(--muted-foreground);
}

.modal-footer a {
    color: var(--primary);
    text-decoration: none;
}

/* 表单 */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 14px;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(142, 68, 173, 0.2);
}

.form-checkbox {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.form-checkbox input {
    margin-right: 8px;
}

.submit-btn {
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius);
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.submit-btn:hover {
    background-color: var(--primary-hover);
}

/* 状态消息 */
.status-container {
    margin-bottom: 16px;
    padding: 8px 12px;
    border-radius: var(--radius);
    background-color: #f8f9fa;
}

.status-message {
    margin: 0;
    font-size: 14px;
}

.status-message.success {
    color: #28a745;
}

.status-message.error {
    color: #dc3545;
}

.status-message.info {
    color: #17a2b8;
}

/* 密码强度样式已移除 - 使用简化的密码校验 */

/* 下载历史记录 */
.downloads-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.download-stats {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    margin-bottom: 16px;
}

.stat-item {
    flex: 1;
    min-width: 100px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: var(--radius);
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary);
}

.stat-label {
    font-size: 14px;
    color: var(--muted-foreground);
    margin-top: 4px;
}

.filter-container {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: var(--radius);
    margin-bottom: 16px;
}

.filter-form {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: flex-end;
}

.filter-group {
    flex: 1;
    min-width: 150px;
}

.filter-actions {
    display: flex;
    gap: 8px;
}

.downloads-list-container {
    background-color: white;
    border-radius: var(--radius);
    border: 1px solid var(--border);
    overflow: hidden;
}

.downloads-table {
    width: 100%;
    border-collapse: collapse;
}

.downloads-table th,
.downloads-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--border);
}

.downloads-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.downloads-table tr:last-child td {
    border-bottom: none;
}

.download-title {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.download-actions {
    display: flex;
    gap: 8px;
}

.download-btn {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: var(--primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.download-btn:hover {
    background-color: var(--primary-hover);
}

.download-btn.disabled {
    background-color: var(--muted);
    cursor: not-allowed;
}

.status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.completed {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.failed {
    background-color: #f8d7da;
    color: #721c24;
}

.loading-container {
    padding: 32px;
    text-align: center;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.empty-container {
    padding: 32px;
    text-align: center;
    color: var(--muted-foreground);
}

.empty-container i {
    font-size: 48px;
    margin-bottom: 16px;
}

.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-top: 1px solid var(--border);
}

.pagination-info {
    font-size: 14px;
    color: var(--muted-foreground);
}

.pagination-links {
    display: flex;
    gap: 4px;
}

.page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: var(--radius);
    text-decoration: none;
    color: var(--foreground);
    transition: background-color 0.2s ease;
}

.page-link:hover {
    background-color: #f8f9fa;
}

.page-link.active {
    background-color: var(--primary);
    color: white;
}

.page-link.disabled {
    color: var(--muted-foreground);
    pointer-events: none;
}

.error-message {
    padding: 16px;
    text-align: center;
    color: #721c24;
}

.error-message i {
    font-size: 24px;
    margin-bottom: 8px;
}

/* 忘记密码样式 */
.forgot-password-link {
    margin-top: 8px;
    text-align: right;
}

.forgot-password-link a {
    color: var(--muted-foreground);
    text-decoration: none;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: color 0.2s ease;
}

.forgot-password-link a:hover {
    color: var(--primary);
}

.forgot-password-info {
    text-align: center;
    margin-bottom: 24px;
}

.forgot-password-info .info-icon {
    font-size: 48px;
    color: var(--primary);
    margin-bottom: 16px;
}

.forgot-password-info h3 {
    margin: 0 0 16px 0;
    color: var(--foreground);
}

.forgot-password-info p {
    margin: 8px 0;
    color: var(--muted-foreground);
    line-height: 1.5;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 24px;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: var(--radius);
    border: 1px solid var(--border);
}

.method-icon {
    font-size: 24px;
    color: var(--primary);
    margin-top: 4px;
}

.method-info {
    flex: 1;
}

.method-info h4 {
    margin: 0 0 8px 0;
    color: var(--foreground);
    font-size: 16px;
}

.method-info p {
    margin: 4px 0;
    color: var(--muted-foreground);
    font-size: 14px;
    line-height: 1.4;
}

.method-info .note {
    font-size: 12px;
    color: var(--muted-foreground);
    font-style: italic;
}

.security-tips {
    background-color: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: var(--radius);
    padding: 16px;
    margin-bottom: 24px;
}

.security-tips h4 {
    margin: 0 0 12px 0;
    color: var(--foreground);
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.security-tips ul {
    margin: 0;
    padding-left: 20px;
}

.security-tips li {
    margin: 8px 0;
    color: var(--muted-foreground);
    font-size: 14px;
    line-height: 1.4;
}

/* 登录方式切换 */
.login-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border);
}

.login-tabs .tab-btn {
    flex: 1;
    padding: 12px 16px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--muted-foreground);
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.login-tabs .tab-btn:hover {
    color: var(--foreground);
}

.login-tabs .tab-btn.active {
    color: var(--primary);
    border-bottom-color: var(--primary);
}

/* 登录表单 */
.login-form {
    display: none;
}

.login-form.active {
    display: block;
}

/* 验证码输入组 */
.sms-input-group {
    display: flex;
    gap: 8px;
}

.sms-input-group input {
    flex: 1;
}

/* 图片验证码输入组 */
.captcha-input-group {
    display: flex;
    gap: 8px;
    align-items: center;
}

.captcha-input-group input {
    flex: 1;
}

.captcha-container {
    display: flex;
    align-items: center;
    gap: 4px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 2px;
    background-color: var(--background);
}

#captcha-image {
    height: 36px;
    width: 100px;
    cursor: pointer;
    border-radius: 4px;
    display: block;
}

.refresh-captcha-btn {
    background: none;
    border: none;
    color: var(--muted-foreground);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.refresh-captcha-btn:hover {
    background-color: var(--muted);
    color: var(--foreground);
}

.refresh-captcha-btn i {
    font-size: 12px;
}

.sms-btn {
    padding: 8px 16px;
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius);
    font-size: 14px;
    cursor: pointer;
    white-space: nowrap;
    transition: background-color 0.2s ease;
}

.sms-btn:hover {
    background-color: var(--primary-hover);
}

.sms-btn:disabled {
    background-color: var(--muted);
    cursor: not-allowed;
}

.sms-btn.countdown {
    background-color: var(--muted);
    cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .contact-method {
        flex-direction: column;
        text-align: center;
    }

    .method-icon {
        margin-top: 0;
    }

    .sms-input-group {
        flex-direction: column;
    }

    .sms-btn {
        margin-top: 8px;
    }

    .captcha-input-group {
        flex-direction: column;
        align-items: stretch;
    }

    .captcha-container {
        margin-top: 8px;
        justify-content: center;
    }
}

/* 校验消息样式 */
.validation-message {
    margin-top: 4px;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.4;
    transition: all 0.2s ease;
}

.validation-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.validation-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.validation-message.warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.validation-message.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* 输入框状态样式 */
.form-group input.valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-group input.invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-group input.checking {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

/* 校验图标 */
.validation-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
}

.validation-icon.success {
    color: #28a745;
}

.validation-icon.error {
    color: #dc3545;
}

.validation-icon.checking {
    color: #ffc107;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* 密码提示样式 */
.password-hint {
    margin-top: 4px;
    padding: 6px 12px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}
