package com.emotional.service.service.impl;

import com.emotional.service.entity.User;
import com.emotional.service.entity.VipActivationCode;
import com.emotional.service.mapper.VipActivationCodeMapper;
import com.emotional.service.service.UserService;
import com.emotional.service.service.VipService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * VIP服务实现类
 */
@Slf4j
@Service
public class VipServiceImpl implements VipService {

    @Autowired
    private VipActivationCodeMapper activationCodeMapper;

    @Autowired
    private UserService userService;

    @Override
    @Transactional
    public Map<String, Object> activateVip(Long userId, String activationCode) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查激活码是否有效
            if (!isCodeValid(activationCode)) {
                result.put("success", false);
                result.put("message", "激活码无效或已被使用");
                return result;
            }

            // 获取激活码信息
            VipActivationCode code = activationCodeMapper.getByCode(activationCode);
            if (code == null) {
                result.put("success", false);
                result.put("message", "激活码不存在");
                return result;
            }

            // 获取用户信息
            User user = userService.getUserById(userId);
            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }

            // 计算VIP过期时间
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expireTime = now.plusDays(code.getDurationDays());

            // 如果用户已经是VIP，在现有过期时间基础上延长
            if (user.getIsVip() != null && user.getIsVip() == 1 && user.getVipExpireTime() != null) {
                LocalDateTime currentExpire = user.getVipExpireTime();
                if (currentExpire.isAfter(now)) {
                    expireTime = currentExpire.plusDays(code.getDurationDays());
                }
            }

            // 更新用户VIP状态
            user.setIsVip(1);
            user.setVipExpireTime(expireTime);
            userService.updateById(user);

            // 标记激活码为已使用
            int updated = activationCodeMapper.useCode(activationCode, userId);
            if (updated == 0) {
                throw new RuntimeException("激活码使用失败，可能已被其他用户使用");
            }

            log.info("用户VIP激活成功: userId={}, code={}, expireTime={}", userId, activationCode, expireTime);

            result.put("success", true);
            result.put("message", "VIP激活成功");
            Map<String, Object> data = new HashMap<>();
            data.put("expireTime", expireTime.toString());
            data.put("validDays", code.getDurationDays());
            result.put("data", data);

            return result;

        } catch (Exception e) {
            log.error("VIP激活失败: userId={}, code={}", userId, activationCode, e);
            result.put("success", false);
            result.put("message", "激活失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public boolean isCodeValid(String code) {
        try {
            int count = activationCodeMapper.checkCodeAvailable(code);
            return count > 0;
        } catch (Exception e) {
            log.error("检查激活码有效性失败: code={}", code, e);
            return false;
        }
    }

    @Override
    public String generateActivationCode(String type, Integer validDays, Long creatorId, String remark) {
        try {
            // 生成16位激活码
            String code = generateRandomCode();
            
            // 创建激活码记录
            VipActivationCode activationCode = new VipActivationCode();
            activationCode.setCode(code);
            activationCode.setCodeType(type);
            activationCode.setDurationDays(validDays);
            activationCode.setStatus(0); // 0-未使用
            activationCode.setCreatedBy(creatorId);
            activationCode.setRemark(remark);
            activationCode.setCreatedTime(LocalDateTime.now());
            activationCode.setUpdatedTime(LocalDateTime.now());

            // 激活码永久有效，不设置过期时间

            activationCodeMapper.insert(activationCode);
            
            log.info("生成激活码成功: code={}, type={}, validDays={}", code, type, validDays);
            return code;

        } catch (Exception e) {
            log.error("生成激活码失败", e);
            throw new RuntimeException("生成激活码失败", e);
        }
    }

    @Override
    public LocalDateTime getUserVipExpireTime(Long userId) {
        try {
            User user = userService.getUserById(userId);
            if (user != null && user.getIsVip() != null && user.getIsVip() == 1) {
                return user.getVipExpireTime();
            }
            return null;
        } catch (Exception e) {
            log.error("获取用户VIP过期时间失败: userId={}", userId, e);
            return null;
        }
    }

    @Override
    public boolean isUserVip(Long userId) {
        try {
            User user = userService.getUserById(userId);
            if (user == null || user.getIsVip() == null || user.getIsVip() != 1) {
                return false;
            }
            
            // 检查VIP是否过期
            LocalDateTime expireTime = user.getVipExpireTime();
            if (expireTime != null && expireTime.isBefore(LocalDateTime.now())) {
                // VIP已过期，更新用户状态
                user.setIsVip(0);
                userService.updateById(user);
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("检查用户VIP状态失败: userId={}", userId, e);
            return false;
        }
    }

    /**
     * 生成随机激活码
     */
    private String generateRandomCode() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        
        for (int i = 0; i < 16; i++) {
            code.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return code.toString();
    }
}
