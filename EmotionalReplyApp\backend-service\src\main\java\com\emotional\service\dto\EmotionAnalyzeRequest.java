package com.emotional.service.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 情感分析请求DTO
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
public class EmotionAnalyzeRequest {
    
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 要分析的消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    @Size(max = 1000, message = "消息内容不能超过1000个字符")
    private String message;

    /**
     * 指定回复风格（可选）
     */
    private List<String> replyStyles;
    
    /**
     * 是否保存历史记录
     */
    private Boolean saveHistory = true;
    
    /**
     * 客户端信息（可选）
     */
    private String clientInfo;
}
