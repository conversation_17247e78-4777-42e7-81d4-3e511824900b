package com.emotional.service.controller;

import com.emotional.service.annotation.RequireAdmin;
import com.emotional.service.common.Result;
import com.emotional.service.entity.User;
import com.emotional.service.service.UserService;
import com.emotional.service.utils.AdminUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员控制器
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@RestController
@RequestMapping("/api/admin")
@Tag(name = "管理员接口", description = "管理员相关功能")
@Slf4j
public class AdminController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 获取系统统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取系统统计", description = "获取系统基本统计信息")
    @RequireAdmin("系统统计查看")
    public Result<Map<String, Object>> getSystemStats() {
        log.info("管理员查看系统统计");
        
        Map<String, Object> stats = new HashMap<>();
        
        // 用户统计
        long totalUsers = userService.count();
        long vipUsers = userService.lambdaQuery()
                .eq(User::getIsVip, 1)
                .eq(User::getDeleted, 0)
                .count();
        long adminUsers = userService.lambdaQuery()
                .eq(User::getIsAdmin, 1)
                .eq(User::getDeleted, 0)
                .count();
        
        stats.put("totalUsers", totalUsers);
        stats.put("vipUsers", vipUsers);
        stats.put("adminUsers", adminUsers);
        stats.put("normalUsers", totalUsers - vipUsers - adminUsers);
        
        return Result.success("获取统计信息成功", stats);
    }
    
    /**
     * 获取所有用户列表
     */
    @GetMapping("/users")
    @Operation(summary = "获取用户列表", description = "获取所有用户信息")
    @RequireAdmin("用户管理")
    public Result<List<User>> getAllUsers(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.info("管理员查看用户列表: page={}, size={}", page, size);
        
        // 这里应该使用分页查询，简化示例直接返回所有用户
        List<User> users = userService.lambdaQuery()
                .eq(User::getDeleted, 0)
                .orderByDesc(User::getCreateTime)
                .list();
        
        // 清除敏感信息
        users.forEach(user -> user.setPassword(null));
        
        return Result.success("获取用户列表成功", users);
    }
    
    /**
     * 设置用户VIP状态
     */
    @PostMapping("/users/{userId}/vip")
    @Operation(summary = "设置VIP状态", description = "设置用户VIP状态")
    @RequireAdmin("用户VIP管理")
    public Result<String> setUserVip(
            @PathVariable Long userId,
            @RequestParam boolean isVip) {
        
        log.info("管理员设置用户VIP状态: userId={}, isVip={}", userId, isVip);
        
        User user = userService.getById(userId);
        if (user == null) {
            return Result.error("用户不存在");
        }
        
        user.setIsVip(isVip ? 1 : 0);
        if (isVip) {
            // 设置VIP配额
            user.setDailyQuota(100);
        } else {
            // 恢复普通用户配额
            user.setDailyQuota(10);
        }
        
        boolean success = userService.updateById(user);
        if (success) {
            String action = isVip ? "设置为VIP" : "取消VIP";
            log.info("管理员{}用户成功: userId={}, username={}", action, userId, user.getUsername());
            return Result.success(action + "成功");
        } else {
            return Result.error("操作失败");
        }
    }
    
    /**
     * 设置用户管理员状态
     */
    @PostMapping("/users/{userId}/admin")
    @Operation(summary = "设置管理员状态", description = "设置用户管理员状态")
    @RequireAdmin(level = RequireAdmin.AdminLevel.SUPER_ADMIN, value = "管理员权限管理")
    public Result<String> setUserAdmin(
            @PathVariable Long userId,
            @RequestParam boolean isAdmin) {
        
        log.info("超级管理员设置用户管理员状态: userId={}, isAdmin={}", userId, isAdmin);
        
        User user = userService.getById(userId);
        if (user == null) {
            return Result.error("用户不存在");
        }
        
        user.setIsAdmin(isAdmin ? 1 : 0);
        
        boolean success = userService.updateById(user);
        if (success) {
            String action = isAdmin ? "设置为管理员" : "取消管理员";
            log.info("超级管理员{}用户成功: userId={}, username={}", action, userId, user.getUsername());
            return Result.success(action + "成功");
        } else {
            return Result.error("操作失败");
        }
    }
    
    /**
     * 禁用/启用用户
     */
    @PostMapping("/users/{userId}/status")
    @Operation(summary = "设置用户状态", description = "禁用或启用用户")
    @RequireAdmin("用户状态管理")
    public Result<String> setUserStatus(
            @PathVariable Long userId,
            @RequestParam int status) {
        
        log.info("管理员设置用户状态: userId={}, status={}", userId, status);
        
        User user = userService.getById(userId);
        if (user == null) {
            return Result.error("用户不存在");
        }
        
        // 不能禁用管理员账号
        if (AdminUtils.isAdmin(user) && status != 0) {
            return Result.error("不能禁用管理员账号");
        }
        
        user.setStatus(status);
        
        boolean success = userService.updateById(user);
        if (success) {
            String action = status == 0 ? "启用" : "禁用";
            log.info("管理员{}用户成功: userId={}, username={}", action, userId, user.getUsername());
            return Result.success(action + "用户成功");
        } else {
            return Result.error("操作失败");
        }
    }
    
    /**
     * 重置用户密码
     */
    @PostMapping("/users/{userId}/reset-password")
    @Operation(summary = "重置密码", description = "重置用户密码")
    @RequireAdmin("密码重置")
    public Result<String> resetUserPassword(@PathVariable Long userId) {
        
        log.info("管理员重置用户密码: userId={}", userId);
        
        User user = userService.getById(userId);
        if (user == null) {
            return Result.error("用户不存在");
        }
        
        // 生成临时密码
        String tempPassword = "temp123456";
        user.setPassword(userService.encodePassword(tempPassword));
        
        boolean success = userService.updateById(user);
        if (success) {
            log.info("管理员重置用户密码成功: userId={}, username={}", userId, user.getUsername());
            return Result.success("密码重置成功，临时密码：" + tempPassword);
        } else {
            return Result.error("密码重置失败");
        }
    }
    
    /**
     * 检查当前用户权限
     */
    @GetMapping("/check-permission")
    @Operation(summary = "检查权限", description = "检查当前用户的管理员权限")
    public Result<Map<String, Object>> checkPermission() {
        // 这个接口不需要@RequireAdmin注解，用于前端检查权限

        Map<String, Object> permission = new HashMap<>();
        permission.put("isAdmin", false);
        permission.put("isSuperAdmin", false);
        permission.put("message", "需要登录后才能检查权限");

        return Result.success("权限检查完成", permission);
    }

    /**
     * 测试管理员权限（普通管理员）
     */
    @GetMapping("/test-admin")
    @Operation(summary = "测试管理员权限", description = "测试普通管理员权限")
    @RequireAdmin("测试功能")
    public Result<String> testAdmin() {
        return Result.success("恭喜！您有管理员权限");
    }

    /**
     * 测试超级管理员权限
     */
    @GetMapping("/test-super-admin")
    @Operation(summary = "测试超级管理员权限", description = "测试超级管理员权限")
    @RequireAdmin(level = RequireAdmin.AdminLevel.SUPER_ADMIN, value = "超级管理员测试功能")
    public Result<String> testSuperAdmin() {
        return Result.success("恭喜！您有超级管理员权限");
    }
}
