package com.emotional.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.emotional.service.entity.ReplyHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 回复历史 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface ReplyHistoryMapper extends BaseMapper<ReplyHistory> {
    
    /**
     * 获取用户在指定时间范围内的使用次数
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 使用次数
     */
    @Select("SELECT COUNT(*) FROM reply_history WHERE user_id = #{userId} " +
            "AND create_time BETWEEN #{startTime} AND #{endTime} AND deleted = 0")
    int countByUserIdAndTimeRange(@Param("userId") Long userId, 
                                 @Param("startTime") LocalDateTime startTime,
                                 @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取用户情感分布统计
     * 
     * @param userId 用户ID
     * @return 情感分布统计
     */
    @Select("SELECT emotion_result as emotion, COUNT(*) as count " +
            "FROM reply_history WHERE user_id = #{userId} AND deleted = 0 " +
            "GROUP BY emotion_result ORDER BY count DESC")
    List<Map<String, Object>> getEmotionDistribution(@Param("userId") Long userId);
    
    /**
     * 获取用户最近N天的使用统计
     * 
     * @param userId 用户ID
     * @param days 天数
     * @return 使用统计
     */
    @Select("SELECT DATE(create_time) as date, COUNT(*) as count " +
            "FROM reply_history WHERE user_id = #{userId} AND deleted = 0 " +
            "AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY) " +
            "GROUP BY DATE(create_time) ORDER BY date")
    List<Map<String, Object>> getUsageStatistics(@Param("userId") Long userId, 
                                                @Param("days") int days);
    
    /**
     * 获取用户回复风格偏好统计
     * 
     * @param userId 用户ID
     * @return 风格偏好统计
     */
    @Select("SELECT selected_style as style, COUNT(*) as count " +
            "FROM reply_history WHERE user_id = #{userId} AND deleted = 0 " +
            "AND selected_style IS NOT NULL " +
            "GROUP BY selected_style ORDER BY count DESC")
    List<Map<String, Object>> getStylePreferences(@Param("userId") Long userId);
}
