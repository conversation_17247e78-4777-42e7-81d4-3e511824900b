package com.emotional.service.dto;

import lombok.Data;

import java.util.List;

/**
 * 情感分析响应DTO
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
public class EmotionAnalyzeResponse {
    
    /**
     * 历史记录ID
     */
    private Long historyId;
    
    /**
     * 原始消息
     */
    private String originalMessage;
    
    /**
     * 情感分析结果
     */
    private EmotionResult emotionResult;
    
    /**
     * 回复建议列表
     */
    private List<ReplyOption> replyOptions;
    
    /**
     * 处理耗时（毫秒）
     */
    private Long processTime;
    
    /**
     * 情感分析结果
     */
    @Data
    public static class EmotionResult {
        /**
         * 主要情感
         */
        private String emotion;
        
        /**
         * 置信度（0-100）
         */
        private Double confidence;
        
        /**
         * 情感强度（1-5）
         */
        private Integer intensity;
        
        /**
         * 详细情感分布
         */
        private List<EmotionDetail> details;
    }
    
    /**
     * 情感详情
     */
    @Data
    public static class EmotionDetail {
        /**
         * 情感类型
         */
        private String emotion;
        
        /**
         * 概率
         */
        private Double probability;
    }
    
    /**
     * 回复选项
     */
    @Data
    public static class ReplyOption {
        /**
         * 回复内容
         */
        private String content;
        
        /**
         * 回复风格
         */
        private String style;
        
        /**
         * 风格名称
         */
        private String styleName;
        
        /**
         * 推荐度（1-5）
         */
        private Integer recommendation;
        
        /**
         * 生成置信度
         */
        private Double confidence;
    }
}
