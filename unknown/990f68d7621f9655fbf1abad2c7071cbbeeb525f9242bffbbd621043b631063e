name: build image and push

on:
  push:
    tags:
      - '*'

jobs:
  docker:
    runs-on: ubuntu-latest
    environment: release
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Install dependencies
        run: pip install requests tqdm

      - name: Download models
        run: python scripts/download_model.py --models all

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ${{ vars.IMAGE_NAME }}:latest
            ${{ vars.IMAGE_NAME }}:${{ github.ref_name }}