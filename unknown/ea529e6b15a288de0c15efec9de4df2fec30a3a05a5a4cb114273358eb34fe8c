package com.emotional.service;

import com.emotional.service.entity.User;
import com.emotional.service.utils.AdminUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 管理员工具类测试
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@SpringBootTest
public class AdminUtilsTest {
    
    @Test
    public void testIsAdmin() {
        // 测试普通用户
        User normalUser = new User();
        normalUser.setId(1L);
        normalUser.setUsername("testuser");
        normalUser.setIsAdmin(0);
        normalUser.setStatus(0);
        normalUser.setDeleted(0);
        
        assertFalse(AdminUtils.isAdmin(normalUser), "普通用户应该不是管理员");
        
        // 测试管理员用户
        User adminUser = new User();
        adminUser.setId(2L);
        adminUser.setUsername("admin");
        adminUser.setIsAdmin(1);
        adminUser.setStatus(0);
        adminUser.setDeleted(0);
        
        assertTrue(AdminUtils.isAdmin(adminUser), "管理员用户应该是管理员");
        
        // 测试被禁用的管理员
        User disabledAdmin = new User();
        disabledAdmin.setId(3L);
        disabledAdmin.setUsername("disabledadmin");
        disabledAdmin.setIsAdmin(1);
        disabledAdmin.setStatus(1); // 被禁用
        disabledAdmin.setDeleted(0);
        
        assertFalse(AdminUtils.isAdmin(disabledAdmin), "被禁用的管理员应该不被认为是管理员");
        
        // 测试被删除的管理员
        User deletedAdmin = new User();
        deletedAdmin.setId(4L);
        deletedAdmin.setUsername("deletedadmin");
        deletedAdmin.setIsAdmin(1);
        deletedAdmin.setStatus(0);
        deletedAdmin.setDeleted(1); // 被删除
        
        assertFalse(AdminUtils.isAdmin(deletedAdmin), "被删除的管理员应该不被认为是管理员");
    }
    
    @Test
    public void testIsSuperAdmin() {
        // 测试普通管理员
        User normalAdmin = new User();
        normalAdmin.setId(1L);
        normalAdmin.setUsername("normaladmin");
        normalAdmin.setIsAdmin(1);
        normalAdmin.setStatus(0);
        normalAdmin.setDeleted(0);
        
        assertFalse(AdminUtils.isSuperAdmin(normalAdmin), "普通管理员应该不是超级管理员");
        
        // 测试超级管理员
        User superAdmin = new User();
        superAdmin.setId(2L);
        superAdmin.setUsername("admin");
        superAdmin.setIsAdmin(1);
        superAdmin.setStatus(0);
        superAdmin.setDeleted(0);
        
        assertTrue(AdminUtils.isSuperAdmin(superAdmin), "用户名为admin的管理员应该是超级管理员");
        
        // 测试另一个超级管理员用户名
        User superAdmin2 = new User();
        superAdmin2.setId(3L);
        superAdmin2.setUsername("superadmin");
        superAdmin2.setIsAdmin(1);
        superAdmin2.setStatus(0);
        superAdmin2.setDeleted(0);
        
        assertTrue(AdminUtils.isSuperAdmin(superAdmin2), "用户名为superadmin的管理员应该是超级管理员");
    }
    
    @Test
    public void testGetUserRoleDescription() {
        // 测试普通用户
        User normalUser = new User();
        normalUser.setUsername("testuser");
        normalUser.setIsAdmin(0);
        normalUser.setIsVip(0);
        
        assertEquals("普通用户", AdminUtils.getUserRoleDescription(normalUser));
        
        // 测试VIP用户
        User vipUser = new User();
        vipUser.setUsername("vipuser");
        vipUser.setIsAdmin(0);
        vipUser.setIsVip(1);
        
        assertEquals("VIP用户", AdminUtils.getUserRoleDescription(vipUser));
        
        // 测试管理员
        User adminUser = new User();
        adminUser.setUsername("normaladmin");
        adminUser.setIsAdmin(1);
        adminUser.setIsVip(0);
        adminUser.setStatus(0);
        adminUser.setDeleted(0);
        
        assertEquals("管理员", AdminUtils.getUserRoleDescription(adminUser));
        
        // 测试超级管理员
        User superAdmin = new User();
        superAdmin.setUsername("admin");
        superAdmin.setIsAdmin(1);
        superAdmin.setIsVip(0);
        superAdmin.setStatus(0);
        superAdmin.setDeleted(0);
        
        assertEquals("超级管理员", AdminUtils.getUserRoleDescription(superAdmin));
    }
    
    @Test
    public void testRequireAdmin() {
        // 测试普通用户
        User normalUser = new User();
        normalUser.setUsername("testuser");
        normalUser.setIsAdmin(0);
        
        assertThrows(RuntimeException.class, () -> {
            AdminUtils.requireAdmin(normalUser);
        }, "普通用户调用requireAdmin应该抛出异常");
        
        // 测试管理员
        User adminUser = new User();
        adminUser.setUsername("admin");
        adminUser.setIsAdmin(1);
        adminUser.setStatus(0);
        adminUser.setDeleted(0);
        
        assertDoesNotThrow(() -> {
            AdminUtils.requireAdmin(adminUser);
        }, "管理员调用requireAdmin不应该抛出异常");
    }
    
    @Test
    public void testHasAdminAccess() {
        // 测试普通用户
        User normalUser = new User();
        normalUser.setUsername("testuser");
        normalUser.setIsAdmin(0);
        
        assertFalse(AdminUtils.hasAdminAccess(normalUser, "测试功能"), 
                   "普通用户应该没有管理员访问权限");
        
        // 测试管理员
        User adminUser = new User();
        adminUser.setUsername("admin");
        adminUser.setIsAdmin(1);
        adminUser.setStatus(0);
        adminUser.setDeleted(0);
        
        assertTrue(AdminUtils.hasAdminAccess(adminUser, "测试功能"), 
                  "管理员应该有管理员访问权限");
    }
    
    @Test
    public void testNullUser() {
        // 测试null用户
        assertFalse(AdminUtils.isAdmin(null), "null用户应该不是管理员");
        assertFalse(AdminUtils.isSuperAdmin(null), "null用户应该不是超级管理员");
        assertEquals("未知用户", AdminUtils.getUserRoleDescription(null));
        assertFalse(AdminUtils.hasAdminAccess(null, "测试功能"));
        
        assertThrows(RuntimeException.class, () -> {
            AdminUtils.requireAdmin(null);
        }, "null用户调用requireAdmin应该抛出异常");
    }
}
