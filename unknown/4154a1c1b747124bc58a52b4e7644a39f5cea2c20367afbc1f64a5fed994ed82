
from flask import request, jsonify
import re

class SecurityMiddleware:
    """安全中间件，过滤响应中的敏感信息"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        app.after_request(self.filter_response)
    
    def filter_response(self, response):
        """过滤响应中的敏感信息"""
        if response.content_type and 'application/json' in response.content_type:
            try:
                # 获取响应数据
                data = response.get_data(as_text=True)
                
                # 过滤IP地址
                data = re.sub(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b', '[IP_HIDDEN]', data)
                
                # 过滤localhost
                data = re.sub(r'\blocalhost\b', '[LOCAL]', data, flags=re.IGNORECASE)
                
                # 过滤端口号
                data = re.sub(r':(80|443|8080|8000|5000|3000|9000)\b', ':[PORT]', data)
                
                # 过滤完整URL
                data = re.sub(r'https?://[^\s/$.?#].[^\s]*', '[URL_HIDDEN]', data)
                
                # 更新响应数据
                response.set_data(data)
                
            except Exception:
                # 如果处理失败，返回原响应
                pass
        
        return response
