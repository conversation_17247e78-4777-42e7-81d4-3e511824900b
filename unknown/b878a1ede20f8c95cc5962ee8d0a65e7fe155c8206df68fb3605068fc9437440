package com.emotional.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.emotional.service.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 用户 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 更新用户今日使用次数
     * 
     * @param userId 用户ID
     * @param increment 增量
     * @return 影响行数
     */
    @Update("UPDATE users SET today_used = today_used + #{increment}, " +
            "total_used = total_used + #{increment} WHERE id = #{userId}")
    int updateUsageCount(@Param("userId") Long userId, @Param("increment") int increment);
    
    /**
     * 重置所有用户的今日使用次数（定时任务使用）
     * 
     * @return 影响行数
     */
    @Update("UPDATE users SET today_used = 0")
    int resetTodayUsage();
}
