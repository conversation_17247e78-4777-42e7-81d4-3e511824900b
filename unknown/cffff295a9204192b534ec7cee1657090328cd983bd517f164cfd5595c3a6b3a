package com.emotional.service.annotation;

import java.lang.annotation.*;

/**
 * 管理员权限注解
 * 用于标记需要管理员权限才能访问的方法或类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequireAdmin {
    
    /**
     * 权限级别
     * 
     * @return 权限级别
     */
    AdminLevel level() default AdminLevel.ADMIN;
    
    /**
     * 功能描述（用于日志记录）
     * 
     * @return 功能描述
     */
    String value() default "";
    
    /**
     * 管理员权限级别枚举
     */
    enum AdminLevel {
        /**
         * 普通管理员
         */
        ADMIN,
        
        /**
         * 超级管理员
         */
        SUPER_ADMIN
    }
}
