# ===== 可选依赖包 =====
# 这些依赖包是可选的，根据需要安装

# ===== 高级网页截图功能 =====
# 如果需要使用 CSDN 文章截图功能，请安装以下依赖：
# playwright==1.40.0
# 安装后还需要运行: playwright install chromium

# ===== 任务调度 =====
# 如果需要使用高级任务调度功能，可以安装：
# APScheduler==3.10.4

# ===== 生产环境部署 =====
# 生产环境推荐使用以下 WSGI 服务器：
# gunicorn==21.2.0  # Linux/macOS
# waitress==2.1.2   # Windows

# ===== 性能监控 =====
# 如果需要性能监控，可以安装：
# psutil==5.9.6
# memory-profiler==0.61.0

# ===== 日志增强 =====
# 如果需要更好的日志功能，可以安装：
# colorlog==6.7.0
# loguru==0.7.2

# ===== 缓存系统 =====
# 如果需要 Redis 缓存，可以安装：
# redis==5.0.1
# flask-caching==2.1.0

# ===== 邮件发送增强 =====
# 如果需要更强大的邮件功能，可以安装：
# flask-mail==0.9.1

# ===== 数据导出 =====
# 如果需要数据导出功能，可以安装：
# pandas==2.1.3
# openpyxl==3.1.2

# ===== 安全增强 =====
# 如果需要更强的安全功能，可以安装：
# flask-limiter==3.5.0
# flask-cors==4.0.0

# ===== 开发工具 =====
# 开发环境推荐安装：
# pytest==7.4.3
# pytest-flask==1.3.0
# black==23.11.0
# flake8==6.1.0
