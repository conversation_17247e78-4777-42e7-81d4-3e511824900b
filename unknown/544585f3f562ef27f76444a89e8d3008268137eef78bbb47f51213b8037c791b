[Unit]
Description=Emotional Reply Service
After=network.target mysql.service redis.service

[Service]
Type=simple
User=emotional-reply
Group=emotional-reply
WorkingDirectory=/opt/emotional-reply
ExecStart=/usr/bin/java -jar \
    -Xms512m \
    -Xmx1024m \
    -Dspring.profiles.active=prod \
    -Dspring.config.location=classpath:/application.yml,file:/opt/emotional-reply/application-prod.yml \
    -Dlogging.file.name=/var/log/emotional-reply/application.log \
    /opt/emotional-reply/emotional-reply-service-latest.jar

# 重启策略
Restart=always
RestartSec=10

# 环境变量
Environment=JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
Environment=SPRING_PROFILES_ACTIVE=prod

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=emotional-reply

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/emotional-reply /opt/emotional-reply

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
