package com.emotionalreply.service;

import com.emotionalreply.entity.AppVersion;
import com.emotionalreply.mapper.AppVersionMapper;
import com.emotionalreply.mapper.SystemConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 版本管理服务
 */
@Service
public class VersionService {

    @Autowired
    private AppVersionMapper appVersionMapper;
    
    @Autowired
    private SystemConfigMapper systemConfigMapper;

    /**
     * 检查应用更新
     */
    public Map<String, Object> checkUpdate(String currentVersion, String platform) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取最新版本信息
        AppVersion latestVersion = appVersionMapper.getLatestVersion(platform);
        
        if (latestVersion == null) {
            // 如果没有版本记录，从系统配置获取
            String configVersion = systemConfigMapper.getConfigValue("app_version");
            result.put("hasUpdate", false);
            result.put("currentVersion", currentVersion);
            result.put("latestVersion", configVersion != null ? configVersion : "1.0.0");
            result.put("message", "已是最新版本");
            return result;
        }
        
        // 比较版本号
        boolean hasUpdate = compareVersions(currentVersion, latestVersion.getVersionName()) < 0;
        
        result.put("hasUpdate", hasUpdate);
        result.put("currentVersion", currentVersion);
        result.put("latestVersion", latestVersion.getVersionName());
        
        if (hasUpdate) {
            result.put("message", "发现新版本");
            result.put("versionInfo", buildVersionInfo(latestVersion));
        } else {
            result.put("message", "已是最新版本");
        }
        
        return result;
    }

    /**
     * 获取最新版本信息
     */
    public Map<String, Object> getLatestVersion(String platform) {
        AppVersion latestVersion = appVersionMapper.getLatestVersion(platform);
        
        if (latestVersion == null) {
            // 返回默认版本信息
            Map<String, Object> defaultVersion = new HashMap<>();
            defaultVersion.put("versionName", "1.0.0");
            defaultVersion.put("versionCode", 100);
            defaultVersion.put("platform", platform);
            defaultVersion.put("releaseDate", LocalDateTime.now());
            defaultVersion.put("updateContent", "当前版本");
            return defaultVersion;
        }
        
        return buildVersionInfo(latestVersion);
    }

    /**
     * 获取版本更新历史
     */
    public Map<String, Object> getVersionHistory(int page, int size) {
        Map<String, Object> result = new HashMap<>();
        
        // 计算偏移量
        int offset = (page - 1) * size;
        
        // 获取版本历史列表
        List<AppVersion> versions = appVersionMapper.getVersionHistory(offset, size);
        
        // 获取总数
        int total = appVersionMapper.getVersionCount();
        
        result.put("list", versions);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("totalPages", (int) Math.ceil((double) total / size));
        
        return result;
    }

    /**
     * 记录用户更新行为
     */
    public void logUpdateAction(Map<String, Object> logData) {
        // 这里可以记录用户的更新行为，比如：
        // - 用户点击了检查更新
        // - 用户选择了立即更新
        // - 用户选择了稍后更新
        // 可以用于统计分析
        
        // 暂时只记录日志，后续可以存储到数据库
        System.out.println("用户更新行为记录: " + logData);
    }

    /**
     * 构建版本信息
     */
    private Map<String, Object> buildVersionInfo(AppVersion version) {
        Map<String, Object> info = new HashMap<>();
        info.put("versionName", version.getVersionName());
        info.put("versionCode", version.getVersionCode());
        info.put("platform", version.getPlatform());
        info.put("releaseDate", version.getReleaseDate());
        info.put("updateContent", version.getUpdateContent());
        info.put("downloadUrl", version.getDownloadUrl());
        info.put("fileSize", version.getFileSize());
        info.put("isForceUpdate", version.getIsForceUpdate());
        info.put("minSupportVersion", version.getMinSupportVersion());
        return info;
    }

    /**
     * 比较版本号
     * @param version1 版本1
     * @param version2 版本2
     * @return 负数表示version1 < version2，0表示相等，正数表示version1 > version2
     */
    private int compareVersions(String version1, String version2) {
        if (version1 == null || version2 == null) {
            return 0;
        }
        
        String[] v1Parts = version1.split("\\.");
        String[] v2Parts = version2.split("\\.");
        
        int maxLength = Math.max(v1Parts.length, v2Parts.length);
        
        for (int i = 0; i < maxLength; i++) {
            int v1Part = i < v1Parts.length ? Integer.parseInt(v1Parts[i]) : 0;
            int v2Part = i < v2Parts.length ? Integer.parseInt(v2Parts[i]) : 0;
            
            if (v1Part != v2Part) {
                return Integer.compare(v1Part, v2Part);
            }
        }
        
        return 0;
    }
}
