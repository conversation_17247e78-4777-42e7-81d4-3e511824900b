package com.emotional.service.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 通知频率控制服务测试
 */
@ExtendWith(MockitoExtension.class)
class NotificationThrottleServiceTest {
    
    @Mock
    private RedisTemplate<String, String> redisTemplate;
    
    @Mock
    private ValueOperations<String, String> valueOperations;
    
    private NotificationThrottleService throttleService;
    
    @BeforeEach
    void setUp() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        
        throttleService = new NotificationThrottleService(redisTemplate);
        
        // 设置测试配置
        ReflectionTestUtils.setField(throttleService, "throttleEnabled", true);
        ReflectionTestUtils.setField(throttleService, "apiKeyErrorThrottleSeconds", 86400L);
        ReflectionTestUtils.setField(throttleService, "balanceErrorThrottleSeconds", 3600L);
        ReflectionTestUtils.setField(throttleService, "serviceErrorThrottleSeconds", 1800L);
    }
    
    @Test
    void testCanSendApiKeyErrorNotification_FirstTime() {
        // 模拟第一次发送，Redis中没有记录
        when(valueOperations.get(anyString())).thenReturn(null);
        
        boolean canSend = throttleService.canSendApiKeyErrorNotification("INVALID_KEY");
        
        assertTrue(canSend);
        verify(valueOperations).set(eq("notification:api_key_error:INVALID_KEY"), anyString(), eq(86400L), eq(TimeUnit.SECONDS));
    }
    
    @Test
    void testCanSendApiKeyErrorNotification_Throttled() {
        // 模拟已有记录，被节流
        when(valueOperations.get(anyString())).thenReturn("2024-01-15 10:00:00");
        
        boolean canSend = throttleService.canSendApiKeyErrorNotification("INVALID_KEY");
        
        assertFalse(canSend);
        verify(valueOperations, never()).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
    }
    
    @Test
    void testCanSendBalanceErrorNotification_FirstTime() {
        when(valueOperations.get(anyString())).thenReturn(null);
        
        boolean canSend = throttleService.canSendBalanceErrorNotification();
        
        assertTrue(canSend);
        verify(valueOperations).set(eq("notification:balance_error"), anyString(), eq(3600L), eq(TimeUnit.SECONDS));
    }
    
    @Test
    void testCanSendBalanceErrorNotification_Throttled() {
        when(valueOperations.get(anyString())).thenReturn("2024-01-15 10:00:00");
        
        boolean canSend = throttleService.canSendBalanceErrorNotification();
        
        assertFalse(canSend);
        verify(valueOperations, never()).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
    }
    
    @Test
    void testCanSendServiceErrorNotification_FirstTime() {
        when(valueOperations.get(anyString())).thenReturn(null);
        
        boolean canSend = throttleService.canSendServiceErrorNotification("CONNECTION_ERROR");
        
        assertTrue(canSend);
        verify(valueOperations).set(eq("notification:service_error:CONNECTION_ERROR"), anyString(), eq(1800L), eq(TimeUnit.SECONDS));
    }
    
    @Test
    void testThrottleDisabled() {
        // 禁用节流
        ReflectionTestUtils.setField(throttleService, "throttleEnabled", false);
        
        boolean canSendApiKey = throttleService.canSendApiKeyErrorNotification("INVALID_KEY");
        boolean canSendBalance = throttleService.canSendBalanceErrorNotification();
        boolean canSendService = throttleService.canSendServiceErrorNotification("ERROR");
        
        assertTrue(canSendApiKey);
        assertTrue(canSendBalance);
        assertTrue(canSendService);
        
        // 验证没有调用Redis
        verify(valueOperations, never()).get(anyString());
        verify(valueOperations, never()).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
    }
    
    @Test
    void testRedisException_AllowsSending() {
        // 模拟Redis异常
        when(valueOperations.get(anyString())).thenThrow(new RuntimeException("Redis connection failed"));
        
        boolean canSend = throttleService.canSendApiKeyErrorNotification("INVALID_KEY");
        
        // 当Redis出错时，为了确保重要通知能发送，应该返回true
        assertTrue(canSend);
    }
}
