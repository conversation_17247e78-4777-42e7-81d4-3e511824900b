package com.emotional.service.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 用户登录请求DTO
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@Schema(description = "用户登录请求")
public class UserLoginRequest {
    
    @Schema(description = "用户名", example = "testuser")
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    private String username;
    
    @Schema(description = "密码", example = "test123")
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    private String password;
    
    @Schema(description = "是否记住登录", example = "false")
    private Boolean rememberMe = false;
}
