/**
 * 用户状态管理模块
 */
import { post, get } from '../../utils/request.js'

const state = {
  // 用户信息
  userInfo: null,
  // 登录状态
  isLoggedIn: false,
  // 用户token
  token: '',
  // 用户偏好设置
  preferences: {},
  // 用户统计数据
  statistics: {
    totalUsage: 0,
    todayUsage: 0,
    dailyQuota: 10,
    remainingQuota: 10
  }
}

const mutations = {
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
    state.isLoggedIn = !!userInfo
  },
  
  SET_TOKEN(state, token) {
    state.token = token
    // 同步到本地存储
    if (token) {
      uni.setStorageSync('token', token)
    } else {
      uni.removeStorageSync('token')
    }
  },
  
  SET_PREFERENCES(state, preferences) {
    state.preferences = preferences
  },
  
  SET_STATISTICS(state, statistics) {
    state.statistics = { ...state.statistics, ...statistics }
  },
  
  UPDATE_USAGE(state, usage) {
    state.statistics.todayUsage = usage
    state.statistics.remainingQuota = state.statistics.dailyQuota - usage
  },
  
  CLEAR_USER_DATA(state) {
    state.userInfo = null
    state.isLoggedIn = false
    state.token = ''
    state.preferences = {}
    state.statistics = {
      totalUsage: 0,
      todayUsage: 0,
      dailyQuota: 10,
      remainingQuota: 10
    }
  }
}

const actions = {
  // 用户登录
  async login({ commit }, { username, password }) {
    try {
      const response = await post('/auth/login', {
        username,
        password
      })
      
      const { token, userInfo } = response
      
      commit('SET_TOKEN', token)
      commit('SET_USER_INFO', userInfo)
      
      // 登录成功后加载用户数据
      await this.dispatch('user/loadUserData')
      
      return response
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  },
  
  // 用户注册
  async register({ commit }, userData) {
    try {
      const response = await post('/auth/register', userData)
      
      const { token, userInfo } = response
      
      commit('SET_TOKEN', token)
      commit('SET_USER_INFO', userInfo)
      
      return response
    } catch (error) {
      console.error('Register failed:', error)
      throw error
    }
  },
  
  // 用户登出
  async logout({ commit }) {
    try {
      await post('/auth/logout')
    } catch (error) {
      console.error('Logout failed:', error)
    } finally {
      // 清除本地数据
      commit('CLEAR_USER_DATA')
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      
      // 跳转到登录页
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }
  },
  
  // 检查登录状态
  async checkLoginStatus({ commit, state }) {
    const token = uni.getStorageSync('token')
    
    if (!token) {
      commit('CLEAR_USER_DATA')
      return false
    }
    
    try {
      commit('SET_TOKEN', token)
      
      // 验证token有效性
      const userInfo = await get('/auth/profile')
      commit('SET_USER_INFO', userInfo)
      
      return true
    } catch (error) {
      console.error('Token validation failed:', error)
      commit('CLEAR_USER_DATA')
      return false
    }
  },
  
  // 加载用户数据
  async loadUserData({ commit, state }) {
    if (!state.isLoggedIn) return
    
    try {
      // 加载用户偏好设置
      const preferences = await get('/user/preferences')
      commit('SET_PREFERENCES', preferences)
      
      // 加载用户统计数据
      const statistics = await get('/user/statistics')
      commit('SET_STATISTICS', statistics)
      
    } catch (error) {
      console.error('Load user data failed:', error)
    }
  },
  
  // 更新用户信息
  async updateUserInfo({ commit }, userInfo) {
    try {
      const updatedInfo = await post('/user/update', userInfo)
      commit('SET_USER_INFO', updatedInfo)
      return updatedInfo
    } catch (error) {
      console.error('Update user info failed:', error)
      throw error
    }
  },
  
  // 更新用户偏好设置
  async updatePreferences({ commit }, preferences) {
    try {
      const updatedPreferences = await post('/user/preferences', preferences)
      commit('SET_PREFERENCES', updatedPreferences)
      return updatedPreferences
    } catch (error) {
      console.error('Update preferences failed:', error)
      throw error
    }
  },
  
  // 增加使用次数
  incrementUsage({ commit, state }) {
    const newUsage = state.statistics.todayUsage + 1
    commit('UPDATE_USAGE', newUsage)
  },
  
  // 检查是否还有配额
  checkQuota({ state }) {
    return state.statistics.remainingQuota > 0
  }
}

const getters = {
  // 用户是否已登录
  isLoggedIn: state => state.isLoggedIn,
  
  // 用户信息
  userInfo: state => state.userInfo,
  
  // 用户昵称
  nickname: state => state.userInfo?.nickname || '未登录',
  
  // 用户头像
  avatar: state => state.userInfo?.avatar || '/static/images/default-avatar.png',
  
  // 是否为VIP用户
  isVip: state => state.userInfo?.role === 'premium',
  
  // 剩余配额
  remainingQuota: state => state.statistics.remainingQuota,
  
  // 今日使用次数
  todayUsage: state => state.statistics.todayUsage,
  
  // 配额使用率
  quotaUsageRate: state => {
    const { todayUsage, dailyQuota } = state.statistics
    return dailyQuota > 0 ? (todayUsage / dailyQuota * 100).toFixed(1) : 0
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
