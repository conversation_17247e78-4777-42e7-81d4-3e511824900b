package com.emotional.service.service.impl;

import com.emotional.service.service.EmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

/**
 * 邮件服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Service
@Slf4j
public class EmailServiceImpl implements EmailService {
    
    @Autowired
    private JavaMailSender mailSender;
    
    @Value("${spring.mail.username}")
    private String fromEmail;
    
    @Override
    public boolean sendVerificationCode(String to, String code) {
        String subject = "【情感回复助手】邮箱验证码";
        String content = buildVerificationCodeContent(code, "注册验证");
        return sendHtmlEmail(to, subject, content);
    }
    
    @Override
    public boolean sendPasswordResetCode(String to, String code) {
        String subject = "【情感回复助手】密码重置验证码";
        String content = buildPasswordResetCodeContent(code);

        // 尝试发送真实邮件
        boolean realEmailSent = sendHtmlEmail(to, subject, content);

        if (realEmailSent) {
            return true;
        } else {
            // 如果真实邮件发送失败，使用模拟模式
            log.warn("真实邮件发送失败，使用模拟模式: to={}, code={}", to, code);
            log.info("=== 模拟邮件发送 ===");
            log.info("收件人: {}", to);
            log.info("主题: {}", subject);
            log.info("验证码: {}", code);
            log.info("===================");
            return true; // 模拟发送成功
        }
    }
    
    @Override
    public boolean sendSimpleEmail(String to, String subject, String content) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(to);
            message.setSubject(subject);
            message.setText(content);
            
            mailSender.send(message);
            log.info("简单邮件发送成功: to={}, subject={}", to, subject);
            return true;
            
        } catch (Exception e) {
            log.error("简单邮件发送失败: to={}, subject={}", to, subject, e);
            return false;
        }
    }
    
    @Override
    public boolean sendHtmlEmail(String to, String subject, String htmlContent) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            
            helper.setFrom(fromEmail);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(htmlContent, true);
            
            mailSender.send(message);
            log.info("HTML邮件发送成功: to={}, subject={}", to, subject);
            return true;
            
        } catch (MessagingException e) {
            log.error("HTML邮件发送失败: to={}, subject={}", to, subject, e);
            return false;
        }
    }
    
    /**
     * 构建验证码邮件内容
     */
    private String buildVerificationCodeContent(String code, String purpose) {
        return "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "    <meta charset=\"UTF-8\">" +
                "    <title>验证码</title>" +
                "</head>" +
                "<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">" +
                "    <div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">" +
                "        <div style=\"text-align: center; margin-bottom: 30px;\">" +
                "            <h1 style=\"color: #2196F3; margin: 0;\">情感回复助手</h1>" +
                "            <p style=\"color: #666; margin: 5px 0;\">Emotional Reply Assistant</p>" +
                "        </div>" +
                "        " +
                "        <div style=\"background: #f8f9fa; padding: 30px; border-radius: 8px; text-align: center;\">" +
                "            <h2 style=\"color: #333; margin-bottom: 20px;\">" + purpose + "验证码</h2>" +
                "            <div style=\"background: white; padding: 20px; border-radius: 6px; margin: 20px 0;\">" +
                "                <span style=\"font-size: 32px; font-weight: bold; color: #2196F3; letter-spacing: 5px;\">" + code + "</span>" +
                "            </div>" +
                "            <p style=\"color: #666; margin: 20px 0;\">验证码有效期为 <strong>5分钟</strong>，请及时使用。</p>" +
                "            <p style=\"color: #999; font-size: 14px;\">如果您没有进行此操作，请忽略此邮件。</p>" +
                "        </div>" +
                "        " +
                "        <div style=\"text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;\">" +
                "            <p style=\"color: #999; font-size: 12px; margin: 0;\">此邮件由系统自动发送，请勿回复。</p>" +
                "        </div>" +
                "    </div>" +
                "</body>" +
                "</html>";
    }
    
    /**
     * 构建密码重置验证码邮件内容
     */
    private String buildPasswordResetCodeContent(String code) {
        return "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "    <meta charset=\"UTF-8\">" +
                "    <title>密码重置验证码</title>" +
                "</head>" +
                "<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">" +
                "    <div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">" +
                "        <div style=\"text-align: center; margin-bottom: 30px;\">" +
                "            <h1 style=\"color: #2196F3; margin: 0;\">情感回复助手</h1>" +
                "            <p style=\"color: #666; margin: 5px 0;\">Emotional Reply Assistant</p>" +
                "        </div>" +
                "        " +
                "        <div style=\"background: #fff3cd; padding: 30px; border-radius: 8px; border-left: 4px solid #ffc107;\">" +
                "            <h2 style=\"color: #856404; margin-bottom: 20px;\">🔐 密码重置验证码</h2>" +
                "            <p style=\"color: #856404; margin-bottom: 20px;\">您正在重置密码，请使用以下验证码完成操作：</p>" +
                "            " +
                "            <div style=\"background: white; padding: 20px; border-radius: 6px; margin: 20px 0; text-align: center;\">" +
                "                <span style=\"font-size: 32px; font-weight: bold; color: #dc3545; letter-spacing: 5px;\">" + code + "</span>" +
                "            </div>" +
                "            " +
                "            <div style=\"background: #f8d7da; padding: 15px; border-radius: 4px; margin: 20px 0;\">" +
                "                <p style=\"color: #721c24; margin: 0; font-size: 14px;\">⚠️ 安全提醒：</p>" +
                "                <ul style=\"color: #721c24; margin: 10px 0; padding-left: 20px; font-size: 14px;\">" +
                "                    <li>验证码有效期为 <strong>5分钟</strong></li>" +
                "                    <li>请勿将验证码告诉他人</li>" +
                "                    <li>如非本人操作，请立即联系客服</li>" +
                "                </ul>" +
                "            </div>" +
                "        </div>" +
                "        " +
                "        <div style=\"text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;\">" +
                "            <p style=\"color: #999; font-size: 12px; margin: 0;\">此邮件由系统自动发送，请勿回复。</p>" +
                "        </div>" +
                "    </div>" +
                "</body>" +
                "</html>";
    }
}
