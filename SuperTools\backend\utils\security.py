#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
安全配置模块
处理敏感信息的隐藏和安全配置
"""

import os
from flask import request

def get_safe_host():
    """
    获取安全的主机地址，避免暴露内部IP
    """
    # 在生产环境中，使用请求的Host头
    if request:
        host = request.headers.get('Host')
        if host:
            # 确保使用HTTPS（如果在生产环境）
            scheme = 'https' if request.is_secure else 'http'
            return f"{scheme}://{host}"
    
    # 开发环境回退
    return "http://localhost:5000"

def get_safe_api_base():
    """
    获取安全的API基础地址
    """
    # 检查是否为生产环境
    if os.environ.get('FLASK_ENV') == 'production':
        return get_safe_host()
    
    # 开发环境使用相对路径
    return ""

def sanitize_url(url):
    """
    清理URL，移除敏感信息
    """
    if not url:
        return url
    
    # 移除localhost和127.0.0.1引用
    url = url.replace('http://127.0.0.1:5000', '')
    url = url.replace('http://localhost:5000', '')
    url = url.replace('http://127.0.0.1:8080', '')
    url = url.replace('http://localhost:8080', '')
    
    # 确保以/开头的相对路径
    if url and not url.startswith('/') and not url.startswith('http'):
        url = '/' + url
    
    return url

def get_safe_media_url(filename):
    """
    生成安全的媒体文件URL
    """
    # 使用相对路径，避免暴露服务器信息
    return f"/media/videos/{filename}"

def is_production():
    """
    检查是否为生产环境
    """
    return os.environ.get('FLASK_ENV') == 'production' or os.environ.get('PRODUCTION') == 'true'

def get_safe_service_status():
    """
    获取安全的服务状态信息（不暴露内部配置）
    """
    return {
        'environment': 'production' if is_production() else 'development',
        'debug_mode': not is_production()
    }

class SecurityConfig:
    """
    安全配置类
    """
    
    @staticmethod
    def hide_sensitive_info(data):
        """
        隐藏响应中的敏感信息
        """
        if isinstance(data, dict):
            # 移除敏感字段
            sensitive_fields = ['api_base', 'host', 'port', 'internal_url', 'server_info']
            for field in sensitive_fields:
                data.pop(field, None)
            
            # 递归处理嵌套字典
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    data[key] = SecurityConfig.hide_sensitive_info(value)
        
        elif isinstance(data, list):
            # 处理列表中的每个元素
            for i, item in enumerate(data):
                data[i] = SecurityConfig.hide_sensitive_info(item)
        
        return data
    
    @staticmethod
    def sanitize_error_message(error_msg):
        """
        清理错误消息中的敏感信息
        """
        if not error_msg:
            return error_msg
        
        # 移除可能的路径信息
        error_msg = error_msg.replace(os.getcwd(), '[PROJECT_ROOT]')
        
        # 移除IP地址
        import re
        error_msg = re.sub(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', '[IP_ADDRESS]', error_msg)
        
        # 移除端口信息
        error_msg = re.sub(r':\d{4,5}\b', ':[PORT]', error_msg)
        
        return error_msg
