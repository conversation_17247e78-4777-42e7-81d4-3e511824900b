package com.emotional.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 激活码实体类（使用现有的activation_codes表）
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("activation_codes")
public class VipActivationCode {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 激活码
     */
    private String code;

    /**
     * 激活码类型：monthly-月度，yearly-年度
     */
    @TableField("code_type")
    private String codeType;

    /**
     * 有效期（天数）
     */
    @TableField("duration_days")
    private Integer durationDays;

    /**
     * 批次ID
     */
    @TableField("batch_id")
    private String batchId;

    /**
     * 创建者ID（管理员）
     */
    @TableField("created_by")
    private Long createdBy;

    /**
     * 使用的用户ID
     */
    @TableField("used_by")
    private Long usedBy;

    /**
     * 使用时间
     */
    @TableField("used_time")
    private LocalDateTime usedTime;

    /**
     * 状态：0-未使用，1-已使用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;
}
