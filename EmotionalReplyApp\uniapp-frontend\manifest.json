{"name": "情感回复助手", "appid": "__UNI__EEEA054", "description": "智能情感分析回复助手应用", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "h5": {"devServer": {"port": 3000, "disableHostCheck": true, "open": false}, "publicPath": "/", "router": {"mode": "hash", "base": "./"}, "optimization": {"treeShaking": {"enable": false}}}, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Clipboard": {}, "Storage": {}, "Camera": {}, "Gallery": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\" />", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\" />", "<uses-permission android:name=\"android.permission.VIBRATE\" />", "<uses-permission android:name=\"android.permission.READ_LOGS\" />", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\" />", "<uses-feature android:name=\"android.hardware.camera.autofocus\" />", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />", "<uses-permission android:name=\"android.permission.CAMERA\" />", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\" />", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\" />", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\" />", "<uses-permission android:name=\"android.permission.WAKE_LOCK\" />", "<uses-permission android:name=\"android.permission.FLASHLIGHT\" />", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\" />", "<uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\" />", "<uses-permission android:name=\"android.permission.FOREGROUND_SERVICE\" />", "<uses-permission android:name=\"android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS\" />"], "abiFilters": ["armeabi-v7a", "arm64-v8a"], "minSdkVersion": 23, "targetSdkVersion": 34}, "ios": {"deploymentTarget": "10.0", "privacyDescription": {"NSCameraUsageDescription": "此应用需要使用摄像头扫描二维码", "NSPhotoLibraryUsageDescription": "此应用需要访问相册选择图片", "NSLocationWhenInUseUsageDescription": "此应用需要在使用期间访问位置信息"}}, "sdkConfigs": {"ad": {}, "oauth": {}, "payment": {}, "push": {}, "share": {}, "statics": {}}, "icons": {"android": {"hdpi": "static/logo/logo72.png", "xhdpi": "static/logo/logo96.png", "xxhdpi": "static/logo/logo144.png", "xxxhdpi": "static/logo/logo192.png"}}}, "nativePlugins": {"floating-window-plugin": {"version": "1.0.0", "description": "悬浮窗原生插件", "path": "native-plugins/android/floating-window"}}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3"}