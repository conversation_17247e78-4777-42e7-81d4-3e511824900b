package com.emotional.service.service;

/**
 * 管理员通知服务接口
 */
public interface AdminNotificationService {
    
    /**
     * 通知管理员API密钥问题
     * @param errorType 错误类型
     * @param errorMessage 错误信息
     * @param userId 用户ID（可选）
     */
    void notifyApiKeyIssue(String errorType, String errorMessage, Long userId);
    
    /**
     * 通知管理员余额不足
     * @param errorMessage 错误信息
     * @param userId 用户ID（可选）
     */
    void notifyInsufficientBalance(String errorMessage, Long userId);
    
    /**
     * 通知管理员服务异常
     * @param errorMessage 错误信息
     * @param userId 用户ID（可选）
     */
    void notifyServiceError(String errorMessage, Long userId);
}
