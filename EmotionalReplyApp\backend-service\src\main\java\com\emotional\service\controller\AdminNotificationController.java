package com.emotional.service.controller;

import com.emotional.service.common.Result;
import com.emotional.service.service.NotificationThrottleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 管理员通知管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/notifications")
@RequiredArgsConstructor
@Tag(name = "管理员通知管理", description = "管理员通知频率控制和状态查看")
public class AdminNotificationController {
    
    private final NotificationThrottleService throttleService;
    
    /**
     * 获取通知状态统计
     */
    @GetMapping("/stats")
    @Operation(summary = "获取通知状态统计", description = "查看各类通知的发送状态和频率限制情况")
    public Result<NotificationThrottleService.NotificationStats> getNotificationStats() {
        try {
            NotificationThrottleService.NotificationStats stats = throttleService.getNotificationStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取通知统计失败", e);
            return Result.error("获取通知统计失败");
        }
    }
    
    /**
     * 清除所有通知频率限制
     */
    @PostMapping("/throttle/clear")
    @Operation(summary = "清除通知频率限制", description = "清除所有通知的频率限制，允许立即发送通知")
    public Result<String> clearNotificationThrottles() {
        try {
            throttleService.clearAllThrottles();
            log.info("管理员手动清除了所有通知频率限制");
            return Result.success("已清除所有通知频率限制");
        } catch (Exception e) {
            log.error("清除通知频率限制失败", e);
            return Result.error("清除通知频率限制失败");
        }
    }
    
    /**
     * 检查API密钥错误通知状态
     */
    @GetMapping("/throttle/api-key/{errorType}")
    @Operation(summary = "检查API密钥错误通知状态", description = "检查指定类型的API密钥错误通知是否被频率限制")
    public Result<Boolean> checkApiKeyErrorThrottle(@PathVariable String errorType) {
        try {
            boolean canSend = throttleService.canSendApiKeyErrorNotification(errorType);
            return Result.success(canSend);
        } catch (Exception e) {
            log.error("检查API密钥错误通知状态失败", e);
            return Result.error("检查通知状态失败");
        }
    }
    
    /**
     * 检查余额不足通知状态
     */
    @GetMapping("/throttle/balance")
    @Operation(summary = "检查余额不足通知状态", description = "检查余额不足通知是否被频率限制")
    public Result<Boolean> checkBalanceErrorThrottle() {
        try {
            boolean canSend = throttleService.canSendBalanceErrorNotification();
            return Result.success(canSend);
        } catch (Exception e) {
            log.error("检查余额不足通知状态失败", e);
            return Result.error("检查通知状态失败");
        }
    }
    
    /**
     * 检查服务错误通知状态
     */
    @GetMapping("/throttle/service/{errorType}")
    @Operation(summary = "检查服务错误通知状态", description = "检查指定类型的服务错误通知是否被频率限制")
    public Result<Boolean> checkServiceErrorThrottle(@PathVariable String errorType) {
        try {
            boolean canSend = throttleService.canSendServiceErrorNotification(errorType);
            return Result.success(canSend);
        } catch (Exception e) {
            log.error("检查服务错误通知状态失败", e);
            return Result.error("检查通知状态失败");
        }
    }
}
