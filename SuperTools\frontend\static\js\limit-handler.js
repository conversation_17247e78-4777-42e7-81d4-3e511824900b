/**
 * 限制处理器 - 提供友好的限制提示和处理
 */

/**
 * 显示友好的限制错误提示
 * @param {Object} errorData - 错误数据
 * @param {string} platform - 平台名称
 */
function showLimitError(errorData, platform = '') {
    const platformNames = {
        'douyin': '抖音',
        'kuaishou': '快手',
        'bilibili': '哔哩哔哩',
        'csdn': 'CSDN'
    };
    
    const platformName = platformNames[platform] || platform || '该平台';
    
    // 创建限制提示模态框
    const modal = createLimitModal(errorData, platformName);
    document.body.appendChild(modal);
    
    // 显示模态框
    setTimeout(() => {
        modal.classList.add('show');
    }, 100);
    
    // 自动关闭（可选）
    setTimeout(() => {
        closeLimitModal(modal);
    }, 10000); // 10秒后自动关闭
}

/**
 * 创建限制提示模态框
 * @param {Object} errorData - 错误数据
 * @param {string} platformName - 平台名称
 */
function createLimitModal(errorData, platformName) {
    const modal = document.createElement('div');
    modal.className = 'limit-modal-overlay';
    
    const isRateLimit = errorData.error_type === 'rate_limit';
    const isDailyLimit = errorData.error_type === 'daily_limit';
    
    // 根据限制类型选择图标和颜色
    const config = isRateLimit ? {
        icon: '⏰',
        color: '#f39c12',
        title: '操作太频繁了',
        subtitle: '请稍等一下再试'
    } : {
        icon: '📊',
        color: '#e74c3c',
        title: '今日解析次数已用完',
        subtitle: '明天再来或升级账户'
    };
    
    modal.innerHTML = `
        <div class="limit-modal" style="
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            max-width: 480px;
            width: 90%;
            z-index: 10001;
            text-align: center;
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.9);
            transition: all 0.3s ease;
        ">
            <div class="limit-icon" style="
                font-size: 64px;
                margin-bottom: 16px;
                animation: bounce 1s ease-in-out;
            ">${config.icon}</div>
            
            <h3 style="
                color: ${config.color};
                font-size: 24px;
                font-weight: 600;
                margin: 0 0 8px 0;
            ">${config.title}</h3>
            
            <p style="
                color: #666;
                font-size: 16px;
                margin: 0 0 24px 0;
                line-height: 1.5;
            ">${config.subtitle}</p>
            
            <div class="limit-details" style="
                background: #f8f9fa;
                border-radius: 12px;
                padding: 20px;
                margin-bottom: 24px;
                text-align: left;
            ">
                <div style="
                    font-size: 16px;
                    color: #2c3e50;
                    margin-bottom: 12px;
                    font-weight: 500;
                ">${errorData.user_message || errorData.message}</div>
                
                ${errorData.data ? `
                    <div style="font-size: 14px; color: #666; margin-bottom: 8px;">
                        📍 重置时间: ${errorData.data.reset_time}
                    </div>
                    ${errorData.data.upgrade_hint ? `
                        <div style="font-size: 14px; color: #27ae60;">
                            💡 ${errorData.data.upgrade_hint}
                        </div>
                    ` : ''}
                ` : ''}
            </div>
            
            <div class="limit-actions" style="
                display: flex;
                gap: 12px;
                justify-content: center;
            ">
                <button class="limit-btn-secondary" onclick="closeLimitModal(this.closest('.limit-modal-overlay'))" style="
                    background: #6c757d;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: background 0.2s;
                " onmouseover="this.style.background='#5a6268'" onmouseout="this.style.background='#6c757d'">
                    我知道了
                </button>
                
                ${!isDailyLimit ? '' : `
                    <button class="limit-btn-primary" onclick="showUpgradeInfo()" style="
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 14px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: transform 0.2s;
                    " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                        🚀 升级账户
                    </button>
                `}
            </div>
        </div>
    `;
    
    // 添加样式
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 10000;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    
    // 点击背景关闭
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeLimitModal(modal);
        }
    });
    
    return modal;
}

/**
 * 关闭限制提示模态框
 * @param {HTMLElement} modal - 模态框元素
 */
function closeLimitModal(modal) {
    modal.style.opacity = '0';
    const modalContent = modal.querySelector('.limit-modal');
    if (modalContent) {
        modalContent.style.transform = 'translate(-50%, -50%) scale(0.9)';
        modalContent.style.opacity = '0';
    }
    
    setTimeout(() => {
        if (modal.parentNode) {
            modal.parentNode.removeChild(modal);
        }
    }, 300);
}

/**
 * 显示升级信息
 */
function showUpgradeInfo() {
    // 关闭当前模态框
    const currentModal = document.querySelector('.limit-modal-overlay');
    if (currentModal) {
        closeLimitModal(currentModal);
    }
    
    // 显示升级信息（可以跳转到升级页面或显示升级模态框）
    setTimeout(() => {
        showUpgradeModal();
    }, 400);
}

/**
 * 显示升级模态框
 */
function showUpgradeModal() {
    const modal = document.createElement('div');
    modal.className = 'upgrade-modal-overlay';
    
    modal.innerHTML = `
        <div class="upgrade-modal" style="
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            max-width: 520px;
            width: 90%;
            z-index: 10001;
            text-align: center;
        ">
            <div style="font-size: 48px; margin-bottom: 16px;">🚀</div>
            
            <h3 style="
                color: #667eea;
                font-size: 24px;
                font-weight: 600;
                margin: 0 0 16px 0;
            ">升级到Pro账户</h3>
            
            <div style="
                background: linear-gradient(135deg, #667eea20 0%, #764ba220 100%);
                border-radius: 12px;
                padding: 24px;
                margin-bottom: 24px;
                text-align: left;
            ">
                <div style="font-size: 18px; font-weight: 600; color: #2c3e50; margin-bottom: 16px;">
                    Pro账户特权
                </div>
                <div style="display: grid; gap: 12px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="color: #27ae60;">✅</span>
                        <span>每日50次解析（vs 普通用户5次）</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="color: #27ae60;">✅</span>
                        <span>每分钟10次API调用（vs 普通用户3次）</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="color: #27ae60;">✅</span>
                        <span>支持知乎文章解析</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="color: #27ae60;">✅</span>
                        <span>优先技术支持</span>
                    </div>
                </div>
            </div>
            
            <div style="
                display: flex;
                gap: 12px;
                justify-content: center;
            ">
                <button onclick="closeLimitModal(this.closest('.upgrade-modal-overlay'))" style="
                    background: #6c757d;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    font-size: 14px;
                    cursor: pointer;
                ">
                    稍后再说
                </button>
                <button onclick="goToUpgrade()" style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                ">
                    立即升级
                </button>
            </div>
        </div>
    `;
    
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 10000;
    `;
    
    // 点击背景关闭
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeLimitModal(modal);
        }
    });
    
    document.body.appendChild(modal);
}

/**
 * 跳转到升级页面
 */
function goToUpgrade() {
    // 关闭模态框
    const modal = document.querySelector('.upgrade-modal-overlay');
    if (modal) {
        closeLimitModal(modal);
    }
    
    // 这里可以跳转到升级页面或打开升级功能
    // 例如：window.location.href = '/upgrade';
    // 或者显示升级相关的功能
    showToast('升级功能即将开放，敬请期待！', 'info');
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }
    
    .limit-modal-overlay.show {
        opacity: 1 !important;
    }
    
    .limit-modal-overlay.show .limit-modal {
        opacity: 1 !important;
        transform: translate(-50%, -50%) scale(1) !important;
    }
`;
document.head.appendChild(style);

// 导出函数供全局使用
window.showLimitError = showLimitError;
window.closeLimitModal = closeLimitModal;
