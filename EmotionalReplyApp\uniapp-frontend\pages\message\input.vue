<template>
  <view class="container themed" :class="themeClass">
    <view class="header">
      <text class="title">消息输入</text>
      <text class="subtitle">输入收到的消息，获取回复建议</text>
    </view>
    
    <view class="input-section">
      <view class="input-label">
        <text>收到的消息</text>
      </view>
      <textarea
        v-model="inputMessage"
        class="message-input"
        placeholder="请输入收到的消息内容..."
        :maxlength="500"
        auto-height
        :show-confirm-bar="false"
        :cursor-spacing="20"
      />
      <view class="char-count" :class="charCountClass">
        <text>{{ inputMessage.length }}/500</text>
      </view>
    </view>

    <!-- 对方性别选择 -->
    <view class="gender-section">
      <view class="input-label">
        <text>对方性别</text>
        <text class="optional-label">（可选，有助于生成更合适的回复）</text>
      </view>
      <view class="gender-options">
        <view
          v-for="option in genderOptions"
          :key="option.value"
          class="gender-option"
          :class="{ active: selectedGender === option.value }"
          @click="selectGender(option.value)"
        >
          <text class="gender-icon">{{ option.icon }}</text>
          <text class="gender-label">{{ option.label }}</text>
        </view>
      </view>
    </view>
    
    <view class="action-section">
      <button 
        class="analyze-btn"
        :disabled="!inputMessage.trim()"
        @click="analyzeMessage"
      >
        <text>分析并生成回复</text>
      </button>
    </view>
    
    <view class="quick-actions" v-if="recentMessages.length > 0">
      <text class="section-title">最近消息</text>
      <view class="recent-list">
        <view 
          v-for="(msg, index) in recentMessages" 
          :key="index"
          class="recent-item"
          @click="selectRecentMessage(msg)"
        >
          <text class="recent-text">{{ msg }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { HistoryManager } from '../../utils/storage.js'
import { getHistoryList } from '../../api/history.js'
import { UserManager } from '../../utils/user.js'
import { AuthGuard } from '../../utils/auth-guard.js'
import themeManager from '../../utils/theme.js'

export default {
  name: 'MessageInput',
  
  data() {
    return {
      inputMessage: '',
      recentMessages: [],
      themeClass: 'theme-auto',
      selectedGender: '', // 选中的性别
      genderOptions: [
        { value: '', icon: '❓', label: '未知' },
        { value: 'male', icon: '👨', label: '男性' },
        { value: 'female', icon: '👩', label: '女性' }
      ]
    }
  },

  computed: {
    // 是否已登录
    isLoggedIn() {
      return UserManager.isLoggedIn()
    },

    // 字数统计样式类
    charCountClass() {
      const length = this.inputMessage.length
      if (length >= 450) {
        return 'error'
      } else if (length >= 400) {
        return 'warning'
      }
      return ''
    }
  },

  onLoad() {
    // 路由守卫检查
    if (!AuthGuard.pageGuard('pages/message/input')) {
      return
    }

    this.loadRecentMessages()
    this.initTheme()
  },
  
  methods: {
    // 加载最近消息
    async loadRecentMessages() {
      try {
        // 尝试从API获取最近的历史记录
        const currentUserId = UserManager.getCurrentUserId()
        const historyList = await getHistoryList(1, 10, currentUserId)

        // 提取原始消息作为最近消息
        this.recentMessages = historyList
          .map(item => item.originalMessage)
          .filter((message, index, arr) => arr.indexOf(message) === index) // 去重
          .slice(0, 5) // 只取前5条

      } catch (error) {
        console.log('API获取失败，使用本地历史记录:', error)

        // API失败时从本地存储获取
        const localHistory = HistoryManager.getHistory()
        this.recentMessages = localHistory
          .map(item => item.originalMessage)
          .filter((message, index, arr) => arr.indexOf(message) === index) // 去重
          .slice(0, 5) // 只取前5条

        // 如果本地也没有，使用默认消息
        if (this.recentMessages.length === 0) {
          this.recentMessages = [
            '今天心情不太好',
            '工作压力好大啊',
            '想你了',
            '晚上一起吃饭吗？',
            '谢谢你的帮助'
          ]
        }
      }
    },

    // 分析消息
    analyzeMessage() {
      if (!this.isLoggedIn) {
        uni.showModal({
          title: '需要登录',
          content: '请先登录以使用智能回复功能',
          showCancel: false,
          success: () => {
            uni.navigateTo({
              url: '/pages/login/login'
            })
          }
        })
        return
      }

      if (!this.inputMessage.trim()) {
        uni.showToast({
          title: '请输入消息内容',
          icon: 'none'
        })
        return
      }

      // 跳转到回复生成页面，传递消息和性别信息
      const params = new URLSearchParams({
        message: this.inputMessage
      })

      if (this.selectedGender) {
        params.append('gender', this.selectedGender)
      }

      uni.navigateTo({
        url: `/pages/reply/generation?${params.toString()}`
      })
    },

    // 选择性别
    selectGender(gender) {
      this.selectedGender = gender
    },
    
    // 选择最近消息
    selectRecentMessage(message) {
      this.inputMessage = message
    },

    // 初始化主题
    initTheme() {
      try {
        this.themeClass = `theme-${themeManager.getCurrentTheme()}`
      } catch (error) {
        console.error('初始化主题失败:', error)
        this.themeClass = 'theme-auto'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../styles/theme.scss';

.container {
  padding: 20rpx;
  background-color: var(--bg-color);
  color: var(--text-color);
  min-height: 100vh;
  transition: all 0.3s ease;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 16rpx;
    transition: color 0.3s ease;
  }

  .subtitle {
    font-size: 28rpx;
    color: var(--text-color-secondary);
    transition: color 0.3s ease;
  }
}

.input-section {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  transition: all 0.3s ease;
  
  .input-label {
    margin-bottom: 20rpx;

    text {
      font-size: 32rpx;
      font-weight: bold;
      color: var(--text-color);
      transition: color 0.3s ease;
    }

    .optional-label {
      font-size: 24rpx !important;
      font-weight: normal !important;
      color: var(--text-color-secondary) !important;
      margin-left: 16rpx;
    }
  }
  
  .message-input {
    width: 100%;
    min-height: 300rpx;
    max-height: 600rpx;
    padding: 24rpx;
    border: 2rpx solid var(--border-color);
    border-radius: 12rpx;
    font-size: 30rpx;
    line-height: 1.6;
    background-color: var(--input-bg);
    color: var(--text-color);
    transition: all 0.3s ease;
    box-sizing: border-box;

    &:focus {
      border-color: #2196F3;
      box-shadow: 0 0 0 4rpx rgba(33, 150, 243, 0.1);
    }
  }
  
  .char-count {
    text-align: right;
    margin-top: 20rpx;

    text {
      font-size: 26rpx;
      color: #999;
      transition: color 0.3s ease;
    }

    &.warning text {
      color: #ff9800;
    }

    &.error text {
      color: #f44336;
    }
  }
}

// 性别选择区域
.gender-section {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  transition: all 0.3s ease;

  .input-label {
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;

    text {
      font-size: 32rpx;
      font-weight: bold;
      color: var(--text-color);
      transition: color 0.3s ease;
    }

    .optional-label {
      font-size: 24rpx !important;
      font-weight: normal !important;
      color: var(--text-color-secondary) !important;
      margin-left: 16rpx;
    }
  }

  .gender-options {
    display: flex;
    gap: 20rpx;

    .gender-option {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24rpx 16rpx;
      border: 2rpx solid var(--border-color);
      border-radius: 12rpx;
      background: var(--card-bg);
      transition: all 0.3s ease;
      cursor: pointer;

      .gender-icon {
        font-size: 40rpx;
        margin-bottom: 12rpx;
      }

      .gender-label {
        font-size: 26rpx;
        color: var(--text-color-secondary);
        transition: color 0.3s ease;
      }

      &.active {
        border-color: #2196F3;
        background: rgba(33, 150, 243, 0.1);

        .gender-label {
          color: #2196F3;
          font-weight: bold;
        }
      }

      &:hover {
        border-color: #2196F3;
        transform: translateY(-2rpx);
      }
    }
  }
}

.action-section {
  margin-bottom: 40rpx;
  
  .analyze-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #2196F3, #21CBF3);
    border-radius: 44rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    
    text {
      color: white;
      font-size: 32rpx;
      font-weight: bold;
    }
    
    &:disabled {
      background: #ccc;
    }
  }
}

.quick-actions {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .recent-list {
    .recent-item {
      padding: 20rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      margin-bottom: 16rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .recent-text {
        font-size: 28rpx;
        color: #333;
      }
    }
  }
}
</style>
