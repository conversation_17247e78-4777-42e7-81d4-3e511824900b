package com.emotional.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户统计实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_stats")
public class UserStats {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 统计日期
     */
    private LocalDate statDate;

    /**
     * 当日使用次数
     */
    private Integer dailyUsage;

    /**
     * 累计使用次数
     */
    private Integer totalUsage;

    /**
     * 当日配额
     */
    private Integer dailyQuota;

    /**
     * 是否VIP用户：0-普通用户，1-VIP用户
     */
    private Integer isVip;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updateTime;
}
