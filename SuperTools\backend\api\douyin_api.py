#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
抖音API模块
提供抖音视频解析相关的API
"""

import logging
import traceback

from flask import Blueprint, request, jsonify

from ..spiders.douyin_spider import <PERSON>uyinSpider
from ..utils.permissions import require_search_limit

# 创建日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
douyin_api = Blueprint('douyin_api', __name__, url_prefix='/douyin')


@douyin_api.route('/parse', methods=['POST'])
@require_search_limit()
def parse_video():
    """
    解析抖音视频链接

    请求体:
        {
            "video_url": "视频URL"
        }

    响应:
        {
            "success": true,
            "message": "解析成功",
            "data": {
                "title": "视频标题",
                "author": "作者名称",
                "videoUrl": "视频播放链接",
                "coverUrl": "封面图片链接",
                "description": "视频描述",
                "duration": "视频时长",
                "playCount": "播放次数",
                "likeCount": "点赞数",
                "commentCount": "评论数",
                "shareCount": "分享数"
            }
        }
    """
    try:
        # 验证请求参数
        if not request.is_json:
            return jsonify({
                "success": False,
                "message": "请求格式错误，需要JSON格式",
                "data": None
            }), 400

        data = request.json
        video_url = data.get('video_url')

        # 验证必要参数
        if not video_url:
            return jsonify({
                "success": False,
                "message": "请提供视频URL",
                "data": None
            }), 400

        # 创建抖音爬虫
        spider = DouyinSpider()

        # 执行解析任务
        result = spider.execute({
            "video_url": video_url,
            "download": False
        })

        return jsonify(result), 200 if result["success"] else 500

    except Exception as e:
        logger.error(f"解析视频失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }), 500


@douyin_api.route('/status', methods=['GET'])
def check_status():
    """
    检查抖音API服务状态

    响应:
        {
            "success": true,
            "message": "服务正常",
            "data": {
                "spider": {
                    "name": "抖音爬虫",
                    "platform": "douyin",
                    "status": "ready",
                    "version": "1.0.0"
                }
            }
        }
    """
    try:
        # 创建爬虫实例
        spider = DouyinSpider()
        spider_status = spider.check_status()

        return jsonify({
            "success": True,
            "message": "服务正常",
            "data": {
                "spider": spider_status
            }
        }), 200

    except Exception as e:
        logger.error(f"检查状态失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务异常: {str(e)}",
            "data": None
        }), 500


@douyin_api.route('/parse', methods=['GET'])
@require_search_limit()
def parse_video_get():
    """
    解析抖音视频链接（GET方法）

    查询参数:
        video_url: 视频URL

    响应:
        {
            "success": true,
            "message": "解析成功",
            "data": {
                "title": "视频标题",
                "author": "作者名称",
                "videoUrl": "视频播放链接",
                "coverUrl": "封面图片链接",
                "description": "视频描述",
                "duration": "视频时长",
                "playCount": "播放次数",
                "likeCount": "点赞数",
                "commentCount": "评论数",
                "shareCount": "分享数"
            }
        }
    """
    try:
        video_url = request.args.get('video_url')

        # 验证必要参数
        if not video_url:
            return jsonify({
                "success": False,
                "message": "请提供视频URL",
                "data": None
            }), 400

        # 创建抖音爬虫
        spider = DouyinSpider()

        # 执行解析任务
        result = spider.execute({
            "video_url": video_url,
            "download": False
        })

        return jsonify(result), 200 if result["success"] else 500

    except Exception as e:
        logger.error(f"解析视频失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }), 500