#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志过滤器
用于过滤日志中的敏感信息，防止IP地址、端口等信息泄露
"""

import re
import logging
from typing import Optional

class SensitiveInfoFilter(logging.Filter):
    """敏感信息过滤器"""
    
    def __init__(self, name: str = ""):
        super().__init__(name)
        
        # 编译正则表达式以提高性能
        self.patterns = [
            # IP地址模式 (IPv4)
            (re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'), '[IP_HIDDEN]'),
            
            # 端口号模式
            (re.compile(r':(?:80|443|8080|8000|5000|3000|9000|8888)\b'), ':[PORT]'),
            
            # localhost和127.0.0.1
            (re.compile(r'\blocalhost\b', re.IGNORECASE), '[LOCAL]'),
            (re.compile(r'\b127\.0\.0\.1\b'), '[LOCAL]'),
            
            # 完整的URL模式
            (re.compile(r'https?://[^\s/$.?#].[^\s]*'), '[URL_HIDDEN]'),
            
            # 文件路径模式 (Windows)
            (re.compile(r'[A-Za-z]:\\[^\\/:*?"<>|\r\n]+'), '[PATH_HIDDEN]'),
            
            # 文件路径模式 (Linux/Unix)
            (re.compile(r'/[^/:*?"<>|\r\n\s]+'), '[PATH_HIDDEN]'),
            
            # 密码和密钥模式
            (re.compile(r'password["\']?\s*[:=]\s*["\']?[^"\'\s]+', re.IGNORECASE), 'password=[HIDDEN]'),
            (re.compile(r'token["\']?\s*[:=]\s*["\']?[^"\'\s]+', re.IGNORECASE), 'token=[HIDDEN]'),
            (re.compile(r'key["\']?\s*[:=]\s*["\']?[^"\'\s]+', re.IGNORECASE), 'key=[HIDDEN]'),
            
            # 数据库连接字符串
            (re.compile(r'mysql://[^@]+@[^/]+/\w+'), 'mysql://[USER]:[PASS]@[HOST]/[DB]'),
            (re.compile(r'postgresql://[^@]+@[^/]+/\w+'), 'postgresql://[USER]:[PASS]@[HOST]/[DB]'),
        ]
    
    def filter(self, record: logging.LogRecord) -> bool:
        """
        过滤日志记录中的敏感信息
        
        Args:
            record: 日志记录对象
            
        Returns:
            bool: 是否允许记录此日志
        """
        if hasattr(record, 'msg') and record.msg:
            # 过滤消息内容
            record.msg = self._sanitize_message(str(record.msg))
        
        if hasattr(record, 'args') and record.args:
            # 过滤参数
            sanitized_args = []
            for arg in record.args:
                if isinstance(arg, str):
                    sanitized_args.append(self._sanitize_message(arg))
                else:
                    sanitized_args.append(arg)
            record.args = tuple(sanitized_args)
        
        return True
    
    def _sanitize_message(self, message: str) -> str:
        """
        清理消息中的敏感信息
        
        Args:
            message: 原始消息
            
        Returns:
            str: 清理后的消息
        """
        if not message:
            return message
        
        # 应用所有过滤模式
        for pattern, replacement in self.patterns:
            message = pattern.sub(replacement, message)
        
        return message

class ProductionLogFilter(SensitiveInfoFilter):
    """生产环境日志过滤器"""
    
    def __init__(self, name: str = ""):
        super().__init__(name)
        
        # 生产环境额外的过滤模式
        additional_patterns = [
            # 更严格的路径过滤
            (re.compile(r'/[a-zA-Z0-9_\-./]+'), '[PATH]'),
            
            # 用户名模式
            (re.compile(r'user["\']?\s*[:=]\s*["\']?[^"\'\s]+', re.IGNORECASE), 'user=[HIDDEN]'),
            
            # 邮箱地址
            (re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'), '[EMAIL_HIDDEN]'),
            
            # 手机号
            (re.compile(r'\b1[3-9]\d{9}\b'), '[PHONE_HIDDEN]'),
        ]
        
        self.patterns.extend(additional_patterns)

def setup_log_filters(logger_instance: Optional[logging.Logger] = None, production: bool = False):
    """
    为日志记录器设置敏感信息过滤器
    
    Args:
        logger_instance: 日志记录器实例，如果为None则使用根记录器
        production: 是否为生产环境
    """
    if logger_instance is None:
        logger_instance = logging.getLogger()
    
    # 选择合适的过滤器
    filter_class = ProductionLogFilter if production else SensitiveInfoFilter
    sensitive_filter = filter_class("sensitive_info_filter")
    
    # 添加过滤器到所有处理器
    for handler in logger_instance.handlers:
        handler.addFilter(sensitive_filter)
    
    # 如果没有处理器，添加到记录器本身
    if not logger_instance.handlers:
        logger_instance.addFilter(sensitive_filter)

def create_safe_logger(name: str, level: int = logging.INFO, production: bool = False) -> logging.Logger:
    """
    创建一个安全的日志记录器（已配置敏感信息过滤）
    
    Args:
        name: 记录器名称
        level: 日志级别
        production: 是否为生产环境
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 如果还没有处理器，添加一个控制台处理器
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    # 设置过滤器
    setup_log_filters(logger, production)
    
    return logger

# 测试函数
def test_filter():
    """测试过滤器功能"""
    filter_instance = SensitiveInfoFilter()
    
    test_messages = [
        "连接到 http://localhost:5000/api/test",
        "数据库连接: mysql://user:password@127.0.0.1:3306/database",
        "用户IP: *************",
        "文件路径: /home/<USER>/project/file.py",
        "Windows路径: C:\\Users\\<USER>\\project\\file.py",
        "API密钥: token=abc123def456",
        "用户邮箱: <EMAIL>",
        "手机号: 13800138000",
    ]
    
    print("敏感信息过滤测试:")
    print("=" * 50)
    
    for message in test_messages:
        filtered = filter_instance._sanitize_message(message)
        print(f"原始: {message}")
        print(f"过滤: {filtered}")
        print("-" * 30)

if __name__ == "__main__":
    test_filter()
