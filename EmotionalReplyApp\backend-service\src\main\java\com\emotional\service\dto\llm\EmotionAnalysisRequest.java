package com.emotional.service.dto.llm;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 情感分析请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmotionAnalysisRequest {
    
    /**
     * 要分析的消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    @Size(max = 2000, message = "消息内容不能超过2000个字符")
    private String message;
    
    /**
     * 用户ID（用于个性化分析）
     */
    private Long userId;
    
    /**
     * 分析语言
     */
    private String language = "zh-CN";
    
    /**
     * 温度参数（控制分析的创造性，0.0-1.0）
     */
    private Double temperature = 0.3;
    
    /**
     * 是否需要详细分析
     */
    private Boolean detailed = false;
    
    /**
     * 上下文信息
     */
    private String context;
    
    /**
     * 分析维度
     */
    private List<String> dimensions;

    /**
     * 对方性别（用于生成更合适的回复建议）
     */
    private String senderGender;
}
