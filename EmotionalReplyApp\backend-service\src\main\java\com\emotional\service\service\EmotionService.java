package com.emotional.service.service;

import com.emotional.service.dto.EmotionAnalyzeRequest;
import com.emotional.service.dto.EmotionAnalyzeResponse;

import java.util.Map;

/**
 * 情感分析服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface EmotionService {
    
    /**
     * 分析情感并生成回复
     * 
     * @param request 分析请求
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @return 分析结果
     */
    EmotionAnalyzeResponse analyzeAndGenerateReply(EmotionAnalyzeRequest request, 
                                                  String clientIp, 
                                                  String userAgent);
    
    /**
     * 重新生成回复
     * 
     * @param historyId 历史记录ID
     * @param request 重新生成请求
     * @return 新的分析结果
     */
    EmotionAnalyzeResponse regenerateReply(Long historyId, EmotionAnalyzeRequest request);
    
    /**
     * 获取支持的回复风格
     * 
     * @return 回复风格列表
     */
    Map<String, String> getReplyStyles();
    
    /**
     * 单独进行情感分析
     * 
     * @param message 消息内容
     * @return 情感分析结果
     */
    EmotionAnalyzeResponse.EmotionResult analyzeEmotion(String message);
    
    /**
     * 根据情感生成回复
     * 
     * @param message 原始消息
     * @param emotionResult 情感分析结果
     * @param replyStyles 指定的回复风格
     * @return 回复选项列表
     */
    java.util.List<EmotionAnalyzeResponse.ReplyOption> generateReplies(
        String message, 
        EmotionAnalyzeResponse.EmotionResult emotionResult,
        java.util.List<String> replyStyles);
}
