# 数据库更新指南 - 回复风格名称修改

## 📋 更新概述

本次更新主要修改回复风格的显示名称：
- `humorous`: `逗比` → `玩梗`
- `literary`: `发表文学` → `文艺风格`
- `detailed`: `话痨延申` → `话痨风格`

## 🚀 执行步骤

### 方法一：使用更新脚本（推荐）

1. **备份数据库**
```bash
mysqldump -u root -p emotional_reply_db > backup_$(date +%Y%m%d_%H%M%S).sql
```

2. **执行更新脚本**
```bash
mysql -u root -p emotional_reply_db < update_reply_styles.sql
```

3. **验证更新结果**
```sql
SELECT config_value FROM system_config WHERE config_key = 'supported_reply_styles';
```

### 方法二：手动执行SQL命令

1. **连接数据库**
```bash
mysql -u root -p
USE emotional_reply_db;
```

2. **执行更新命令**
```sql
UPDATE system_config 
SET config_value = '{"warm_caring": "暖男", "humorous": "玩梗", "romantic": "撩妹", "high_eq": "高情商", "direct": "直接", "mature": "成熟稳重", "gentle": "温柔大叔", "dominant": "霸道总裁", "literary": "文艺风格", "detailed": "话痨风格"}'
WHERE config_key = 'supported_reply_styles';
```

3. **验证更新**
```sql
SELECT config_key, config_value FROM system_config WHERE config_key = 'supported_reply_styles';
```

## 📊 影响的表和字段

### 1. system_config 表
- **字段**: `config_value`
- **条件**: `config_key = 'supported_reply_styles'`
- **影响**: 更新风格配置JSON中的显示名称

### 2. user_settings 表
- **字段**: `reply_styles`
- **影响**: 存储的是风格代码，通常不需要更新
- **注意**: 如果存储了显示名称，需要相应更新

### 3. reply_history 表
- **影响**: 存储的是风格代码，通常不需要更新
- **注意**: 历史记录保持不变，新的显示名称会在前端展示时应用

## ✅ 验证检查

### 1. 检查系统配置
```sql
SELECT 
    config_key,
    config_value,
    config_desc
FROM system_config 
WHERE config_key = 'supported_reply_styles';
```

### 2. 检查用户设置
```sql
SELECT 
    user_id,
    reply_styles,
    updated_time
FROM user_settings 
WHERE reply_styles IS NOT NULL
LIMIT 5;
```

### 3. 检查历史记录
```sql
SELECT 
    id,
    reply_styles,
    created_time
FROM reply_history 
WHERE reply_styles IS NOT NULL
ORDER BY created_time DESC
LIMIT 5;
```

## 🔄 回滚方案

如果需要回滚到原来的名称：

```sql
UPDATE system_config 
SET config_value = '{"warm_caring": "暖男", "humorous": "逗比", "romantic": "撩妹", "high_eq": "高情商", "direct": "直接", "mature": "成熟稳重", "gentle": "温柔大叔", "dominant": "霸道总裁", "literary": "发表文学", "detailed": "话痨延申"}'
WHERE config_key = 'supported_reply_styles';
```

## ⚠️ 注意事项

1. **备份重要性**: 执行前务必备份数据库
2. **应用重启**: 更新后建议重启应用服务
3. **缓存清理**: 如果使用了缓存，需要清理相关缓存
4. **前端更新**: 确保前端代码也已更新对应的显示名称

## 🔧 故障排除

### 问题1: 权限不足
```
ERROR 1142 (42000): UPDATE command denied to user
```
**解决**: 确保数据库用户有UPDATE权限

### 问题2: 表不存在
```
ERROR 1146 (42S02): Table 'emotional_reply_db.system_config' doesn't exist
```
**解决**: 检查数据库名称和表名是否正确

### 问题3: JSON格式错误
**解决**: 检查JSON字符串格式是否正确，注意引号和转义字符
