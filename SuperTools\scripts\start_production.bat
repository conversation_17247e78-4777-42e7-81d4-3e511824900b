@echo off
REM SuperTools 生产环境启动脚本 (Windows)
REM 使用此脚本在生产环境中安全启动应用

echo 🚀 SuperTools 生产环境启动脚本
echo ==================================

REM 检查是否存在生产环境配置文件
if not exist "scripts\production.env" (
    echo ❌ 未找到生产环境配置文件 scripts\production.env
    echo 请先复制并配置 scripts\production.env 文件
    pause
    exit /b 1
)

REM 加载生产环境配置
echo 📋 加载生产环境配置...
for /f "usebackq tokens=1,2 delims==" %%a in ("scripts\production.env") do (
    if not "%%a"=="" if not "%%a:~0,1%"=="#" (
        set "%%a=%%b"
    )
)

REM 设置生产环境标识
set FLASK_ENV=production
set PRODUCTION=true
set DEBUG=False

echo ✅ 环境配置加载完成

REM 检查Python依赖
echo 📦 检查Python依赖...
python -c "import flask, mysql.connector, requests" 2>nul
if errorlevel 1 (
    echo ❌ 缺少必要的Python依赖
    echo 请运行: pip install -r requirements.txt
    pause
    exit /b 1
)

REM 创建必要的目录
echo 📁 创建必要的目录...
if not exist "logs" mkdir logs
if not exist "data" mkdir data

REM 启动应用
echo 🚀 启动SuperTools应用...
echo ==================================
echo 🌐 生产环境模式
echo 🔒 调试模式已禁用
echo 🛡️  服务器信息已隐藏
echo ==================================

REM 检查是否安装了gunicorn
python -c "import gunicorn" 2>nul
if not errorlevel 1 (
    echo 使用 Gunicorn 启动应用...
    gunicorn -w 4 -b %WEB_HOST%:%WEB_PORT% --timeout 120 backend.main:app
) else (
    echo 使用 Flask 内置服务器启动应用...
    echo ⚠️  建议在生产环境中使用 Gunicorn: pip install gunicorn
    python run.py --host %WEB_HOST% --port %WEB_PORT%
)

pause
