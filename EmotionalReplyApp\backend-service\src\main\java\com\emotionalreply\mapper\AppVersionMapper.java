package com.emotionalreply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.emotionalreply.entity.AppVersion;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 应用版本数据访问层
 */
@Mapper
public interface AppVersionMapper extends BaseMapper<AppVersion> {

    /**
     * 获取指定平台的最新版本
     */
    @Select("SELECT * FROM app_versions WHERE platform = #{platform} AND status = 'published' ORDER BY version_code DESC LIMIT 1")
    AppVersion getLatestVersion(@Param("platform") String platform);

    /**
     * 获取版本历史列表
     */
    @Select("SELECT * FROM app_versions WHERE status = 'published' ORDER BY release_date DESC LIMIT #{size} OFFSET #{offset}")
    List<AppVersion> getVersionHistory(@Param("offset") int offset, @Param("size") int size);

    /**
     * 获取版本总数
     */
    @Select("SELECT COUNT(*) FROM app_versions WHERE status = 'published'")
    int getVersionCount();

    /**
     * 根据版本名称和平台获取版本信息
     */
    @Select("SELECT * FROM app_versions WHERE version_name = #{versionName} AND platform = #{platform} AND status = 'published'")
    AppVersion getVersionByNameAndPlatform(@Param("versionName") String versionName, @Param("platform") String platform);

    /**
     * 获取指定平台的所有已发布版本
     */
    @Select("SELECT * FROM app_versions WHERE platform = #{platform} AND status = 'published' ORDER BY version_code DESC")
    List<AppVersion> getAllVersionsByPlatform(@Param("platform") String platform);

    /**
     * 检查是否存在强制更新版本
     */
    @Select("SELECT * FROM app_versions WHERE platform = #{platform} AND status = 'published' AND is_force_update = true AND version_code > #{currentVersionCode} ORDER BY version_code ASC LIMIT 1")
    AppVersion getForceUpdateVersion(@Param("platform") String platform, @Param("currentVersionCode") int currentVersionCode);

    /**
     * 插入新版本
     */
    @Insert("INSERT INTO app_versions (version_name, version_code, platform, update_content, download_url, file_size, is_force_update, min_support_version, status, release_date, created_at, updated_at) " +
            "VALUES (#{versionName}, #{versionCode}, #{platform}, #{updateContent}, #{downloadUrl}, #{fileSize}, #{isForceUpdate}, #{minSupportVersion}, #{status}, #{releaseDate}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertVersion(AppVersion version);

    /**
     * 更新版本信息
     */
    @Update("UPDATE app_versions SET version_name = #{versionName}, version_code = #{versionCode}, platform = #{platform}, " +
            "update_content = #{updateContent}, download_url = #{downloadUrl}, file_size = #{fileSize}, " +
            "is_force_update = #{isForceUpdate}, min_support_version = #{minSupportVersion}, status = #{status}, " +
            "release_date = #{releaseDate}, updated_at = NOW() WHERE id = #{id}")
    int updateVersion(AppVersion version);

    /**
     * 删除版本（软删除）
     */
    @Update("UPDATE app_versions SET status = 'deleted', updated_at = NOW() WHERE id = #{id}")
    int deleteVersion(@Param("id") Long id);

    /**
     * 发布版本
     */
    @Update("UPDATE app_versions SET status = 'published', release_date = NOW(), updated_at = NOW() WHERE id = #{id}")
    int publishVersion(@Param("id") Long id);

    /**
     * 撤回版本
     */
    @Update("UPDATE app_versions SET status = 'draft', updated_at = NOW() WHERE id = #{id}")
    int unpublishVersion(@Param("id") Long id);
}
