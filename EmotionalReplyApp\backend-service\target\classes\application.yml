server:
  port: 8080
  # 配置服务器监听地址
  # 0.0.0.0 - 监听所有网络接口（推荐用于真机调试）
  # localhost/127.0.0.1 - 仅监听本地回环接口
  # 具体IP - 监听指定网络接口
  address: 0.0.0.0
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: emotional-reply-service
  
  # 数据源配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    
    # HikariCP连接池配置
    hikari:
      minimum-idle: 5                    # 最小空闲连接数
      maximum-pool-size: 20              # 最大连接池大小
      auto-commit: true                  # 自动提交
      idle-timeout: 30000               # 空闲连接超时时间(毫秒)
      pool-name: EmotionalReplyHikariCP # 连接池名称
      max-lifetime: 1800000             # 连接最大生命周期(毫秒)
      connection-timeout: 30000         # 连接超时时间(毫秒)
      connection-test-query: SELECT 1   # 连接测试查询

  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

  # 邮件配置
  mail:
    host: ${MAIL_HOST:smtp.qq.com}        # SMTP服务器地址
    port: ${MAIL_PORT:465}                # SMTP服务器端口(SSL)
    username: ${MAIL_USERNAME:<EMAIL>}           # 发送者邮箱地址
    password: ${MAIL_PASSWORD:qogcxxuxixebfbbg}           # QQ邮箱16位授权码
    default-encoding: UTF-8               # 邮件编码
    properties:
      mail:
        smtp:
          auth: true                      # 启用SMTP认证
          ssl:
            enable: true                  # 启用SSL加密
          socketFactory:
            port: 465                     # SSL端口
            class: javax.net.ssl.SSLSocketFactory
          connectiontimeout: 5000         # 连接超时时间(毫秒)
          timeout: 3000                   # 读取超时时间(毫秒)
          writetimeout: 5000              # 写入超时时间(毫秒)

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true  # 下划线转驼峰
    cache-enabled: false                # 关闭二级缓存
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # 打印SQL日志
  global-config:
    db-config:
      id-type: auto                     # 主键类型
      logic-delete-field: deleted       # 逻辑删除字段
      logic-delete-value: 1             # 逻辑删除值
      logic-not-delete-value: 0         # 逻辑未删除值
  mapper-locations: classpath*:mapper/**/*.xml

# 日志配置
logging:
  level:
    com.emotional.service: DEBUG
    com.baomidou.mybatisplus: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/emotional-reply-service.log
    max-size: 100MB
    max-history: 30

# Actuator监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  health:
    db:
      enabled: false  # 临时禁用数据库健康检查
    mail:
      enabled: false  # 临时禁用邮件健康检查

# SpringDoc OpenAPI配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true

# 应用自定义配置
app:
  # LLM配置
  llm:
    deepseek:
      enabled: ${DEEPSEEK_ENABLED:true}
      api-key: ${DEEPSEEK_API_KEY:***********************************}  # 从环境变量获取API密钥
      base-url: ${DEEPSEEK_BASE_URL:https://api.deepseek.com/v1}
      model: ${DEEPSEEK_MODEL:deepseek-chat}
      timeout: ${DEEPSEEK_TIMEOUT:30000}  # 30秒超时
      balance-warning-threshold: ${DEEPSEEK_BALANCE_WARNING:5.0}  # 余额警告阈值（人民币）
      balance-check-enabled: ${DEEPSEEK_BALANCE_CHECK:true}  # 是否启用余额检查

    
  # 用户配置
  user:
    max-daily-requests: 100             # 每日最大请求数
    vip-max-daily-requests: 1000        # VIP用户每日最大请求数
    
  # 安全配置
  security:
    jwt-secret: ${JWT_SECRET:emotional-reply-service-super-secure-jwt-secret-key-2025}
    jwt-expire: 86400                   # JWT过期时间(秒)
    
  # 文件存储配置
  file:
    upload-path: ${FILE_UPLOAD_PATH:/tmp/uploads}
    max-size: 10485760                  # 最大文件大小(字节)

  # 管理员配置
  admin:
    email: ${ADMIN_EMAIL:<EMAIL>}  # 管理员邮箱
    notification:
      enabled: ${ADMIN_NOTIFICATION_ENABLED:true}  # 是否启用管理员通知
      # 通知频率控制（秒）
      throttle:
        enabled: ${ADMIN_NOTIFICATION_THROTTLE_ENABLED:true}
        api-key-error: ${ADMIN_NOTIFICATION_THROTTLE_API_KEY:86400}    # API密钥错误：24小时
        balance-error: ${ADMIN_NOTIFICATION_THROTTLE_BALANCE:3600}     # 余额不足：1小时
        service-error: ${ADMIN_NOTIFICATION_THROTTLE_SERVICE:1800}     # 服务异常：30分钟

---
# 开发环境配置
spring:
  profiles: dev
  datasource:
    url: ************************************************************************************************************************************************************

logging:
  level:
    root: INFO
    com.emotional.service: DEBUG

---
# 测试环境配置
spring:
  profiles: test
  datasource:
    url: ************************************************************************************************************************************************************

---
# 生产环境配置
spring:
  profiles: prod
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:emotional_reply_db}?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10

logging:
  level:
    root: WARN
    com.emotional.service: INFO
  file:
    name: /var/log/emotional-reply-service.log
