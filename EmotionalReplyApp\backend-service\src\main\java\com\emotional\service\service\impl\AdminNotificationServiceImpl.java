package com.emotional.service.service.impl;

import com.emotional.service.service.AdminNotificationService;
import com.emotional.service.service.NotificationThrottleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 管理员通知服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminNotificationServiceImpl implements AdminNotificationService {

    private final JavaMailSender mailSender;
    private final NotificationThrottleService throttleService;
    
    @Value("${app.admin.email:<EMAIL>}")
    private String adminEmail;
    
    @Value("${app.admin.notification.enabled:true}")
    private boolean notificationEnabled;
    
    @Value("${spring.mail.username:}")
    private String fromEmail;
    
    @Override
    public void notifyApiKeyIssue(String errorType, String errorMessage, Long userId) {
        if (!notificationEnabled) {
            log.debug("管理员通知已禁用，跳过API密钥问题通知");
            return;
        }

        // 检查频率限制
        if (!throttleService.canSendApiKeyErrorNotification(errorType)) {
            log.debug("API密钥错误通知被频率限制，跳过发送: {}", errorType);
            return;
        }

        String subject = "【紧急】DeepSeek API密钥问题 - 情感回复助手";
        String content = buildApiKeyIssueContent(errorType, errorMessage, userId);

        sendNotification(subject, content);
        log.warn("已通知管理员API密钥问题: {}", errorMessage);
    }
    
    @Override
    public void notifyInsufficientBalance(String errorMessage, Long userId) {
        if (!notificationEnabled) {
            log.debug("管理员通知已禁用，跳过余额不足通知");
            return;
        }

        // 检查频率限制
        if (!throttleService.canSendBalanceErrorNotification()) {
            log.debug("余额不足通知被频率限制，跳过发送");
            return;
        }

        String subject = "【重要】DeepSeek API余额不足 - 情感回复助手";
        String content = buildBalanceIssueContent(errorMessage, userId);

        sendNotification(subject, content);
        log.warn("已通知管理员余额不足问题: {}", errorMessage);
    }
    
    @Override
    public void notifyServiceError(String errorMessage, Long userId) {
        if (!notificationEnabled) {
            log.debug("管理员通知已禁用，跳过服务异常通知");
            return;
        }

        // 检查频率限制 - 使用错误信息的前50个字符作为错误类型
        String errorType = errorMessage.length() > 50 ?
            errorMessage.substring(0, 50) : errorMessage;
        if (!throttleService.canSendServiceErrorNotification(errorType)) {
            log.debug("服务错误通知被频率限制，跳过发送: {}", errorType);
            return;
        }

        String subject = "【警告】DeepSeek服务异常 - 情感回复助手";
        String content = buildServiceErrorContent(errorMessage, userId);

        sendNotification(subject, content);
        log.warn("已通知管理员服务异常: {}", errorMessage);
    }
    
    /**
     * 发送通知邮件
     */
    private void sendNotification(String subject, String content) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(adminEmail);
            message.setSubject(subject);
            message.setText(content);
            
            mailSender.send(message);
            log.info("管理员通知邮件发送成功: {}", adminEmail);
            
        } catch (Exception e) {
            log.error("发送管理员通知邮件失败", e);
        }
    }
    
    /**
     * 构建API密钥问题通知内容
     */
    private String buildApiKeyIssueContent(String errorType, String errorMessage, Long userId) {
        StringBuilder content = new StringBuilder();
        content.append("尊敬的管理员，\n\n");
        content.append("情感回复助手系统检测到DeepSeek API密钥问题：\n\n");
        content.append("错误类型：").append(errorType).append("\n");
        content.append("错误信息：").append(errorMessage).append("\n");
        content.append("发生时间：").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        
        if (userId != null) {
            content.append("用户ID：").append(userId).append("\n");
        }
        
        content.append("\n请立即检查并更新DeepSeek API密钥配置。\n\n");
        content.append("可能的解决方案：\n");
        content.append("1. 检查API密钥是否正确配置\n");
        content.append("2. 验证API密钥是否已过期\n");
        content.append("3. 确认API密钥权限设置\n\n");
        content.append("系统将暂时无法提供AI情感分析服务，请尽快处理。\n\n");
        content.append("此致\n");
        content.append("情感回复助手系统");
        
        return content.toString();
    }
    
    /**
     * 构建余额不足通知内容
     */
    private String buildBalanceIssueContent(String errorMessage, Long userId) {
        StringBuilder content = new StringBuilder();
        content.append("尊敬的管理员，\n\n");
        content.append("情感回复助手系统检测到DeepSeek API余额不足：\n\n");
        content.append("错误信息：").append(errorMessage).append("\n");
        content.append("发生时间：").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        
        if (userId != null) {
            content.append("用户ID：").append(userId).append("\n");
        }
        
        content.append("\n请立即为DeepSeek账户充值。\n\n");
        content.append("处理步骤：\n");
        content.append("1. 登录DeepSeek控制台\n");
        content.append("2. 查看账户余额和使用情况\n");
        content.append("3. 进行账户充值\n");
        content.append("4. 考虑设置余额预警\n\n");
        content.append("系统将暂时无法提供AI情感分析服务，请尽快处理。\n\n");
        content.append("此致\n");
        content.append("情感回复助手系统");
        
        return content.toString();
    }
    
    /**
     * 构建服务异常通知内容
     */
    private String buildServiceErrorContent(String errorMessage, Long userId) {
        StringBuilder content = new StringBuilder();
        content.append("尊敬的管理员，\n\n");
        content.append("情感回复助手系统检测到DeepSeek服务异常：\n\n");
        content.append("错误信息：").append(errorMessage).append("\n");
        content.append("发生时间：").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        
        if (userId != null) {
            content.append("用户ID：").append(userId).append("\n");
        }
        
        content.append("\n请检查系统状态和配置。\n\n");
        content.append("排查步骤：\n");
        content.append("1. 检查网络连接\n");
        content.append("2. 验证DeepSeek服务状态\n");
        content.append("3. 查看系统日志\n");
        content.append("4. 检查API配置\n\n");
        content.append("如果问题持续，请联系技术支持。\n\n");
        content.append("此致\n");
        content.append("情感回复助手系统");
        
        return content.toString();
    }
}
