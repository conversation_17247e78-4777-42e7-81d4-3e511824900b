<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VIP账号管理 - SuperSpider</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .vip-management {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .vip-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .vip-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .accounts-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .accounts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        
        .account-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }
        
        .account-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .account-platform {
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
        }
        
        .account-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .status-banned { background: #f5c6cb; color: #721c24; }
        .status-expired { background: #fff3cd; color: #856404; }
        
        .account-info {
            margin: 10px 0;
        }
        
        .account-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 0.8em;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <div class="vip-management">
        <div class="vip-header">
            <h1><i class="fas fa-crown"></i> VIP账号管理</h1>
            <button class="btn btn-primary" onclick="showAddModal()">
                <i class="fas fa-plus"></i> 添加账号
            </button>
        </div>
        
        <!-- 统计信息 -->
        <div class="vip-stats">
            <div class="stat-card">
                <div class="stat-number" id="total-accounts">0</div>
                <div>总账号数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="active-accounts">0</div>
                <div>可用账号</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="csdn-accounts">0</div>
                <div>CSDN账号</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="daily-usage">0</div>
                <div>今日使用</div>
            </div>
        </div>
        
        <!-- 账号列表 -->
        <div class="accounts-table">
            <div class="table-header">
                <h3>账号列表</h3>
                <div>
                    <select id="platform-filter" onchange="filterAccounts()">
                        <option value="">所有平台</option>
                        <option value="csdn">CSDN</option>
                        <option value="zhihu">知乎</option>
                    </select>
                    <select id="status-filter" onchange="filterAccounts()">
                        <option value="">所有状态</option>
                        <option value="active">活跃</option>
                        <option value="inactive">不可用</option>
                        <option value="banned">被封禁</option>
                        <option value="expired">已过期</option>
                    </select>
                    <button class="btn btn-secondary" onclick="refreshAccounts()">
                        <i class="fas fa-refresh"></i> 刷新
                    </button>
                </div>
            </div>
            <div class="accounts-grid" id="accounts-grid">
                <!-- 账号卡片将在这里动态生成 -->
            </div>
        </div>
    </div>
    
    <!-- 添加/编辑账号模态框 -->
    <div id="account-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">添加VIP账号</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <form id="account-form">
                <input type="hidden" id="account-id">
                
                <div class="form-group">
                    <label class="form-label" for="platform">平台</label>
                    <select class="form-input" id="platform" required>
                        <option value="">选择平台</option>
                        <option value="csdn">CSDN</option>
                        <option value="zhihu">知乎</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="username">用户名</label>
                    <input type="text" class="form-input" id="username" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="password">密码</label>
                    <input type="password" class="form-input" id="password" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="email">邮箱</label>
                    <input type="email" class="form-input" id="email">
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="vip-level">VIP等级</label>
                    <input type="text" class="form-input" id="vip-level" placeholder="如：CSDN会员">
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="expire-date">到期时间</label>
                    <input type="datetime-local" class="form-input" id="expire-date">
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="daily-limit">每日限制</label>
                    <input type="number" class="form-input" id="daily-limit" value="100">
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="notes">备注</label>
                    <textarea class="form-input" id="notes" rows="3"></textarea>
                </div>
                
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='js/vip-accounts.js') }}"></script>
</body>
</html>
