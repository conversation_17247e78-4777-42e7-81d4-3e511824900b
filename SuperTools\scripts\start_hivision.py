#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HivisionIDPhotos服务启动脚本
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

def check_hivision_service():
    """检查HivisionIDPhotos服务是否运行"""
    try:
        response = requests.get('http://127.0.0.1:8080/docs', timeout=5)
        return response.status_code == 200
    except:
        return False

def start_hivision_service():
    """启动HivisionIDPhotos服务"""
    hivision_path = project_root.parent / 'HivisionIDPhotos-master'
    
    if not hivision_path.exists():
        print(f"❌ HivisionIDPhotos项目路径不存在: {hivision_path}")
        return False
    
    print(f"📁 HivisionIDPhotos路径: {hivision_path}")
    
    # 检查是否已安装依赖
    requirements_file = hivision_path / 'requirements.txt'
    if requirements_file.exists():
        print("📦 检查依赖...")
        try:
            # 检查主要依赖是否已安装
            import cv2
            import onnxruntime
            import numpy
            print("✅ 主要依赖已安装")
        except ImportError as e:
            print(f"❌ 缺少依赖: {e}")
            print("请先安装依赖: pip install -r HivisionIDPhotos-master/requirements.txt")
            return False
    
    # 检查模型文件
    weights_dir = hivision_path / 'hivision' / 'creator' / 'weights'
    if not weights_dir.exists():
        weights_dir.mkdir(parents=True, exist_ok=True)
        print("📁 创建模型权重目录")
    
    model_files = list(weights_dir.glob('*.onnx'))
    if not model_files:
        print("⚠️  未找到模型文件，将使用默认模型")
        print("建议下载模型文件以获得更好的效果")
    else:
        print(f"✅ 找到 {len(model_files)} 个模型文件")
    
    # 启动服务
    print("🚀 启动HivisionIDPhotos服务...")
    
    try:
        # 切换到HivisionIDPhotos目录
        os.chdir(hivision_path)
        
        # 启动API服务
        process = subprocess.Popen([
            sys.executable, 'deploy_api.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待服务启动
        print("⏳ 等待服务启动...")
        for i in range(30):  # 最多等待30秒
            time.sleep(1)
            if check_hivision_service():
                print("✅ HivisionIDPhotos服务启动成功!")
                print("🌐 服务地址: http://127.0.0.1:8080")
                return True
            print(f"   等待中... ({i+1}/30)")
        
        print("❌ 服务启动超时")
        process.terminate()
        return False
        
    except Exception as e:
        print(f"❌ 启动服务失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🎯 HivisionIDPhotos服务启动器")
    print("=" * 50)
    
    # 检查服务是否已经运行
    if check_hivision_service():
        print("✅ HivisionIDPhotos服务已在运行")
        print("🌐 服务地址: http://127.0.0.1:8080")
        return
    
    # 启动服务
    if start_hivision_service():
        print("\n🎉 服务启动完成!")
        print("💡 提示: 保持此窗口打开以维持服务运行")
        print("💡 按 Ctrl+C 停止服务")
        
        try:
            # 保持脚本运行
            while True:
                time.sleep(10)
                if not check_hivision_service():
                    print("⚠️  服务已停止")
                    break
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务...")
    else:
        print("\n❌ 服务启动失败")
        print("\n🔧 故障排除:")
        print("1. 确保已安装依赖: pip install -r HivisionIDPhotos-master/requirements.txt")
        print("2. 确保端口8080未被占用")
        print("3. 检查HivisionIDPhotos-master目录是否存在")

if __name__ == '__main__':
    main()
