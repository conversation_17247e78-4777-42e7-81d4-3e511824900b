package com.emotional.service.service;

import com.emotional.service.entity.VipActivationCode;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * VIP服务接口
 */
public interface VipService {

    /**
     * 激活VIP
     * @param userId 用户ID
     * @param activationCode 激活码
     * @return 激活结果
     */
    Map<String, Object> activateVip(Long userId, String activationCode);

    /**
     * 检查激活码是否有效
     * @param code 激活码
     * @return 是否有效
     */
    boolean isCodeValid(String code);

    /**
     * 生成激活码
     * @param type 类型
     * @param validDays 有效天数
     * @param creatorId 创建者ID
     * @param remark 备注
     * @return 激活码
     */
    String generateActivationCode(String type, Integer validDays, Long creatorId, String remark);

    /**
     * 获取用户VIP过期时间
     * @param userId 用户ID
     * @return 过期时间
     */
    LocalDateTime getUserVipExpireTime(Long userId);

    /**
     * 检查用户是否为VIP
     * @param userId 用户ID
     * @return 是否为VIP
     */
    boolean isUserVip(Long userId);
}
