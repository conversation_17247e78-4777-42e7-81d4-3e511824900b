package com.emotional.service.aspect;

import com.emotional.service.annotation.RequireAdmin;
import com.emotional.service.entity.User;
import com.emotional.service.service.UserService;
import com.emotional.service.utils.AdminUtils;
import com.emotional.service.utils.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * 管理员权限切面
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Aspect
@Component
@Slf4j
public class AdminAspect {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private JwtUtils jwtUtils;
    
    /**
     * 管理员权限检查切面
     * 
     * @param joinPoint 连接点
     * @param requireAdmin 权限注解
     * @return 方法执行结果
     * @throws Throwable 异常
     */
    @Around("@annotation(requireAdmin)")
    public Object checkAdminPermission(ProceedingJoinPoint joinPoint, RequireAdmin requireAdmin) throws Throwable {
        
        // 获取当前请求
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            log.error("无法获取当前请求上下文");
            throw new RuntimeException("系统错误：无法获取请求上下文");
        }
        
        HttpServletRequest request = attributes.getRequest();
        
        // 获取方法信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String methodName = method.getDeclaringClass().getSimpleName() + "." + method.getName();
        String functionDesc = requireAdmin.value().isEmpty() ? methodName : requireAdmin.value();
        
        try {
            // 从请求头获取token
            String token = getTokenFromRequest(request);
            if (token == null) {
                log.warn("访问管理功能 {} 时未提供认证token", functionDesc);
                throw new RuntimeException("未登录，请先登录");
            }
            
            // 验证token并获取用户ID
            Long userId = jwtUtils.getUserIdFromToken(token);
            if (userId == null) {
                log.warn("访问管理功能 {} 时token无效", functionDesc);
                throw new RuntimeException("登录已过期，请重新登录");
            }
            
            // 获取用户信息
            User user = userService.getById(userId);
            if (user == null) {
                log.warn("访问管理功能 {} 时用户不存在：{}", functionDesc, userId);
                throw new RuntimeException("用户不存在");
            }
            
            // 检查权限级别
            RequireAdmin.AdminLevel requiredLevel = requireAdmin.level();
            boolean hasPermission = false;
            
            switch (requiredLevel) {
                case ADMIN:
                    hasPermission = AdminUtils.isAdmin(user);
                    break;
                case SUPER_ADMIN:
                    hasPermission = AdminUtils.isSuperAdmin(user);
                    break;
            }
            
            if (!hasPermission) {
                String requiredLevelDesc = requiredLevel == RequireAdmin.AdminLevel.SUPER_ADMIN ? "超级管理员" : "管理员";
                log.warn("用户 {} 尝试访问需要{}权限的功能：{}", user.getUsername(), requiredLevelDesc, functionDesc);
                throw new RuntimeException("权限不足，需要" + requiredLevelDesc + "权限");
            }
            
            // 记录管理操作日志
            log.info("管理员 {} 访问功能：{}", user.getUsername(), functionDesc);
            
            // 执行原方法
            return joinPoint.proceed();
            
        } catch (Exception e) {
            log.error("管理员权限检查失败，功能：{}，错误：{}", functionDesc, e.getMessage());
            throw e;
        }
    }
    
    /**
     * 从请求中获取token
     * 
     * @param request HTTP请求
     * @return token字符串
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        // 从Authorization头获取
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        
        // 从token参数获取
        String tokenParam = request.getParameter("token");
        if (tokenParam != null && !tokenParam.isEmpty()) {
            return tokenParam;
        }
        
        // 从X-Token头获取
        String xToken = request.getHeader("X-Token");
        if (xToken != null && !xToken.isEmpty()) {
            return xToken;
        }
        
        return null;
    }
}
