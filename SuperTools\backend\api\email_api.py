#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
邮箱验证API
替换短信验证，使用邮箱发送验证码
"""

import logging
from flask import Blueprint, request, jsonify, session
from flask_login import login_user

logger = logging.getLogger(__name__)

# 创建蓝图
email_api = Blueprint('email_api', __name__)

@email_api.route('/send-register-email', methods=['POST'])
def send_register_email():
    """发送注册邮箱验证码"""
    data = request.get_json()

    if not data:
        return jsonify({
            "success": False,
            "message": "无效的请求数据",
            "data": None
        }), 400

    email = data.get('email', '').strip()

    # 参数验证
    if not email:
        return jsonify({
            "success": False,
            "message": "邮箱不能为空",
            "data": None
        }), 400

    # 验证邮箱格式
    import re
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        return jsonify({
            "success": False,
            "message": "请输入有效的邮箱地址",
            "data": None
        }), 400

    # 检查邮箱是否已注册
    from backend.models.user import User
    user = User.query.filter_by(email=email).first()
    if user:
        return jsonify({
            "success": False,
            "message": "该邮箱已被注册",
            "data": None
        }), 400

    try:
        # 使用邮箱验证服务
        from backend.utils.email_verification import email_verification_service
        
        result = email_verification_service.send_verification_code(email, 'register')
        
        if result['success']:
            return jsonify({
                "success": True,
                "message": result['message'],
                "data": {
                    "expire_minutes": result['expire_minutes'],
                    "dev_code": result.get('dev_code')  # 开发环境才有
                }
            })
        else:
            return jsonify({
                "success": False,
                "message": result['message'],
                "data": None
            }), 400

    except Exception as e:
        logger.error(f"发送注册验证码异常: {e}")
        return jsonify({
            "success": False,
            "message": "发送失败，请稍后再试",
            "data": None
        }), 500

@email_api.route('/send-login-email', methods=['POST'])
def send_login_email():
    """发送登录邮箱验证码"""
    data = request.get_json()

    if not data:
        return jsonify({
            "success": False,
            "message": "无效的请求数据",
            "data": None
        }), 400

    email = data.get('email', '').strip()

    # 参数验证
    if not email:
        return jsonify({
            "success": False,
            "message": "邮箱不能为空",
            "data": None
        }), 400

    # 验证邮箱格式
    import re
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        return jsonify({
            "success": False,
            "message": "请输入有效的邮箱地址",
            "data": None
        }), 400

    # 检查邮箱是否已注册
    from backend.models.user import User
    user = User.query.filter_by(email=email).first()
    if not user:
        return jsonify({
            "success": False,
            "message": "该邮箱未注册",
            "data": None
        }), 400

    try:
        # 使用邮箱验证服务
        from backend.utils.email_verification import email_verification_service
        
        result = email_verification_service.send_verification_code(email, 'login')
        
        if result['success']:
            return jsonify({
                "success": True,
                "message": result['message'],
                "data": {
                    "expire_minutes": result['expire_minutes'],
                    "dev_code": result.get('dev_code')  # 开发环境才有
                }
            })
        else:
            return jsonify({
                "success": False,
                "message": result['message'],
                "data": None
            }), 400

    except Exception as e:
        logger.error(f"发送登录验证码异常: {e}")
        return jsonify({
            "success": False,
            "message": "发送失败，请稍后再试",
            "data": None
        }), 500

@email_api.route('/email-login', methods=['POST'])
def email_login():
    """邮箱验证码登录"""
    data = request.get_json()

    if not data:
        return jsonify({
            "success": False,
            "message": "无效的请求数据",
            "data": None
        }), 400

    email = data.get('email', '').strip()
    email_code = data.get('email_code', '').strip()

    # 参数验证
    if not email or not email_code:
        return jsonify({
            "success": False,
            "message": "邮箱和验证码不能为空",
            "data": None
        }), 400

    # 验证邮箱格式
    import re
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        return jsonify({
            "success": False,
            "message": "请输入有效的邮箱地址",
            "data": None
        }), 400

    # 验证码长度检查
    if len(email_code) != 6:
        return jsonify({
            "success": False,
            "message": "请输入6位验证码",
            "data": None
        }), 400

    try:
        # 验证验证码
        from backend.utils.email_verification import email_verification_service
        
        verify_result = email_verification_service.verify_code(email, email_code, 'login')
        
        if not verify_result['success']:
            return jsonify({
                "success": False,
                "message": verify_result['message'],
                "data": None
            }), 400

        # 查找用户
        from backend.models.user import User
        from backend.main import db

        user = User.query.filter_by(email=email).first()
        if not user:
            return jsonify({
                "success": False,
                "message": "该邮箱未注册",
                "data": None
            }), 400

        # 检查用户状态
        if not user.is_active:
            return jsonify({
                "success": False,
                "message": "账户已被禁用，请联系管理员",
                "data": None
            }), 403

        # 登录用户
        login_user(user, remember=False)

        # 更新登录信息
        user.update_login_info()
        db.session.commit()

        return jsonify({
            "success": True,
            "message": "登录成功",
            "data": {
                "user_id": user.id,
                "username": user.username,
                "nickname": user.username,
                "role": user.role,
                "role_display": user.get_role_display(),
                "is_admin": user.is_admin,
                "vip_expire_date": user.vip_expire_date.isoformat() if user.vip_expire_date else None,
                "is_pro_valid": user.is_pro_user_valid()
            }
        })

    except Exception as e:
        logger.error(f"邮箱验证码登录异常: {e}")
        return jsonify({
            "success": False,
            "message": "登录失败，请稍后再试",
            "data": None
        }), 500

@email_api.route('/status', methods=['GET'])
def email_service_status():
    """获取邮箱验证服务状态"""
    try:
        from backend.utils.email_verification import email_verification_service
        
        status = email_verification_service.get_service_status()
        
        return jsonify({
            "success": True,
            "message": "状态获取成功",
            "data": status
        })
        
    except Exception as e:
        logger.error(f"获取邮箱服务状态失败: {e}")
        return jsonify({
            "success": False,
            "message": "获取状态失败",
            "data": None
        }), 500

@email_api.route('/verify', methods=['POST'])
def verify_email_code():
    """验证邮箱验证码（通用接口）"""
    data = request.get_json()

    if not data:
        return jsonify({
            "success": False,
            "message": "无效的请求数据",
            "data": None
        }), 400

    email = data.get('email', '').strip()
    code = data.get('code', '').strip()
    purpose = data.get('purpose', 'login').strip()

    # 参数验证
    if not email or not code:
        return jsonify({
            "success": False,
            "message": "邮箱和验证码不能为空",
            "data": None
        }), 400

    try:
        # 验证验证码
        from backend.utils.email_verification import email_verification_service
        
        result = email_verification_service.verify_code(email, code, purpose)
        
        return jsonify({
            "success": result['success'],
            "message": result['message'],
            "data": None
        })

    except Exception as e:
        logger.error(f"验证邮箱验证码异常: {e}")
        return jsonify({
            "success": False,
            "message": "验证失败，请稍后再试",
            "data": None
        }), 500
