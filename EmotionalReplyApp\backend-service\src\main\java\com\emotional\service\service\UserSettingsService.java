package com.emotional.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.emotional.service.entity.UserSettings;

import java.util.List;

/**
 * 用户设置服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface UserSettingsService extends IService<UserSettings> {
    
    /**
     * 根据用户ID获取用户设置
     * 
     * @param userId 用户ID
     * @return 用户设置
     */
    UserSettings getByUserId(Long userId);
    
    /**
     * 获取用户偏好的回复风格列表
     * 
     * @param userId 用户ID
     * @return 回复风格列表
     */
    List<String> getUserReplyStyles(Long userId);
    
    /**
     * 获取用户偏好的回复数量
     * 
     * @param userId 用户ID
     * @return 回复数量
     */
    Integer getUserPreferredReplyCount(Long userId);
    
    /**
     * 获取用户的回复生成模式
     * 
     * @param userId 用户ID
     * @return 生成模式
     */
    String getUserReplyGenerationMode(Long userId);
    
    /**
     * 获取用户的主要回复风格
     * 
     * @param userId 用户ID
     * @return 主要风格
     */
    String getUserPrimaryStyle(Long userId);
    
    /**
     * 更新用户设置
     * 
     * @param userId 用户ID
     * @param settings 设置信息
     * @return 是否成功
     */
    boolean updateUserSettings(Long userId, UserSettings settings);
    
    /**
     * 初始化用户默认设置
     * 
     * @param userId 用户ID
     * @return 默认设置
     */
    UserSettings initDefaultSettings(Long userId);
}
