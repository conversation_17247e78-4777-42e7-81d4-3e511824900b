#!/bin/bash
# SuperTools 生产环境启动脚本
# 使用此脚本在生产环境中安全启动应用

echo "🚀 SuperTools 生产环境启动脚本"
echo "=================================="

# 检查是否存在生产环境配置文件
if [ ! -f "scripts/production.env" ]; then
    echo "❌ 未找到生产环境配置文件 scripts/production.env"
    echo "请先复制并配置 scripts/production.env 文件"
    exit 1
fi

# 加载生产环境配置
echo "📋 加载生产环境配置..."
export $(cat scripts/production.env | grep -v '^#' | xargs)

# 验证必要的环境变量
echo "🔍 验证环境配置..."
required_vars=("MYSQL_USER" "MYSQL_PASSWORD" "MYSQL_HOST" "SECRET_KEY")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ 缺少必要的环境变量: $var"
        echo "请在 scripts/production.env 中配置此变量"
        exit 1
    fi
done

# 设置生产环境标识
export FLASK_ENV=production
export PRODUCTION=true

# 禁用调试模式
export DEBUG=False

echo "✅ 环境配置验证通过"

# 检查Python依赖
echo "📦 检查Python依赖..."
if ! python -c "import flask, mysql.connector, requests" 2>/dev/null; then
    echo "❌ 缺少必要的Python依赖"
    echo "请运行: pip install -r requirements.txt"
    exit 1
fi

# 检查数据库连接
echo "🗄️  检查数据库连接..."
python -c "
import mysql.connector
import os
try:
    conn = mysql.connector.connect(
        host=os.environ.get('MYSQL_HOST'),
        user=os.environ.get('MYSQL_USER'),
        password=os.environ.get('MYSQL_PASSWORD'),
        database=os.environ.get('MYSQL_DATABASE')
    )
    conn.close()
    print('✅ 数据库连接正常')
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs data

# 启动应用
echo "🚀 启动SuperTools应用..."
echo "=================================="
echo "🌐 生产环境模式"
echo "🔒 调试模式已禁用"
echo "🛡️  服务器信息已隐藏"
echo "=================================="

# 使用gunicorn启动（推荐生产环境）
if command -v gunicorn &> /dev/null; then
    echo "使用 Gunicorn 启动应用..."
    gunicorn -w 4 -b ${WEB_HOST:-0.0.0.0}:${WEB_PORT:-5000} --timeout 120 backend.main:app
else
    echo "使用 Flask 内置服务器启动应用..."
    echo "⚠️  建议在生产环境中使用 Gunicorn: pip install gunicorn"
    python run.py --host ${WEB_HOST:-0.0.0.0} --port ${WEB_PORT:-5000}
fi
