<template>
  <view class="version-test">
    <view class="header">
      <text class="title">版本管理测试页面</text>
    </view>
    
    <view class="actions">
      <button class="btn" @click="testLoadVersions">测试加载版本列表</button>
      <button class="btn" @click="testPublishVersion">测试发布版本</button>
    </view>
    
    <view class="result" v-if="result">
      <text class="result-title">测试结果:</text>
      <text class="result-content">{{ result }}</text>
    </view>
    
    <view class="versions" v-if="versions.length > 0">
      <text class="versions-title">版本列表:</text>
      <view class="version-item" v-for="version in versions" :key="version.id">
        <text>{{ version.versionName }} - {{ version.platform }} - {{ version.status }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { get, post } from '../../utils/request.js'

export default {
  name: 'VersionTest',
  data() {
    return {
      result: '',
      versions: []
    }
  },
  
  methods: {
    async testLoadVersions() {
      try {
        this.result = '正在加载版本列表...'

        // 使用顶层导入的函数
        const response = await get('/version/list', { page: 1, size: 10 })

        this.versions = response.data?.list || response.list || []
        this.result = `成功加载 ${this.versions.length} 个版本`
      } catch (error) {
        console.error('测试失败:', error)
        this.result = `加载失败: ${error.message}`
      }
    },
    
    async testPublishVersion() {
      try {
        this.result = '正在测试发布版本...'

        const testVersion = {
          versionName: '1.0.1',
          versionCode: 101,
          platform: 'android',
          downloadUrl: 'https://example.com/app.apk',
          fileSize: '5.2MB',
          minSupportVersion: '1.0.0',
          isForceUpdate: false,
          updateContent: '测试版本发布功能'
        }

        const response = await post('/version/publish', testVersion)
        this.result = '版本发布测试成功'

        // 重新加载版本列表
        this.testLoadVersions()
      } catch (error) {
        console.error('发布测试失败:', error)
        this.result = `发布失败: ${error.message}`
      }
    }
  }
}
</script>

<style scoped>
.version-test {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.btn {
  padding: 30rpx;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.result {
  background-color: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 40rpx;
}

.result-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.result-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.versions {
  background-color: white;
  padding: 30rpx;
  border-radius: 10rpx;
}

.versions-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.version-item {
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  font-size: 26rpx;
  color: #666;
}
</style>
