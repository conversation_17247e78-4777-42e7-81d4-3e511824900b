/* 全局样式 */

/* 重置样式 */
* {
  box-sizing: border-box;
}

page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

/* 通用类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 颜色类 */
.text-primary {
  color: #2196F3;
}

.text-success {
  color: #4CAF50;
}

.text-warning {
  color: #FF9800;
}

.text-danger {
  color: #F44336;
}

.text-muted {
  color: #666;
}

/* 背景色类 */
.bg-primary {
  background-color: #2196F3;
}

.bg-white {
  background-color: #fff;
}

.bg-light {
  background-color: #f8f9fa;
}

/* 间距类 */
.m-0 { margin: 0; }
.m-1 { margin: 8rpx; }
.m-2 { margin: 16rpx; }
.m-3 { margin: 24rpx; }
.m-4 { margin: 32rpx; }
.m-5 { margin: 40rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 8rpx; }
.p-2 { padding: 16rpx; }
.p-3 { padding: 24rpx; }
.p-4 { padding: 32rpx; }
.p-5 { padding: 40rpx; }

/* 圆角类 */
.rounded {
  border-radius: 8rpx;
}

.rounded-lg {
  border-radius: 16rpx;
}

.rounded-xl {
  border-radius: 24rpx;
}

.rounded-full {
  border-radius: 50%;
}

/* 阴影类 */
.shadow-sm {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.shadow {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #2196F3, #21CBF3);
  color: white;
}

.btn-secondary {
  background: #f0f0f0;
  color: #333;
}

.btn-outline {
  background: transparent;
  border: 2rpx solid #2196F3;
  color: #2196F3;
}

.btn-sm {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-lg {
  padding: 20rpx 40rpx;
  font-size: 32rpx;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.card-body {
  padding: 20rpx 0;
}

/* 列表样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 24rpx;
  color: #666;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.tag-primary {
  background: rgba(33, 150, 243, 0.1);
  color: #2196F3;
}

.tag-success {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.tag-warning {
  background: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.tag-danger {
  background: rgba(244, 67, 54, 0.1);
  color: #F44336;
}

/* 输入框样式 */
.input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fafafa;
}

.input:focus {
  border-color: #2196F3;
  background-color: white;
}

/* 加载动画 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #2196F3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  color: #ccc;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
}

/* 响应式 */
@media (max-width: 750rpx) {
  .container {
    padding: 16rpx;
  }
  
  .card {
    padding: 24rpx;
  }
}
