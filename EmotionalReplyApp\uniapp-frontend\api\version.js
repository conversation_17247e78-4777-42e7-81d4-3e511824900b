import { get, post, request } from '../utils/request.js'

/**
 * 版本管理相关API
 */

/**
 * 检查应用更新
 * @param {string} currentVersion - 当前版本号
 * @param {string} platform - 平台类型 (android/ios/h5)
 * @returns {Promise} 更新检查结果
 */
export const checkUpdate = (currentVersion, platform = 'h5') => {
  return get('/version/check', {
    currentVersion,
    platform
  })
}

/**
 * 获取最新版本信息
 * @param {string} platform - 平台类型
 * @returns {Promise} 最新版本信息
 */
export const getLatestVersion = (platform = 'h5') => {
  return get('/version/latest', {
    platform
  })
}

/**
 * 获取版本更新历史
 * @param {number} page - 页码
 * @param {number} size - 每页数量
 * @returns {Promise} 版本历史列表
 */
export const getVersionHistory = (page = 1, size = 10) => {
  return get('/version/history', {
    page,
    size
  })
}

/**
 * 记录用户更新行为
 * @param {object} logData - 日志数据
 * @returns {Promise} 记录结果
 */
export const logUpdateAction = (logData) => {
  return post('/version/log', logData)
}

// ==================== 管理端API ====================

/**
 * 获取版本列表（管理端）
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页数量
 * @param {string} params.platform - 平台筛选
 * @param {string} params.status - 状态筛选
 * @returns {Promise} 版本列表
 */
export const getVersionList = (params = {}) => {
  const {
    page = 1,
    size = 10,
    platform = '',
    status = ''
  } = params

  const queryParams = { page, size }
  if (platform) queryParams.platform = platform
  if (status) queryParams.status = status

  return get('/version/list', queryParams)
}

/**
 * 发布新版本（管理端）
 * @param {Object} versionData - 版本数据
 * @returns {Promise} 发布结果
 */
export const publishVersion = (versionData) => {
  return post('/version/publish', versionData)
}

/**
 * 下架版本（管理端）
 * @param {number} id - 版本ID
 * @returns {Promise} 下架结果
 */
export const unpublishVersion = (id) => {
  return request({
    url: `/version/unpublish/${id}`,
    method: 'PUT'
  })
}

/**
 * 删除版本（管理端）
 * @param {number} id - 版本ID
 * @returns {Promise} 删除结果
 */
export const deleteVersion = (id) => {
  return request({
    url: `/version/${id}`,
    method: 'DELETE'
  })
}

/**
 * 获取版本详情（管理端）
 * @param {number} id - 版本ID
 * @returns {Promise} 版本详情
 */
export const getVersionDetail = (id) => {
  return get(`/version/info/${id}`)
}

/**
 * 获取当前应用版本信息
 * @returns {object} 版本信息
 */
export const getCurrentVersion = () => {
  // 从manifest.json获取版本信息
  const systemInfo = uni.getSystemInfoSync()
  
  // 获取平台类型
  let platform = 'h5'
  if (systemInfo.platform === 'android') {
    platform = 'android'
  } else if (systemInfo.platform === 'ios') {
    platform = 'ios'
  }
  
  // 获取版本号
  let versionName = '1.0.0'
  let versionCode = 100
  
  // #ifdef APP-PLUS
  // 在App环境下可以获取真实版本信息
  const appInfo = plus.runtime.getProperty(plus.runtime.appid, (info) => {
    versionName = info.version
    versionCode = parseInt(info.versionCode) || 100
  })
  // #endif
  
  // #ifdef H5
  // H5环境下从配置获取
  versionName = process.env.VUE_APP_VERSION || '1.0.0'
  // #endif
  
  return {
    versionName,
    versionCode,
    platform,
    systemInfo
  }
}

/**
 * 比较版本号
 * @param {string} version1 - 版本1
 * @param {string} version2 - 版本2
 * @returns {number} 比较结果：-1表示version1 < version2，0表示相等，1表示version1 > version2
 */
export const compareVersions = (version1, version2) => {
  if (!version1 || !version2) {
    return 0
  }
  
  const v1Parts = version1.split('.').map(Number)
  const v2Parts = version2.split('.').map(Number)
  
  const maxLength = Math.max(v1Parts.length, v2Parts.length)
  
  for (let i = 0; i < maxLength; i++) {
    const v1Part = v1Parts[i] || 0
    const v2Part = v2Parts[i] || 0
    
    if (v1Part !== v2Part) {
      return v1Part < v2Part ? -1 : 1
    }
  }
  
  return 0
}

/**
 * 格式化文件大小
 * @param {string} size - 文件大小字符串
 * @returns {string} 格式化后的大小
 */
export const formatFileSize = (size) => {
  if (!size) return '未知'
  
  // 如果已经是格式化的字符串，直接返回
  if (typeof size === 'string' && /[KMGT]B/i.test(size)) {
    return size
  }
  
  // 如果是数字，进行格式化
  const bytes = parseInt(size)
  if (isNaN(bytes)) return size
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let unitIndex = 0
  let fileSize = bytes
  
  while (fileSize >= 1024 && unitIndex < units.length - 1) {
    fileSize /= 1024
    unitIndex++
  }
  
  return `${fileSize.toFixed(1)}${units[unitIndex]}`
}

/**
 * 获取平台显示名称
 * @param {string} platform - 平台代码
 * @returns {string} 平台显示名称
 */
export const getPlatformName = (platform) => {
  const platformNames = {
    'android': 'Android',
    'ios': 'iOS',
    'h5': '网页版'
  }
  return platformNames[platform] || platform
}

export default {
  checkUpdate,
  getLatestVersion,
  getVersionHistory,
  logUpdateAction,
  getCurrentVersion,
  compareVersions,
  formatFileSize,
  getPlatformName,
  // 管理端方法
  getVersionList,
  publishVersion,
  unpublishVersion,
  deleteVersion,
  getVersionDetail
}
