package com.emotional.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 邮件配置类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Configuration
@ConfigurationProperties(prefix = "spring.mail")
@Data
public class MailConfig {
    
    /**
     * SMTP服务器地址
     */
    private String host;
    
    /**
     * SMTP服务器端口
     */
    private Integer port;
    
    /**
     * 发送者邮箱
     */
    private String username;
    
    /**
     * 发送者邮箱密码或授权码
     */
    private String password;
    
    /**
     * 邮件编码
     */
    private String defaultEncoding = "UTF-8";
}
