package com.emotional.service.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 用户注册请求DTO
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@Schema(description = "用户注册请求")
public class UserRegisterRequest {
    
    @Schema(description = "用户名", example = "testuser")
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;
    
    @Schema(description = "昵称", example = "测试用户")
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;
    
    @Schema(description = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;
    
    @Schema(description = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    @Schema(description = "密码", example = "password123")
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;
    
    @Schema(description = "确认密码", example = "password123")
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;
    
    @Schema(description = "验证码", example = "123456")
    private String captcha;
    
    @Schema(description = "短信验证码", example = "123456")
    private String smsCode;
    
    @Schema(description = "邀请码", example = "INVITE123")
    private String inviteCode;
    
    @Schema(description = "是否同意用户协议", example = "true")
    private Boolean agreeTerms;
    
    /**
     * 验证密码是否一致
     * 
     * @return 是否一致
     */
    public boolean isPasswordMatch() {
        return password != null && password.equals(confirmPassword);
    }
    
    /**
     * 获取显示用的昵称（如果昵称为空则使用用户名）
     * 
     * @return 显示昵称
     */
    public String getDisplayNickname() {
        return (nickname != null && !nickname.trim().isEmpty()) ? nickname.trim() : username;
    }
    
    /**
     * 检查是否提供了联系方式（邮箱或手机号）
     * 
     * @return 是否提供了联系方式
     */
    public boolean hasContactInfo() {
        return (email != null && !email.trim().isEmpty()) || 
               (phone != null && !phone.trim().isEmpty());
    }
}
