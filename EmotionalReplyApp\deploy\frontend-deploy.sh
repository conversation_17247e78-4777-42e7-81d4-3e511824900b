#!/bin/bash

# 前端部署脚本
# 使用方法: ./frontend-deploy.sh [环境] [平台]
# 示例: ./frontend-deploy.sh prod h5

ENVIRONMENT=${1:-dev}
PLATFORM=${2:-h5}
WEB_ROOT="/var/www/emotional-reply"
BACKUP_DIR="/var/backups/emotional-reply"

echo "🚀 开始部署前端应用..."
echo "环境: $ENVIRONMENT"
echo "平台: $PLATFORM"

# 进入前端目录
cd uniapp-frontend

# 1. 安装依赖
echo "📦 安装依赖..."
npm install

# 2. 构建应用
echo "🔨 构建应用..."
case $PLATFORM in
    "h5")
        npm run build:h5
        BUILD_DIR="dist/build/h5"
        DEPLOY_DIR="$WEB_ROOT/h5"
        ;;
    "app")
        echo "📱 构建App版本需要在HBuilderX中进行云打包"
        echo "请按以下步骤操作:"
        echo "1. 在HBuilderX中打开项目"
        echo "2. 选择 发行 -> 原生App-云打包"
        echo "3. 配置签名证书和应用信息"
        echo "4. 提交云端打包"
        exit 0
        ;;
    "mp-weixin")
        npm run build:mp-weixin
        BUILD_DIR="dist/build/mp-weixin"
        echo "📱 微信小程序构建完成，请手动上传到微信开发者工具"
        exit 0
        ;;
    *)
        echo "❌ 不支持的平台: $PLATFORM"
        echo "支持的平台: h5, app, mp-weixin"
        exit 1
        ;;
esac

# 3. 检查构建结果
if [ ! -d "$BUILD_DIR" ]; then
    echo "❌ 构建失败，找不到构建目录: $BUILD_DIR"
    exit 1
fi

# 4. 创建部署目录
sudo mkdir -p "$DEPLOY_DIR"
sudo mkdir -p "$BACKUP_DIR"

# 5. 备份当前版本
if [ -d "$DEPLOY_DIR" ] && [ "$(ls -A $DEPLOY_DIR)" ]; then
    echo "📦 备份当前版本..."
    BACKUP_NAME="backup_$(date +%Y%m%d_%H%M%S)"
    sudo cp -r "$DEPLOY_DIR" "$BACKUP_DIR/$BACKUP_NAME"
    echo "备份保存到: $BACKUP_DIR/$BACKUP_NAME"
fi

# 6. 部署新版本
echo "📁 部署新版本..."
sudo rm -rf "$DEPLOY_DIR"/*
sudo cp -r "$BUILD_DIR"/* "$DEPLOY_DIR/"

# 7. 设置权限
sudo chown -R www-data:www-data "$DEPLOY_DIR"
sudo chmod -R 755 "$DEPLOY_DIR"

# 8. 重载Nginx配置
echo "🔄 重载Nginx配置..."
sudo nginx -t
if [ $? -eq 0 ]; then
    sudo systemctl reload nginx
    echo "✅ Nginx配置重载成功"
else
    echo "❌ Nginx配置检查失败"
    exit 1
fi

# 9. 健康检查
echo "🔍 健康检查..."
sleep 3
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/)
if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ 前端应用部署成功!"
    echo "🌐 访问地址: http://your-domain.com"
else
    echo "❌ 健康检查失败，HTTP状态码: $HTTP_CODE"
    
    # 回滚到备份版本
    if [ -d "$BACKUP_DIR/$BACKUP_NAME" ]; then
        echo "🔄 回滚到备份版本..."
        sudo rm -rf "$DEPLOY_DIR"/*
        sudo cp -r "$BACKUP_DIR/$BACKUP_NAME"/* "$DEPLOY_DIR/"
        sudo systemctl reload nginx
        echo "✅ 回滚完成"
    fi
    exit 1
fi

# 10. 清理旧备份 (保留最近5个)
echo "🧹 清理旧备份..."
cd "$BACKUP_DIR"
ls -t | tail -n +6 | xargs -r sudo rm -rf

echo "🎉 前端部署完成!"
echo "📊 部署信息:"
echo "  - 环境: $ENVIRONMENT"
echo "  - 平台: $PLATFORM"
echo "  - 部署目录: $DEPLOY_DIR"
echo "  - 备份目录: $BACKUP_DIR/$BACKUP_NAME"
