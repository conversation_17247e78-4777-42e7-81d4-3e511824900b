# 用户类型判断优化

## 问题描述
个人资料页面的用户类型显示存在问题：
- 没有区分管理员、VIP用户、普通用户
- 所有用户都显示"升级VIP"按钮，不够智能
- VIP用户应该显示"续费VIP"，管理员不应该显示操作按钮

## 解决方案

### 1. 用户类型分类
- **管理员**：`isAdmin: true`
- **VIP用户**：`isVip: true, isAdmin: false`
- **普通用户**：`isVip: false, isAdmin: false`

### 2. 显示逻辑优化

#### 用户类型文本显示
```javascript
getUserTypeText() {
  if (this.userInfo.isAdmin) {
    return '管理员'
  } else if (this.userInfo.isVip) {
    return 'VIP用户'
  } else {
    return '普通用户'
  }
}
```

#### 操作按钮显示逻辑
- **管理员**：不显示任何操作按钮
- **VIP用户**：显示"续费VIP"按钮（绿色）
- **普通用户**：显示"升级VIP"按钮（橙色）

### 3. 样式优化

#### 用户类型文本样式
- 普通用户：灰色文字
- VIP用户：橙色文字，加粗
- 管理员：紫色文字，加粗

#### 按钮样式
- 升级VIP按钮：橙色背景 `#ff6b35`
- 续费VIP按钮：绿色背景 `#4caf50`

### 4. 功能实现

#### 按钮点击处理
```javascript
handleUserAction() {
  if (this.userInfo.isVip) {
    this.renewVip()  // 跳转续费页面
  } else {
    this.upgradeVip()  // 跳转升级页面
  }
}
```

#### 页面跳转
- 升级VIP：`/pages/vip/upgrade`
- 续费VIP：`/pages/vip/upgrade?type=renew`

### 5. 数据结构更新

#### UserManager.getDefaultUserInfo()
```javascript
{
  id: 1,
  username: 'testuser',
  nickname: '测试用户',
  email: '<EMAIL>',
  avatar: '/static/images/default-avatar.png',
  isVip: false,
  isAdmin: false,  // 新增字段
  dailyQuota: 10,
  todayUsed: 0,
  totalUsed: 0
}
```

### 6. 测试功能

#### 开发环境测试按钮
- 仅在H5环境显示
- 可以循环切换：普通用户 → VIP用户 → 管理员 → 普通用户
- 方便开发时测试不同用户类型的显示效果

#### 测试流程
1. 打开个人资料页面
2. 点击"切换用户类型（测试）"按钮
3. 观察用户类型文本和按钮的变化
4. 验证不同状态下的显示是否正确

### 7. 后端兼容性

#### 用户实体字段
- `is_vip`：VIP状态（0-普通，1-VIP）
- `is_admin`：管理员状态（0-普通，1-管理员）
- `vip_expire_time`：VIP过期时间

#### API响应格式
```json
{
  "id": 1,
  "username": "testuser",
  "nickname": "测试用户",
  "isVip": true,
  "isAdmin": false,
  "vipExpireTime": "2024-12-31T23:59:59"
}
```

## 使用说明

### 普通用户
- 显示"普通用户"文字（灰色）
- 显示"升级VIP"按钮（橙色）
- 点击按钮跳转到VIP升级页面

### VIP用户
- 显示"VIP用户"文字（橙色，加粗）
- 显示"续费VIP"按钮（绿色）
- 点击按钮跳转到VIP续费页面

### 管理员
- 显示"管理员"文字（紫色，加粗）
- 不显示任何操作按钮
- 管理员拥有所有权限，无需升级

## 注意事项

1. **权限优先级**：管理员权限高于VIP权限
2. **数据一致性**：确保前后端用户状态同步
3. **测试按钮**：生产环境应移除测试功能
4. **用户体验**：不同用户类型有明确的视觉区分

## 后续优化建议

1. **VIP过期提醒**：VIP用户接近过期时显示提醒
2. **权限说明**：添加不同用户类型的权限说明页面
3. **升级引导**：为普通用户提供VIP功能介绍
4. **管理功能**：为管理员添加专门的管理入口
