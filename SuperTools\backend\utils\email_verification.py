#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
邮箱验证服务
替换短信验证，使用邮箱发送验证码
"""

import os
import random
import time
import logging
from typing import Dict, Any, Optional
from .mailer import EmailSender

logger = logging.getLogger(__name__)

class EmailVerificationService:
    """邮箱验证服务"""
    
    def __init__(self):
        self.mailer = None
        self._init_mailer()
        
        # 验证码存储（生产环境建议使用Redis）
        self.verification_codes = {}
        
        # 配置
        self.code_expire_minutes = 5  # 验证码有效期（分钟）
        self.max_send_per_day = 10    # 每天最大发送次数
        self.send_interval = 60       # 发送间隔（秒）
    
    def _init_mailer(self):
        """初始化邮件发送器"""
        try:
            # 从环境变量获取邮箱配置
            email = os.environ.get('QQ_EMAIL')
            auth_code = os.environ.get('QQ_AUTH_CODE')
            
            if email and auth_code and email != '<EMAIL>':
                self.mailer = EmailSender(email, auth_code)
                logger.info("邮箱验证服务初始化成功")
            else:
                logger.error("邮箱配置不完整，邮件发送将失败")
                self.mailer = None
                
        except Exception as e:
            logger.error(f"邮箱验证服务初始化失败: {e}")
    
    def is_service_available(self) -> bool:
        """检查服务是否可用"""
        return self.mailer is not None
    
    def generate_verification_code(self) -> str:
        """生成6位验证码"""
        return str(random.randint(100000, 999999))
    
    def _get_code_key(self, email: str, purpose: str) -> str:
        """获取验证码存储键"""
        return f"{purpose}_{email}"
    
    def _check_send_frequency(self, email: str, purpose: str) -> Dict[str, Any]:
        """检查发送频率"""
        key = self._get_code_key(email, purpose)
        current_time = time.time()
        
        # 检查是否在发送间隔内
        if key in self.verification_codes:
            last_send_time = self.verification_codes[key].get('send_time', 0)
            if current_time - last_send_time < self.send_interval:
                remaining = int(self.send_interval - (current_time - last_send_time))
                return {
                    'allowed': False,
                    'message': f'请等待 {remaining} 秒后再试'
                }
        
        return {'allowed': True}
    
    def send_verification_code(self, email: str, purpose: str = 'login') -> Dict[str, Any]:
        """
        发送验证码
        
        Args:
            email: 邮箱地址
            purpose: 用途 (login, register, reset_password)
            
        Returns:
            Dict: 发送结果
        """
        try:
            # 检查发送频率
            frequency_check = self._check_send_frequency(email, purpose)
            if not frequency_check['allowed']:
                return {
                    'success': False,
                    'message': frequency_check['message']
                }
            
            # 生成验证码
            code = self.generate_verification_code()
            current_time = time.time()
            
            # 存储验证码
            key = self._get_code_key(email, purpose)
            self.verification_codes[key] = {
                'code': code,
                'send_time': current_time,
                'expire_time': current_time + (self.code_expire_minutes * 60),
                'attempts': 0
            }
            
            # 发送邮件
            if not self.mailer:
                return {
                    'success': False,
                    'message': '邮箱服务未配置，请联系管理员'
                }

            success = self._send_email(email, code, purpose)
            if success:
                return {
                    'success': True,
                    'message': '验证码已发送到您的邮箱，请查收',
                    'expire_minutes': self.code_expire_minutes
                }
            else:
                return {
                    'success': False,
                    'message': '邮件发送失败，请稍后重试'
                }
                
        except Exception as e:
            logger.error(f"发送验证码失败: {e}")
            return {
                'success': False,
                'message': '发送失败，请稍后重试'
            }
    
    def _send_email(self, email: str, code: str, purpose: str) -> bool:
        """发送验证码邮件"""
        try:
            # 根据用途设置邮件内容
            purpose_map = {
                'login': '登录',
                'register': '注册',
                'reset_password': '重置密码'
            }

            purpose_text = purpose_map.get(purpose, '验证')
            subject = f"SuperTools {purpose_text}验证码"

            # 美化的文本邮件内容
            text_content = f"""
🚀 SuperTools {purpose_text}验证码

您好！{email}
您正在进行【{purpose_text}】操作，请使用以下验证码：{code}
⏰ 验证码有效期：{self.code_expire_minutes} 分钟
🔒 请妥善保管，及时使用

💡 温馨提示：
• 如果这不是您的操作，请忽略此邮件
• 请勿将验证码告诉他人
• 验证码仅用于本次操作
• 此邮件发送至：{email}

SuperTools 团队
此邮件由系统自动发送，请勿回复
"""

            return self.mailer.send_mail(
                receiver_email=email,
                subject=subject,
                body=text_content
            )
            
        except Exception as e:
            logger.error(f"发送验证码邮件失败: {e}")
            return False
    
    def verify_code(self, email: str, code: str, purpose: str = 'login') -> Dict[str, Any]:
        """
        验证验证码
        
        Args:
            email: 邮箱地址
            code: 验证码
            purpose: 用途
            
        Returns:
            Dict: 验证结果
        """
        try:
            key = self._get_code_key(email, purpose)
            
            if key not in self.verification_codes:
                return {
                    'success': False,
                    'message': '验证码不存在或已过期'
                }
            
            code_info = self.verification_codes[key]
            current_time = time.time()
            
            # 检查是否过期
            if current_time > code_info['expire_time']:
                del self.verification_codes[key]
                return {
                    'success': False,
                    'message': '验证码已过期，请重新获取'
                }
            
            # 检查验证码
            if code_info['code'] != code:
                code_info['attempts'] += 1
                if code_info['attempts'] >= 3:
                    del self.verification_codes[key]
                    return {
                        'success': False,
                        'message': '验证码错误次数过多，请重新获取'
                    }
                return {
                    'success': False,
                    'message': '验证码错误'
                }
            
            # 验证成功，删除验证码
            del self.verification_codes[key]
            return {
                'success': True,
                'message': '验证成功'
            }
            
        except Exception as e:
            logger.error(f"验证码验证失败: {e}")
            return {
                'success': False,
                'message': '验证失败，请稍后重试'
            }
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            'service_available': self.is_service_available(),
            'config_complete': self.mailer is not None,
            'mock_mode': False,  # 已禁用模拟模式
            'real_email_mode': self.mailer is not None,
            'active_codes': len(self.verification_codes),
            'code_expire_minutes': self.code_expire_minutes,
            'max_send_per_day': self.max_send_per_day,
            'send_interval_seconds': self.send_interval
        }

# 全局实例
email_verification_service = EmailVerificationService()
